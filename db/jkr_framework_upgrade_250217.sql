/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2025年2月17日09:17:30
  说明：新增第三方授权表
------------------------------------------------------------------------------*/
DROP TABLE IF EXISTS `sys_auth_user`;
CREATE TABLE `sys_auth_user` (
  `auth_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '授权ID',
  `uuid` varchar(500) COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台用户唯一ID',
  `user_id` bigint(20) NOT NULL COMMENT '系统用户ID',
  `login_name` varchar(30) COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录账号',
  `user_name` varchar(30) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户昵称',
  `avatar` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像地址',
  `email` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户邮箱',
  `source` varchar(255) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户来源',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`auth_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='第三方授权表';

/*-----------------------------------------------------------------------------
  编写人:lxy
  时间：2025年2月17日09:19:35
  说明：增加微信小程序对接参数
------------------------------------------------------------------------------*/
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7296771628862537728, '微信-appid', 'wechat.appid', '', 'Y', 'admin', '2025-02-16 14:05:23', 'admin', '2025-02-16 14:30:32', '小程序ID');
INSERT INTO `sys_config` (`config_id`, `config_name`, `config_key`, `config_value`, `config_type`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (7296771738010910720, '微信-secret', 'wechat.secret', '', 'Y', 'admin', '2025-02-16 14:05:49', 'admin', '2025-02-16 14:30:34', '小程序密钥');
