/*-----------------------------------------------------------------------------
  编写人: lty
  时间：2025-02-20
  说明：增加markdown文档管理功能及相关菜单
------------------------------------------------------------------------------*/
CREATE TABLE `sys_document_dir` (
            `dir_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '目录id',
            `parent_id` BIGINT(20) DEFAULT '0' COMMENT '父级目录id',
            `ancestors` VARCHAR(50) DEFAULT '' COMMENT '祖级列表',
            `dir_name` VARCHAR(30) DEFAULT '' COMMENT '目录名称',
            `order_num` INT(4) DEFAULT '0' COMMENT '显示顺序',
            `status` CHAR(1) DEFAULT '0' COMMENT '目录状态（0正常 1停用）',
            `dir_type` VARCHAR(1) DEFAULT NULL COMMENT '目录类型：1.接口文档目录；2：帮助文档目录',


            `create_by` VARCHAR(64) DEFAULT '' COMMENT '创建者',
            `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_by` VARCHAR(64) DEFAULT '' COMMENT '更新者',
            `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `del_flag` VARCHAR(100) NOT NULL DEFAULT '1' COMMENT '删除标志（默认为1，表示数据可用，所有值为时间戳的表示数据不可用）',
            `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',

            `tenant_id` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
            PRIMARY KEY (`dir_id`) USING BTREE
) ENGINE=INNODB AUTO_INCREMENT=288 DEFAULT CHARSET=utf8mb4 COMMENT='接口文档目录表';

CREATE TABLE `sys_document_md` (
       `md_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
       `dir_id` BIGINT(20) DEFAULT NULL COMMENT '关联接口文档目录id',
       `render` MEDIUMTEXT,
       `value` TEXT,
       `status` CHAR(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
       `title` VARCHAR(255) DEFAULT NULL COMMENT '标题',


       `create_by` VARCHAR(64) DEFAULT '' COMMENT '创建者',
       `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       `update_by` VARCHAR(64) DEFAULT '' COMMENT '更新者',
       `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       `del_flag` VARCHAR(100) NOT NULL DEFAULT '1' COMMENT '删除标志（默认为1，表示数据可用，所有值为时间戳的表示数据不可用）',
       `remark` VARCHAR(500) DEFAULT NULL COMMENT '备注',

       `tenant_id` BIGINT(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
       PRIMARY KEY (`md_id`)
) ENGINE=INNODB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COMMENT='接口文档内容表';

##  新增文档管理菜单菜单脚本
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES('1121','目录管理','1120','1','document-dir','document/dir/index',NULL,'','1','0','C','0','0','','nested','admin','2025-02-18 16:33:28','admin','2025-02-18 16:34:25','');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES('1120','文档管理','0','15','document',NULL,NULL,'','1','0','M','0','0','','dict','admin','2025-02-18 16:30:05','admin','2025-02-19 17:00:12','');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query`, `route_name`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES('1119','文档内容管理','1120','2','document-md','document/md/index',NULL,'','1','0','C','0','0','','excel','admin','2025-02-18 15:26:45','admin','2025-02-18 16:33:44','');