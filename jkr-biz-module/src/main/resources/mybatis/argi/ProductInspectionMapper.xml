<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.ProductInspectionMapper">

    <resultMap type="com.jkr.project.argi.domain.ProductInspection" id="ProductInspectionResult">
        <result property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="entName" column="ent_name"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="sampleNo" column="sample_no"/>
        <result property="sampleQrcodeUrl" column="sample_qrcode_url"/>
        <result property="recordNo" column="record_no"/>
        <result property="inspectionSituation" column="inspection_situation"/>
        <result property="inspectionItem" column="inspection_item"/>
        <result property="inspectionDate" column="inspection_date"/>
        <result property="inspectionStandard" column="inspection_standard"/>
        <result property="inspectionResult" column="inspection_result"/>
        <result property="inspectionPerson" column="inspection_person"/>
        <result property="inspectionExplain" column="inspection_explain"/>
        <result property="inspectionCompany" column="inspection_company"/>
        <result property="inspectionCompanyId" column="inspection_company_id"/>
        <result property="inspectionValue" column="inspection_value"/>
        <result property="acceptableRange" column="acceptable_range"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectProductInspectionVo">
        select id,
               ent_id,
               ent_name,
               product_id,
               product_name,
               sample_no,
               sample_qrcode_url,
               record_no,
               inspection_situation,
               inspection_item,
               inspection_date,
               inspection_standard,
               inspection_result,
               inspection_person,
               inspection_explain,
               inspection_company,
               inspection_company_id,
               inspection_value,
               acceptable_range,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from bas_product_inspection
    </sql>

    <select id="selectProductInspectionList" parameterType="com.jkr.project.argi.domain.ProductInspection"
            resultMap="ProductInspectionResult">
        <include refid="selectProductInspectionVo"/>
        <where>
            <if test="entId != null and entId != ''">
                AND ent_id=#{entId}
            </if>
            <if test="entName != null and entName != ''">
                AND ent_name LIKE concat('%',#{entName},'%')
            </if>
            <if test="inspectionCompanyId != null and inspectionCompanyId != ''">
                AND inspection_company_id=#{inspectionCompanyId}
            </if>
            <if test="inspectionSituation != null and inspectionSituation != ''">
                AND inspection_situation=#{inspectionSituation}
            </if>
            <if test="inspectionResult != null and inspectionResult != ''">
                AND inspection_result=#{inspectionResult}
            </if>
            <if test="productId != null and productId != ''">
                AND product_id=#{productId}
            </if>
            <if test="productName != null and productName != ''">
                AND product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="sampleNo != null and sampleNo != ''">
                AND sample_no=#{sampleNo}
            </if>
            <if test="startDate != null">
                AND DATE(inspection_date) <![CDATA[ >= ]]> DATE(#{startDate})
            </if>
            <if test="endDate != null">
                AND DATE(inspection_date) <![CDATA[ <= ]]> DATE(#{endDate})
            </if>
            and del_flag = '1'
        </where>
        <choose>
            <when test="params.orderBy != null and params.orderBy != '' and params.orderBy == 1">
                ORDER BY inspection_date desc
            </when>
            <when test="params.orderBy != null and params.orderBy != '' and params.orderBy == 2">
                ORDER BY inspection_date asc
            </when>
            <when test="params.orderBy != null and params.orderBy != '' and params.orderBy == 3">
                ORDER BY create_time desc
            </when>
            <when test="params.orderBy != null and params.orderBy != '' and params.orderBy == 4">
                ORDER BY create_time asc
            </when>
            <when test="params.orderBy != null and params.orderBy != '' and params.orderBy == 5">
                ORDER BY inspection_result desc
            </when>
            <when test="params.orderBy != null and params.orderBy != '' and params.orderBy == 6">
                ORDER BY inspection_result asc
            </when>
            <otherwise>
                ORDER BY update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectProductInspectionById" parameterType="Long" resultMap="ProductInspectionResult">
        <include refid="selectProductInspectionVo"/>
        where id = #{id}
    </select>

    <insert id="insertProductInspection" parameterType="com.jkr.project.argi.domain.ProductInspection">
        insert into bas_product_inspection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="entId != null">ent_id,</if>
            <if test="entName != null">ent_name,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="sampleNo != null">sample_no,</if>
            <if test="sampleQrcodeUrl != null">sample_qrcode_url,</if>
            <if test="recordNo != null">record_no,</if>
            <if test="inspectionSituation != null">inspection_situation,</if>
            <if test="inspectionItem != null">inspection_item,</if>
            <if test="inspectionDate != null">inspection_date,</if>
            <if test="inspectionStandard != null">inspection_standard,</if>
            <if test="inspectionResult != null">inspection_result,</if>
            <if test="inspectionPerson != null">inspection_person,</if>
            <if test="inspectionExplain != null">inspection_explain,</if>
            <if test="inspectionCompany != null">inspection_company,</if>
            <if test="inspectionCompanyId != null">inspection_company_id,</if>
            <if test="inspectionValue != null">inspection_value,</if>
            <if test="acceptableRange != null">acceptable_range,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="entId != null">#{entId},</if>
            <if test="entName != null">#{entName},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="sampleNo != null">#{sampleNo},</if>
            <if test="sampleQrcodeUrl != null">#{sampleQrcodeUrl},</if>
            <if test="recordNo != null">#{recordNo},</if>
            <if test="inspectionSituation != null">#{inspectionSituation},</if>
            <if test="inspectionItem != null">#{inspectionItem},</if>
            <if test="inspectionDate != null">#{inspectionDate},</if>
            <if test="inspectionStandard != null">#{inspectionStandard},</if>
            <if test="inspectionResult != null">#{inspectionResult},</if>
            <if test="inspectionPerson != null">#{inspectionPerson},</if>
            <if test="inspectionExplain != null">#{inspectionExplain},</if>
            <if test="inspectionCompany != null">#{inspectionCompany},</if>
            <if test="inspectionCompanyId != null">#{inspectionCompanyId},</if>
            <if test="inspectionValue != null">#{inspectionValue},</if>
            <if test="acceptableRange != null">#{acceptableRange},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateProductInspection" parameterType="com.jkr.project.argi.domain.ProductInspection">
        update bas_product_inspection
        <trim prefix="SET" suffixOverrides=",">
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="entName != null">ent_name = #{entName},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="sampleNo != null">sample_no = #{sampleNo},</if>
            <if test="sampleQrcodeUrl != null">sample_qrcode_url = #{sampleQrcodeUrl},</if>
            <if test="recordNo != null">record_no = #{recordNo},</if>
            <if test="inspectionSituation != null">inspection_situation = #{inspectionSituation},</if>
            <if test="inspectionItem != null">inspection_item = #{inspectionItem},</if>
            <if test="inspectionDate != null">inspection_date = #{inspectionDate},</if>
            <if test="inspectionStandard != null">inspection_standard = #{inspectionStandard},</if>
            <if test="inspectionResult != null">inspection_result = #{inspectionResult},</if>
            <if test="inspectionPerson != null">inspection_person = #{inspectionPerson},</if>
            <if test="inspectionExplain != null">inspection_explain = #{inspectionExplain},</if>
            <if test="inspectionCompany != null">inspection_company = #{inspectionCompany},</if>
            <if test="inspectionCompanyId != null">inspection_company_id = #{inspectionCompanyId},</if>
            <if test="inspectionValue != null">inspection_value = #{inspectionValue},</if>
            <if test="acceptableRange != null">acceptable_range = #{acceptableRange},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_product_inspection set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_product_inspection
        set del_flag = REPLACE(unix_timestamp(current_timestamp(3)), '.', '')
        where id = #{id}
    </update>

    <delete id="deleteProductInspectionById" parameterType="Long">
        delete
        from bas_product_inspection
        where id = #{id}
    </delete>

    <delete id="deleteProductInspectionByIds" parameterType="String">
        delete from bas_product_inspection where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getNewest" resultMap="ProductInspectionResult">
        <include refid="selectProductInspectionVo"/>
        <where>
            del_flag = '1'
            <if test="entId != null and entId != ''">
                AND ent_id = #{entId}
            </if>
            <if test="productId != null and productId != ''">
                AND product_id = #{productId}
            </if>
            <if test="inspectionSituation != null and inspectionSituation != ''">
                AND inspection_situation = #{inspectionSituation}
            </if>
        </where>
        ORDER BY inspection_date desc,update_time DESC LIMIT 1
    </select>

    <insert id="insertBatch">
        INSERT INTO bas_product_inspection(
            id,
            ent_id,
            ent_name,
            product_id,
            product_name,
            sample_no,
            sample_qrcode_url,
            record_no,
            inspection_situation,
            inspection_item,
            inspection_date,
            inspection_standard,
            inspection_result,
            inspection_person,
            inspection_explain,
            inspection_company,
            inspection_company_id,
            inspection_value,
            acceptable_range,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.entId},
            #{item.entName},
            #{item.productId},
            #{item.productName},
            #{item.sampleNo},
            #{item.sampleQrcodeUrl},
            #{item.recordNo},
            #{item.inspectionSituation},
            #{item.inspectionItem},
            #{item.inspectionDate},
            #{item.inspectionStandard},
            #{item.inspectionResult},
            #{item.inspectionPerson},
            #{item.inspectionExplain},
            #{item.inspectionCompany},
            #{item.inspectionCompanyId},
            #{item.inspectionValue},
            #{item.acceptableRange},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            '1'
            )
        </foreach>
    </insert>
</mapper>
