<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateConfigTemplateMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateConfigTemplate" id="CertificateConfigTemplateResult">
        <result property="id"    column="id"    />
        <result property="certificateConfigId"    column="certificate_config_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="templateId"    column="template_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <association property="deviceParameter" javaType="com.jkr.project.argi.domain.DeviceParameter">
            <result property="name" column="deviceParameterName"/>
            <result property="type" column="deviceParameterType"/>
            <result property="command" column="deviceParameterCommand"/>
            <result property="imageUrl" column="deviceParameterImageUrl"/>
        </association>

        <association property="certificateTemplate" javaType="com.jkr.project.argi.domain.CertificateTemplate">
            <result property="code" column="certificateTemplateCode"/>
            <result property="name" column="certificateTemplateName"/>
            <result property="imageUrl" column="certificateTemplateImageUrl"/>
        </association>
    </resultMap>

    <sql id="selectCertificateConfigTemplateVo">
        select id, certificate_config_id, device_id, template_id, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate_config_template
    </sql>

    <sql id="Base_Column_List">
        <trim>
        ${alias}.id, ${alias}.certificate_config_id, ${alias}.device_id, ${alias}.template_id, ${alias}.del_flag,
        ${alias}.create_by, ${alias}.create_time, ${alias}.update_by, ${alias}.update_time, ${alias}.remark
        </trim>
    </sql>

    <select id="selectCertificateConfigTemplateList" parameterType="com.jkr.project.argi.domain.CertificateConfigTemplate" resultMap="CertificateConfigTemplateResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,bdp.name AS deviceParameterName,
        bdp.type AS deviceParameterType,
        bdp.command AS deviceParameterCommand,
        bdp.image_url AS deviceParameterImageUrl,
        bct.code AS certificateTemplateCode,
        bct.name AS certificateTemplateName,
        bct.image_url AS certificateTemplateImageUrl
        from bas_certificate_config_template a
        left join bas_device_parameter bdp on bdp.id=a.device_id
        left join bas_certificate_template bct on bct.id=a.template_id
        <where>
            <if test="certificateConfigId != null  and certificateConfigId != ''"> and certificate_config_id = #{certificateConfigId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            and a.del_flag = '1'
        </where>
        order by a.update_time desc
    </select>

    <select id="selectCertificateConfigTemplateById" parameterType="Long" resultMap="CertificateConfigTemplateResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,bdp.name AS deviceParameterName,
        bdp.type AS deviceParameterType,
        bdp.command AS deviceParameterCommand,
        bdp.image_url AS deviceParameterImageUrl,
        bct.code AS certificateTemplateCode,
        bct.name AS certificateTemplateName,
        bct.image_url AS certificateTemplateImageUrl
        from bas_certificate_config_template a
        left join bas_device_parameter bdp on bdp.id=a.device_id
        left join bas_certificate_template bct on bct.id=a.template_id
        where a.id = #{id}
    </select>

    <insert id="insertCertificateConfigTemplate" parameterType="com.jkr.project.argi.domain.CertificateConfigTemplate">
        insert into bas_certificate_config_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="certificateConfigId != null">certificate_config_id,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="templateId != null">template_id,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="certificateConfigId != null">#{certificateConfigId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="templateId != null">#{templateId},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateConfigTemplate" parameterType="com.jkr.project.argi.domain.CertificateConfigTemplate">
        update bas_certificate_config_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateConfigId != null">certificate_config_id = #{certificateConfigId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_config_template set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_config_template set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateConfigTemplateById" parameterType="Long">
        delete from bas_certificate_config_template where id = #{id}
    </delete>

    <delete id="deleteCertificateConfigTemplateByIds" parameterType="String">
        delete from bas_certificate_config_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBatch">
        INSERT INTO bas_certificate_config_template(
            id,
            certificate_config_id,
            device_id,
            template_id,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id},
            #{item.certificateConfigId},
            #{item.deviceId},
            #{item.templateId},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            '1'
        )
        </foreach>
    </insert>

    <update id="deleteByCertificateConfigId">
        UPDATE bas_certificate_config_template SET
            del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','')
        WHERE certificate_config_id = #{certificateConfigId}
    </update>
</mapper>
