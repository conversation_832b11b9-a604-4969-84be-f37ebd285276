<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateTemplateMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateTemplate" id="CertificateTemplateResult">
            <result property="id"    column="id"    />
            <result property="code"    column="code"    />
            <result property="name"    column="name"    />
            <result property="imageUrl"    column="image_url"    />
            <result property="showVillage"    column="show_village"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCertificateTemplateVo">
        select id, code, name, image_url, show_village, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate_template
    </sql>

    <select id="selectCertificateTemplateList" parameterType="com.jkr.project.argi.domain.CertificateTemplate" resultMap="CertificateTemplateResult">
        <include refid="selectCertificateTemplateVo"/>
        <where>
            <if test="code != null "> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="showVillage != null  and showVillage != ''"> and show_village = #{showVillage}</if>
            and del_flag = '1'
        </where>
        order by update_time desc
    </select>

    <select id="selectCertificateTemplateById" parameterType="Long" resultMap="CertificateTemplateResult">
            <include refid="selectCertificateTemplateVo"/>
            where id = #{id}
    </select>

    <insert id="insertCertificateTemplate" parameterType="com.jkr.project.argi.domain.CertificateTemplate">
        insert into bas_certificate_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="showVillage != null">show_village,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="showVillage != null">#{showVillage},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateTemplate" parameterType="com.jkr.project.argi.domain.CertificateTemplate">
        update bas_certificate_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="showVillage != null">show_village = #{showVillage},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_template set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_template set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateTemplateById" parameterType="Long">
        delete from bas_certificate_template where id = #{id}
    </delete>

    <delete id="deleteCertificateTemplateByIds" parameterType="String">
        delete from bas_certificate_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
