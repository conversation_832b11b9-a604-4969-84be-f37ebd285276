<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateMapper">

    <resultMap type="com.jkr.project.argi.domain.Certificate" id="CertificateResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="userId"    column="user_id"    />
        <result property="dataScope"    column="data_scope"    />
        <result property="no"    column="no"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productMixFlag"    column="product_mix_flag"    />
        <result property="productIntroduction"    column="product_introduction"    />
        <result property="productProvince"    column="product_province"    />
        <result property="productCity"    column="product_city"    />
        <result property="productCounty"    column="product_county"    />
        <result property="productAddress"    column="product_address"    />
        <result property="productDetail"    column="product_detail"    />
        <result property="productSortCode"    column="product_sort_code"    />
        <result property="productSortName"    column="product_sort_name"    />
        <result property="productCertificationCode"    column="product_certification_code"    />
        <result property="productCertificationName"    column="product_certification_name"    />
        <result property="productNum"    column="product_num"    />
        <result property="productUnitCode"    column="product_unit_code"    />
        <result property="productUnitName"    column="product_unit_name"    />
        <result property="entId"    column="ent_id"    />
        <result property="entName"    column="ent_name"    />
        <result property="entBusinessType"    column="ent_business_type"    />
        <result property="entType"    column="ent_type"    />
        <result property="entMainType"    column="ent_main_type"    />
        <result property="entFarmType"    column="ent_farm_type"    />
        <result property="entCardNo"    column="ent_card_no"    />
        <result property="entLegalPerson"    column="ent_legal_person"    />
        <result property="entProvince"    column="ent_province"    />
        <result property="entCity"    column="ent_city"    />
        <result property="entCounty"    column="ent_county"    />
        <result property="entAddress"    column="ent_address"    />
        <result property="entDetail"    column="ent_detail"    />
        <result property="entSocialCode"    column="ent_social_code"    />
        <result property="entContactsPhone"    column="ent_contacts_phone"    />
        <result property="entAutograph"    column="ent_autograph"    />
        <result property="entCompanyIntroduction"    column="ent_company_introduction"    />
        <result property="entHonor"    column="ent_honor"    />
        <result property="productionDate"    column="production_date"    />
        <result property="printCount"    column="print_count"    />
        <result property="blockChainId"    column="block_chain_id"    />
        <result property="beginSerialNumber"    column="begin_serial_number"    />
        <result property="endSerialNumber"    column="end_serial_number"    />
        <result property="inspectionSituation"    column="inspection_situation"    />
        <result property="sampleNo"    column="sample_no"    />
        <result property="productInspectionId"    column="product_inspection_id"    />
        <result property="reBuyVisible"    column="re_buy_visible"    />
        <result property="electricFlag"    column="electric_flag"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCertificateVo">
        select id, batch_no, user_id, data_scope, no, product_id, product_name, product_mix_flag, product_introduction, product_province, product_city, product_county, product_address, product_detail, product_sort_code, product_sort_name, product_certification_code, product_certification_name, product_num, product_unit_code, product_unit_name, ent_id, ent_name, ent_business_type, ent_type, ent_main_type, ent_farm_type, ent_card_no, ent_legal_person, ent_province, ent_city, ent_county, ent_address, ent_detail, ent_social_code, ent_contacts_phone, ent_autograph, ent_company_introduction, ent_honor, production_date, print_count, block_chain_id, begin_serial_number, end_serial_number, inspection_situation, sample_no, product_inspection_id, re_buy_visible, electric_flag, certificate_type, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate
    </sql>

    <sql id="Base_Column_List">
        <trim>
            ${alias}.id, ${alias}.batch_no, ${alias}.user_id, ${alias}.data_scope, ${alias}.no,
            ${alias}.product_id, ${alias}.product_name, ${alias}.product_mix_flag, ${alias}.product_introduction, ${alias}.product_province,
            ${alias}.product_city, ${alias}.product_county, ${alias}.product_address, ${alias}.product_detail, ${alias}.product_sort_code,
            ${alias}.product_sort_name, ${alias}.product_certification_code, ${alias}.product_certification_name, ${alias}.product_num, ${alias}.product_unit_code,
            ${alias}.product_unit_name, ${alias}.ent_id, ${alias}.ent_name, ${alias}.ent_business_type, ${alias}.ent_type,
            ${alias}.ent_main_type, ${alias}.ent_farm_type, ${alias}.ent_card_no, ${alias}.ent_legal_person, ${alias}.ent_province,
            ${alias}.ent_city, ${alias}.ent_county, ${alias}.ent_address, ${alias}.ent_detail, ${alias}.ent_social_code,
            ${alias}.ent_contacts_phone, ${alias}.ent_autograph, ${alias}.ent_company_introduction, ${alias}.ent_honor, ${alias}.production_date,
            ${alias}.print_count, ${alias}.block_chain_id, ${alias}.begin_serial_number, ${alias}.end_serial_number, ${alias}.inspection_situation,
            ${alias}.sample_no, ${alias}.product_inspection_id, ${alias}.re_buy_visible, ${alias}.electric_flag, ${alias}.certificate_type,
            ${alias}.del_flag, ${alias}.create_by, ${alias}.create_time, ${alias}.update_by, ${alias}.update_time, ${alias}.remark
        </trim>
    </sql>

    <sql id="certificateJoins">
        INNER JOIN bas_product p ON p.id = a.product_id
    </sql>
    <sql id="certificateNoJoins">
        INNER JOIN bas_certificate_no n ON n.certificate_id = a.id
    </sql>
    <sql id="certificateEntJoins">
        INNER JOIN bas_ent e ON a.ent_id = e.id
    </sql>

    <select id="selectCertificateList" parameterType="com.jkr.project.argi.domain.Certificate" resultMap="CertificateResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        from bas_certificate a
        <include refid="certificateEntJoins"/>
        <if test="batchNo != null and batchNo != ''">
            <include refid="certificateNoJoins"/>
        </if>
        <where>
            <if test="batchNo != null and batchNo != ''">
                AND n.full_number LIKE concat('%',#{batchNo},'')
            </if>
            <if test="productName != null and productName != ''">
                <!--需求变更改为模糊查询 lxy 2021年5月25日9:40:13-->
                AND a.product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="productId != null and productId != ''">
                AND a.product_id = #{productId}
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="printCount != null and printCount != ''">
                AND a.print_count = #{printCount}
            </if>
            <!-- <if test="beginCreateDate != null and endCreateDate != null and beginCreateDate != '' and endCreateDate != ''">
                AND a.create_time BETWEEN #{beginCreateDate} AND #{endCreateDate}
            </if> -->
            <if test="beginCreateDate != null and beginCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_time,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endCreateDate != null and endCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_time,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
            </if>

            <if test="time != null and time != ''">
                AND DATE_FORMAT(a.create_time,'%Y-%m-%d') = #{time}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="entBusinessType != null and entBusinessType != ''">
                AND a.ent_business_type = #{entBusinessType}
            </if>
            <if test="entType != null and entType != ''">
                AND a.ent_type = #{entType}
            </if>
            <if test="electricFlag != null and electricFlag != ''">
                AND a.electric_flag = #{electricFlag}
            </if>
            <if test="certificateType != null and certificateType != ''">
                AND a.certificate_type = #{certificateType}
            </if>
            <if test="entName != null and entName != ''">
                AND a.ent_name like "%"#{entName}"%"
            </if>
            <if test="entCode != null and entCode != ''">
                AND a.ent_county LIKE #{entCode}"%"
            </if>
            <if test="params.sqlMap.areaWhere!='' and params.sqlMap.areaWhere!=null">
                ${params.sqlMap.areaWhere}
            </if>
            <if test="ent != null">
                <if test="ent.frozenFlag != null and ent.frozenFlag != ''">
                    AND e.frozen_flag = #{ent.frozenFlag}
                </if>
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="beginPrintCount != null">
                AND a.print_count <![CDATA[ >= ]]> #{beginPrintCount}
            </if>
            <if test="endPrintCount != null">
                AND a.print_count <![CDATA[ <= ]]> #{endPrintCount}
            </if>
            <if test="productMixFlag != null">
                and a.product_mix_flag = #{productMixFlag}
            </if>
            and a.del_flag = '1'
        </where>
        order by a.create_time desc
    </select>

    <select id="selectCertificateById" parameterType="Long" resultMap="CertificateResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        from bas_certificate a
        where a.id = #{id}
    </select>

    <select id="findListForWechat" resultMap="CertificateResult">
        SELECT
        a.id,
        a.product_name,
        a.product_sort_code,
        a.product_sort_name,
        a.product_certification_code,
        a.product_certification_name,
        a.product_num,
        a.product_unit_code,
        a.product_unit_name,
        a.production_date,
        a.print_count,
        a.inspection_situation,
        a.re_buy_visible,
        a.electric_flag,
        a.certificate_type,
        a.create_time
        FROM bas_certificate a
        <where>
            a.del_flag = '1'
            <if test="productName != null and productName != ''">
                <!--需求变更改为模糊查询 lxy 2021年5月25日9:40:13-->
                AND a.product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="productId != null and productId != ''">
                AND a.product_id = #{productId}
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="printCount != null and printCount != ''">
                AND a.print_count = #{printCount}
            </if>
            <if test="beginCreateDate != null and beginCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_time,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endCreateDate != null and endCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_time,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="time != null and time != ''">
                AND DATE_FORMAT(a.create_time,'%Y-%m-%d') = #{time}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="entBusinessType != null and entBusinessType != ''">
                AND a.ent_business_type = #{entBusinessType}
            </if>
            <if test="entType != null and entType != ''">
                AND a.ent_type = #{entType}
            </if>
            <if test="electricFlag != null and electricFlag != ''">
                AND a.electric_flag = #{electricFlag}
            </if>
            <if test="certificateType != null and certificateType != ''">
                AND a.certificate_type = #{certificateType}
            </if>
            <if test="entName != null and entName != ''">
                AND a.ent_name like "%"#{entName}"%"
            </if>
            <if test="entCode != null and entCode != ''">
                AND a.ent_county LIKE #{entCode}"%"
            </if>
            <if test="params.sqlMap.areaWhere!='' and params.sqlMap.areaWhere!=null">
                ${params.sqlMap.areaWhere}
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
        </where>
        ORDER BY a.update_time DESC
    </select>

    <insert id="insertCertificate" parameterType="com.jkr.project.argi.domain.Certificate">
        insert into bas_certificate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="dataScope != null">data_scope,</if>
            <if test="no != null">no,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="productMixFlag != null">product_mix_flag,</if>
            <if test="productIntroduction != null">product_introduction,</if>
            <if test="productProvince != null">product_province,</if>
            <if test="productCity != null">product_city,</if>
            <if test="productCounty != null">product_county,</if>
            <if test="productAddress != null">product_address,</if>
            <if test="productDetail != null">product_detail,</if>
            <if test="productSortCode != null">product_sort_code,</if>
            <if test="productSortName != null">product_sort_name,</if>
            <if test="productCertificationCode != null">product_certification_code,</if>
            <if test="productCertificationName != null">product_certification_name,</if>
            <if test="productNum != null">product_num,</if>
            <if test="productUnitCode != null">product_unit_code,</if>
            <if test="productUnitName != null">product_unit_name,</if>
            <if test="entId != null">ent_id,</if>
            <if test="entName != null">ent_name,</if>
            <if test="entBusinessType != null">ent_business_type,</if>
            <if test="entType != null">ent_type,</if>
            <if test="entMainType != null">ent_main_type,</if>
            <if test="entFarmType != null">ent_farm_type,</if>
            <if test="entCardNo != null">ent_card_no,</if>
            <if test="entLegalPerson != null">ent_legal_person,</if>
            <if test="entProvince != null">ent_province,</if>
            <if test="entCity != null">ent_city,</if>
            <if test="entCounty != null">ent_county,</if>
            <if test="entAddress != null">ent_address,</if>
            <if test="entDetail != null">ent_detail,</if>
            <if test="entSocialCode != null">ent_social_code,</if>
            <if test="entContactsPhone != null">ent_contacts_phone,</if>
            <if test="entAutograph != null">ent_autograph,</if>
            <if test="entCompanyIntroduction != null">ent_company_introduction,</if>
            <if test="entHonor != null">ent_honor,</if>
            <if test="productionDate != null">production_date,</if>
            <if test="printCount != null">print_count,</if>
            <if test="blockChainId != null">block_chain_id,</if>
            <if test="beginSerialNumber != null">begin_serial_number,</if>
            <if test="endSerialNumber != null">end_serial_number,</if>
            <if test="inspectionSituation != null">inspection_situation,</if>
            <if test="sampleNo != null">sample_no,</if>
            <if test="productInspectionId != null">product_inspection_id,</if>
            <if test="reBuyVisible != null">re_buy_visible,</if>
            <if test="electricFlag != null">electric_flag,</if>
            <if test="certificateType != null">certificate_type,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="dataScope != null">#{dataScope},</if>
            <if test="no != null">#{no},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productMixFlag != null">#{productMixFlag},</if>
            <if test="productIntroduction != null">#{productIntroduction},</if>
            <if test="productProvince != null">#{productProvince},</if>
            <if test="productCity != null">#{productCity},</if>
            <if test="productCounty != null">#{productCounty},</if>
            <if test="productAddress != null">#{productAddress},</if>
            <if test="productDetail != null">#{productDetail},</if>
            <if test="productSortCode != null">#{productSortCode},</if>
            <if test="productSortName != null">#{productSortName},</if>
            <if test="productCertificationCode != null">#{productCertificationCode},</if>
            <if test="productCertificationName != null">#{productCertificationName},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="productUnitCode != null">#{productUnitCode},</if>
            <if test="productUnitName != null">#{productUnitName},</if>
            <if test="entId != null">#{entId},</if>
            <if test="entName != null">#{entName},</if>
            <if test="entBusinessType != null">#{entBusinessType},</if>
            <if test="entType != null">#{entType},</if>
            <if test="entMainType != null">#{entMainType},</if>
            <if test="entFarmType != null">#{entFarmType},</if>
            <if test="entCardNo != null">#{entCardNo},</if>
            <if test="entLegalPerson != null">#{entLegalPerson},</if>
            <if test="entProvince != null">#{entProvince},</if>
            <if test="entCity != null">#{entCity},</if>
            <if test="entCounty != null">#{entCounty},</if>
            <if test="entAddress != null">#{entAddress},</if>
            <if test="entDetail != null">#{entDetail},</if>
            <if test="entSocialCode != null">#{entSocialCode},</if>
            <if test="entContactsPhone != null">#{entContactsPhone},</if>
            <if test="entAutograph != null">#{entAutograph},</if>
            <if test="entCompanyIntroduction != null">#{entCompanyIntroduction},</if>
            <if test="entHonor != null">#{entHonor},</if>
            <if test="productionDate != null">#{productionDate},</if>
            <if test="printCount != null">#{printCount},</if>
            <if test="blockChainId != null">#{blockChainId},</if>
            <if test="beginSerialNumber != null">#{beginSerialNumber},</if>
            <if test="endSerialNumber != null">#{endSerialNumber},</if>
            <if test="inspectionSituation != null">#{inspectionSituation},</if>
            <if test="sampleNo != null">#{sampleNo},</if>
            <if test="productInspectionId != null">#{productInspectionId},</if>
            <if test="reBuyVisible != null">#{reBuyVisible},</if>
            <if test="electricFlag != null">#{electricFlag},</if>
            <if test="certificateType != null">#{certificateType},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificate" parameterType="com.jkr.project.argi.domain.Certificate">
        update bas_certificate
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="dataScope != null">data_scope = #{dataScope},</if>
            <if test="no != null">no = #{no},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productMixFlag != null">product_mix_flag = #{productMixFlag},</if>
            <if test="productIntroduction != null">product_introduction = #{productIntroduction},</if>
            <if test="productProvince != null">product_province = #{productProvince},</if>
            <if test="productCity != null">product_city = #{productCity},</if>
            <if test="productCounty != null">product_county = #{productCounty},</if>
            <if test="productAddress != null">product_address = #{productAddress},</if>
            <if test="productDetail != null">product_detail = #{productDetail},</if>
            <if test="productSortCode != null">product_sort_code = #{productSortCode},</if>
            <if test="productSortName != null">product_sort_name = #{productSortName},</if>
            <if test="productCertificationCode != null">product_certification_code = #{productCertificationCode},</if>
            <if test="productCertificationName != null">product_certification_name = #{productCertificationName},</if>
            <if test="productNum != null">product_num = #{productNum},</if>
            <if test="productUnitCode != null">product_unit_code = #{productUnitCode},</if>
            <if test="productUnitName != null">product_unit_name = #{productUnitName},</if>
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="entName != null">ent_name = #{entName},</if>
            <if test="entBusinessType != null">ent_business_type = #{entBusinessType},</if>
            <if test="entType != null">ent_type = #{entType},</if>
            <if test="entMainType != null">ent_main_type = #{entMainType},</if>
            <if test="entFarmType != null">ent_farm_type = #{entFarmType},</if>
            <if test="entCardNo != null">ent_card_no = #{entCardNo},</if>
            <if test="entLegalPerson != null">ent_legal_person = #{entLegalPerson},</if>
            <if test="entProvince != null">ent_province = #{entProvince},</if>
            <if test="entCity != null">ent_city = #{entCity},</if>
            <if test="entCounty != null">ent_county = #{entCounty},</if>
            <if test="entAddress != null">ent_address = #{entAddress},</if>
            <if test="entDetail != null">ent_detail = #{entDetail},</if>
            <if test="entSocialCode != null">ent_social_code = #{entSocialCode},</if>
            <if test="entContactsPhone != null">ent_contacts_phone = #{entContactsPhone},</if>
            <if test="entAutograph != null">ent_autograph = #{entAutograph},</if>
            <if test="entCompanyIntroduction != null">ent_company_introduction = #{entCompanyIntroduction},</if>
            <if test="entHonor != null">ent_honor = #{entHonor},</if>
            <if test="productionDate != null">production_date = #{productionDate},</if>
            <if test="printCount != null">print_count = #{printCount},</if>
            <if test="blockChainId != null">block_chain_id = #{blockChainId},</if>
            <if test="beginSerialNumber != null">begin_serial_number = #{beginSerialNumber},</if>
            <if test="endSerialNumber != null">end_serial_number = #{endSerialNumber},</if>
            <if test="inspectionSituation != null">inspection_situation = #{inspectionSituation},</if>
            <if test="sampleNo != null">sample_no = #{sampleNo},</if>
            <if test="productInspectionId != null">product_inspection_id = #{productInspectionId},</if>
            <if test="reBuyVisible != null">re_buy_visible = #{reBuyVisible},</if>
            <if test="electricFlag != null">electric_flag = #{electricFlag},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateById" parameterType="Long">
        delete from bas_certificate where id = #{id}
    </delete>

    <delete id="deleteCertificateByIds" parameterType="String">
        delete from bas_certificate where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updatePrintCount">
        UPDATE bas_certificate SET
           print_count = #{printCount},
           update_by = #{updateBy},
           update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <select id="searchNextNo" resultType="Integer">
        SELECT IFNULL(MAX(no)+1,1) AS nextNo FROM bas_certificate
        where ent_county=#{code} AND DATE(create_time) = DATE(NOW());
    </select>

    <select id="getFrequencyByEntId" resultType="map">
        SELECT
        count(a.id) AS "certificateCount",
        IFNULL(SUM(a.print_count),0) AS "certificatePrintCount"
        FROM bas_certificate a
        <include refid="certificateJoins"/>
        WHERE p.ent_id = #{id}
    </select>

    <select id="getFrequencyByCountyCode" resultType="map">
        SELECT
        count(a.id) AS "certificateCountAmount",
        IFNULL(SUM(a.print_count),0) AS "certificatePrintCountAmount"
        FROM bas_certificate a
        INNER JOIN bas_ent e ON a.ent_id = e.id and e.frozen_flag='0'
        WHERE a.ent_county LIKE CONCAT(#{entCounty},'%')
        <if test="params.sqlMap.areaWhere!='' and params.sqlMap.areaWhere!=null">
            ${params.sqlMap.areaWhere}
        </if>
        AND a.ent_main_type != ''
        and a.del_flag = '1'
    </select>

    <select id="getSummaryInfo" resultType="map">
        SELECT
            COUNT(a.id) dataAmount,
            IFNULL(SUM(a.print_count),0) printAmount,
            MAX(DATE(a.create_time)) lastCreateDate
        FROM bas_certificate a
        WHERE a.ent_id = #{entId}
          and a.del_flag = '1'
    </select>

    <update id="updateBlockChainId">
        UPDATE bas_certificate SET
           block_chain_id = #{blockChainId},
           remark = '绑定区块链',
           update_by = null,
           update_time = now()
        WHERE id = #{id}
    </update>

    <select id="findInvalidList" resultMap="CertificateResult">
        SELECT
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        FROM bas_certificate a
        <if test="batchNo != null and batchNo != ''">
            <include refid="certificateNoJoins"/>
        </if>
        <where>
            a.del_flag = '1'
            <if test="batchNo != null and batchNo != ''">
                AND n.full_number LIKE concat('%',#{batchNo},'')
            </if>
            <if test="productName != null and productName != ''">
                <!--需求变更改为模糊查询 lxy 2021年5月25日9:40:13-->
                AND a.product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="productId != null and productId != ''">
                AND a.product_id = #{productId}
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="printCount != null and printCount != ''">
                AND a.print_count = #{printCount}
            </if>
            <!-- <if test="beginCreateDate != null and endCreateDate != null and beginCreateDate != '' and endCreateDate != ''">
                AND a.create_time BETWEEN #{beginCreateDate} AND #{endCreateDate}
            </if> -->
            <if test="beginCreateDate != null and beginCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_time,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endCreateDate != null and endCreateDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.create_time,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
            </if>

            <if test="time != null and time != ''">
                AND DATE_FORMAT(a.create_time,'%Y-%m-%d') = #{time}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="electricFlag != null and electricFlag != ''">
                AND a.electric_flag = #{electricFlag}
            </if>
            <if test="certificateType != null and certificateType != ''">
                AND a.certificate_type = #{certificateType}
            </if>
            <if test="entBusinessType != null and entBusinessType != ''">
                AND a.ent_business_type = #{entBusinessType}
            </if>
            <if test="entType != null and entType != ''">
                AND a.ent_type = #{entType}
            </if>
            <if test="entName != null and entName != ''">
                AND a.ent_name like "%"#{entName}"%"
            </if>
            <if test="entCode != null and entCode != ''">
                AND a.ent_county LIKE #{entCode}"%"
            </if>
            <if test="params.sqlMap.areaWhere!='' and params.sqlMap.areaWhere!=null">
                ${params.sqlMap.areaWhere}
            </if>
        </where>
        ORDER BY a.update_time DESC
    </select>

    <select id="findPrintTotalNum" resultType="Integer">
        SELECT IFNULL(sum(a.print_count),0) AS printTotalNum FROM bas_certificate a
        <where>
            a.del_flag = '1'
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="productName != null and productName != ''">
                AND a.product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="entName != null and entName != ''">
                AND a.ent_name like concat('%',#{entName},'%')
            </if>
            <if test="electricFlag != null and electricFlag != ''">
                AND a.electric_flag = #{electricFlag}
            </if>
            <if test="certificateType != null and certificateType != ''">
                AND a.certificate_type = #{certificateType}
            </if>
        </where>
    </select>
    <select id="incrementalQuery" resultType="com.jkr.project.argi.domain.Certificate">
        SELECT * FROM bas_certificate WHERE  <![CDATA[ create_time > #{date} ]]> and del_flag = '1'
    </select>
</mapper>
