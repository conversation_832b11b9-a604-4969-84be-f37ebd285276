<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateConfigDeviceMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateConfigDevice" id="CertificateConfigDeviceResult">
        <result property="id"    column="id"    />
        <result property="certificateConfigId"    column="certificate_config_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCertificateConfigDeviceVo">
        select id, certificate_config_id, device_id, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate_config_device
    </sql>

    <select id="selectCertificateConfigDeviceList" parameterType="com.jkr.project.argi.domain.CertificateConfigDevice" resultMap="CertificateConfigDeviceResult">
        <include refid="selectCertificateConfigDeviceVo"/>
        <where>
            <if test="certificateConfigId != null  and certificateConfigId != ''"> and certificate_config_id = #{certificateConfigId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            and del_flag = '1'
        </where>
        order by update_time desc
    </select>

    <select id="selectCertificateConfigDeviceById" parameterType="Long" resultMap="CertificateConfigDeviceResult">
        <include refid="selectCertificateConfigDeviceVo"/>
        where id = #{id}
    </select>

    <insert id="insertCertificateConfigDevice" parameterType="com.jkr.project.argi.domain.CertificateConfigDevice">
        insert into bas_certificate_config_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="certificateConfigId != null">certificate_config_id,</if>
            <if test="deviceId != null">device_id,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="certificateConfigId != null">#{certificateConfigId},</if>
            <if test="deviceId != null">#{deviceId},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateConfigDevice" parameterType="com.jkr.project.argi.domain.CertificateConfigDevice">
        update bas_certificate_config_device
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateConfigId != null">certificate_config_id = #{certificateConfigId},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_config_device set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_config_device set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateConfigDeviceById" parameterType="Long">
        delete from bas_certificate_config_device where id = #{id}
    </delete>

    <delete id="deleteCertificateConfigDeviceByIds" parameterType="String">
        delete from bas_certificate_config_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBatch">
        INSERT INTO bas_certificate_config_device(
            id,
            certificate_config_id,
            device_id,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id},
            #{item.certificateConfigId},
            #{item.deviceId},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            '1'
        )
        </foreach>
    </insert>

    <update id="deleteByCertificateConfigId">
        UPDATE bas_certificate_config_device SET
            del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','')
        WHERE certificate_config_id = #{certificateConfigId}
    </update>
</mapper>
