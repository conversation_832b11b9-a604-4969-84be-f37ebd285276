<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.ProductMapper">

    <resultMap type="com.jkr.project.argi.domain.Product" id="ProductResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="mixFlag" column="mix_flag"/>
        <result property="userId" column="user_id"/>
        <result property="entId" column="ent_id"/>
        <result property="dataScope" column="data_scope"/>
        <result property="productSortCode" column="product_sort_code"/>
        <result property="productSortName" column="product_sort_name"/>
        <result property="productCertificationCode" column="product_certification_code"/>
        <result property="productCertificationName" column="product_certification_name"/>
        <result property="addDate" column="add_date"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="address" column="address"/>
        <result property="detail" column="detail"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="productIntroduction" column="product_introduction"/>
        <result property="currentSampleNo" column="current_sample_no"/>
        <result property="queryCodeUrl" column="query_code_url"/>
        <result property="electricityLink" column="electricity_link"/>
        <result property="scaleAmount" column="scale_amount"/>
        <result property="scaleUnitCode" column="scale_unit_code"/>
        <result property="scaleUnitName" column="scale_unit_name"/>
        <result property="productionValue" column="production_value"/>
        <result property="productionAmount" column="production_amount"/>
        <result property="productionUnitCode" column="production_unit_code"/>
        <result property="productionUnitName" column="production_unit_name"/>
        <result property="printAmount" column="print_amount"/>
        <result property="printDate" column="print_date"/>
        <result property="reBuyProductId" column="re_buy_product_id"/>
        <result property="reBuyVisible" column="re_buy_visible"/>
        <result property="reBuyProductName" column="re_buy_product_name"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="count" column="count"/>

        <result property="entName" column="entName"/>
        <result property="businessType" column="business_type"/>
        <result property="entType" column="ent_type"/>
    </resultMap>

    <sql id="selectProductVo">
        select id,
               name,
               mix_flag,
               user_id,
               ent_id,
               data_scope,
               product_sort_code,
               product_sort_name,
               product_certification_code,
               product_certification_name,
               add_date,
               province,
               city,
               county,
               address,
               detail,
               longitude,
               latitude,
               product_introduction,
               current_sample_no,
               query_code_url,
               electricity_link,
               scale_amount,
               scale_unit_code,
               scale_unit_name,
               production_value,
               production_amount,
               production_unit_code,
               production_unit_name,
               print_amount,
               print_date,
               re_buy_product_id,
               re_buy_visible,
               re_buy_product_name,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from bas_product
    </sql>

    <sql id="Base_Column_List">
        <trim>
            ${alias}.id, ${alias}.name, ${alias}.mix_flag, ${alias}.user_id, ${alias}.ent_id,
            ${alias}.data_scope, ${alias}.product_sort_code, ${alias}.product_sort_name, ${alias}.product_certification_code, ${alias}.product_certification_name,
            ${alias}.add_date, ${alias}.province, ${alias}.city, ${alias}.county, ${alias}.address,
            ${alias}.detail, ${alias}.longitude, ${alias}.latitude, ${alias}.product_introduction, ${alias}.current_sample_no,
            ${alias}.query_code_url, ${alias}.electricity_link, ${alias}.scale_amount, ${alias}.scale_unit_code, ${alias}.scale_unit_name,
            ${alias}.production_value, ${alias}.production_amount, ${alias}.production_unit_code, ${alias}.production_unit_name, ${alias}.print_amount,
            ${alias}.print_date, ${alias}.re_buy_product_id, ${alias}.re_buy_visible, ${alias}.re_buy_product_name, ${alias}.del_flag,
            ${alias}.create_by, ${alias}.create_time, ${alias}.update_by, ${alias}.update_time, ${alias}.remark
        </trim>
    </sql>

    <select id="selectProductList" parameterType="com.jkr.project.argi.domain.Product" resultMap="ProductResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
            , b.name as entName, b.business_type, b.ent_type
        from bas_product a
        left join bas_ent b on a.ent_id = b.id
        <where>
            <if test="name != null and name != ''">
                AND a.name LIKE concat('%',#{name},'%')
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="mixFlag != null and mixFlag != ''">
                AND a.mix_flag = #{mixFlag}
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="name != null and name != ''">
                and a.name like concat('%',#{name},'%')
            </if>
            <if test="dataScope != null and dataScope != ''">
                and a.data_scope = #{dataScope}
            </if>
            <if test="beginAddDate != null">
                AND Date(a.add_date) &gt;= Date(#{beginAddDate})
            </if>
            <if test="endAddDate != null">
                AND Date(a.add_date) &lt;= Date(#{endAddDate})
            </if>
            <if test="beginPrintDate != null">
                AND Date(a.print_date) <![CDATA[ >= ]]>  Date(#{beginPrintDate})
            </if>
            <if test="endPrintDate != null">
                AND Date(a.print_date)<![CDATA[ <= ]]> Date(#{endPrintDate})
            </if>
            <if test="entType != null  and entType != ''">
                and b.ent_type = #{entType}
            </if>
            <if test="entName != null and entName !=''">
                and b.name like concat('%', #{entName}, '%')
            </if>
            <if test="businessType != null and businessType != ''">
                and b.business_type = #{businessType}
            </if>
            <if test="beginPrintAmount != null">
                AND a.print_amount <![CDATA[ >= ]]> #{beginPrintAmount}
            </if>
            <if test="endPrintAmount != null">
                AND a.print_amount <![CDATA[ <= ]]> #{endPrintAmount}
            </if>
            and a.del_flag = '1'
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectProductById" parameterType="Long" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        where id = #{id}
    </select>

    <insert id="insertProduct" parameterType="com.jkr.project.argi.domain.Product">
        insert into bas_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="mixFlag != null">mix_flag,</if>
            <if test="userId != null">user_id,</if>
            <if test="entId != null">ent_id,</if>
            <if test="dataScope != null">data_scope,</if>
            <if test="productSortCode != null">product_sort_code,</if>
            <if test="productSortName != null">product_sort_name,</if>
            <if test="productCertificationCode != null">product_certification_code,</if>
            <if test="productCertificationName != null">product_certification_name,</if>
            <if test="addDate != null">add_date,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="county != null">county,</if>
            <if test="address != null">address,</if>
            <if test="detail != null">detail,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="productIntroduction != null">product_introduction,</if>
            <if test="currentSampleNo != null">current_sample_no,</if>
            <if test="queryCodeUrl != null">query_code_url,</if>
            <if test="electricityLink != null">electricity_link,</if>
            <if test="scaleAmount != null">scale_amount,</if>
            <if test="scaleUnitCode != null">scale_unit_code,</if>
            <if test="scaleUnitName != null">scale_unit_name,</if>
            <if test="productionValue != null">production_value,</if>
            <if test="productionAmount != null">production_amount,</if>
            <if test="productionUnitCode != null">production_unit_code,</if>
            <if test="productionUnitName != null">production_unit_name,</if>
            <if test="printAmount != null">print_amount,</if>
            <if test="printDate != null">print_date,</if>
            <if test="reBuyProductId != null">re_buy_product_id,</if>
            <if test="reBuyVisible != null">re_buy_visible,</if>
            <if test="reBuyProductName != null">re_buy_product_name,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="mixFlag != null">#{mixFlag},</if>
            <if test="userId != null">#{userId},</if>
            <if test="entId != null">#{entId},</if>
            <if test="dataScope != null">#{dataScope},</if>
            <if test="productSortCode != null">#{productSortCode},</if>
            <if test="productSortName != null">#{productSortName},</if>
            <if test="productCertificationCode != null">#{productCertificationCode},</if>
            <if test="productCertificationName != null">#{productCertificationName},</if>
            <if test="addDate != null">#{addDate},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="county != null">#{county},</if>
            <if test="address != null">#{address},</if>
            <if test="detail != null">#{detail},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="productIntroduction != null">#{productIntroduction},</if>
            <if test="currentSampleNo != null">#{currentSampleNo},</if>
            <if test="queryCodeUrl != null">#{queryCodeUrl},</if>
            <if test="electricityLink != null">#{electricityLink},</if>
            <if test="scaleAmount != null">#{scaleAmount},</if>
            <if test="scaleUnitCode != null">#{scaleUnitCode},</if>
            <if test="scaleUnitName != null">#{scaleUnitName},</if>
            <if test="productionValue != null">#{productionValue},</if>
            <if test="productionAmount != null">#{productionAmount},</if>
            <if test="productionUnitCode != null">#{productionUnitCode},</if>
            <if test="productionUnitName != null">#{productionUnitName},</if>
            <if test="printAmount != null">#{printAmount},</if>
            <if test="printDate != null">#{printDate},</if>
            <if test="reBuyProductId != null">#{reBuyProductId},</if>
            <if test="reBuyVisible != null">#{reBuyVisible},</if>
            <if test="reBuyProductName != null">#{reBuyProductName},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateProduct" parameterType="com.jkr.project.argi.domain.Product">
        update bas_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="mixFlag != null">mix_flag = #{mixFlag},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="dataScope != null">data_scope = #{dataScope},</if>
            <if test="productSortCode != null">product_sort_code = #{productSortCode},</if>
            <if test="productSortName != null">product_sort_name = #{productSortName},</if>
            <if test="productCertificationCode != null">product_certification_code = #{productCertificationCode},</if>
            <if test="productCertificationName != null">product_certification_name = #{productCertificationName},</if>
            <if test="addDate != null">add_date = #{addDate},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="county != null">county = #{county},</if>
            <if test="address != null">address = #{address},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="productIntroduction != null">product_introduction = #{productIntroduction},</if>
            <if test="currentSampleNo != null">current_sample_no = #{currentSampleNo},</if>
            <if test="queryCodeUrl != null">query_code_url = #{queryCodeUrl},</if>
            <if test="electricityLink != null">electricity_link = #{electricityLink},</if>
            <if test="scaleAmount != null">scale_amount = #{scaleAmount},</if>
            <if test="scaleUnitCode != null">scale_unit_code = #{scaleUnitCode},</if>
            <if test="scaleUnitName != null">scale_unit_name = #{scaleUnitName},</if>
            <if test="productionValue != null">production_value = #{productionValue},</if>
            <if test="productionAmount != null">production_amount = #{productionAmount},</if>
            <if test="productionUnitCode != null">production_unit_code = #{productionUnitCode},</if>
            <if test="productionUnitName != null">production_unit_name = #{productionUnitName},</if>
            <if test="printAmount != null">print_amount = #{printAmount},</if>
            <if test="printDate != null">print_date = #{printDate},</if>
            <if test="reBuyProductId != null">re_buy_product_id = #{reBuyProductId},</if>
            <if test="reBuyVisible != null">re_buy_visible = #{reBuyVisible},</if>
            <if test="reBuyProductName != null">re_buy_product_name = #{reBuyProductName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_product set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_product
        set del_flag = REPLACE(unix_timestamp(current_timestamp(3)), '.', '')
        where id = #{id}
    </update>

    <delete id="deleteProductById" parameterType="Long">
        delete
        from bas_product
        where id = #{id}
    </delete>

    <delete id="deleteProductByIds" parameterType="String">
        delete from bas_product where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateInspectionResultById">
        UPDATE bas_product bp
        INNER JOIN
        <foreach collection="list" item="item" open="(" separator=" UNION ALL " close=")">
            SELECT #{item.sampleNo} AS "sampleNo" ,#{item.sampleId} AS "productId", #{item.qrcodeUrl} AS "queryCodeUrl"
        </foreach> a
        ON bp.id =a.productId
        SET bp.current_sample_no = a.sampleNo,bp.query_code_url =a.queryCodeUrl
    </update>

    <select id="findProductList" resultMap="ProductResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        from bas_product a
        WHERE a.ent_id = #{entId}
        <if test="mixFlag != null and mixFlag != ''">
            and a.mix_flag = #{mixFlag}
        </if>
        and a.del_flag = '1'
        order by a.create_time desc
    </select>

    <select id="findProductPage" resultMap="ProductResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        , sum(b.print_count) AS "count"
        FROM bas_product a
        LEFT JOIN bas_certificate b ON a.id = b.product_id and b.del_flag = ='1'
        <where>
            a.del_flag = '1'
            <if test="name != null and name != ''">
                AND a.name LIKE concat('%',#{name},'%')
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND a.product_sort_code = #{productSortCode}
            </if>
            <if test="entId != null and entId != ''">
                AND a.ent_id = #{entId}
            </if>
            <if test="mixFlag != null and mixFlag != ''">
                AND a.mix_flag = #{mixFlag}
            </if>
            <!-- <if test="beginAddDate != null and endAddDate != null and beginAddDate != '' and endAddDate != ''">
                AND a.add_date BETWEEN #{beginAddDate} AND #{endAddDate}
            </if> -->
            <if test="beginAddDate != null">
                AND Date(a.add_date) &gt;= Date(#{beginAddDate})
            </if>
            <if test="endAddDate != null">
                AND Date(a.add_date) &lt;= Date(#{endAddDate})
            </if>
        </where>
        group by  a.id
        <choose>
            <when test="addDateOrder == 1 ">
                ORDER BY a.add_date asc,a.create_time asc
            </when>
            <when test="addDateOrder == 2">
                ORDER BY a.add_date desc,a.create_time desc
            </when>
            <when test="printCountOrder == 1">
                ORDER BY count asc
            </when>
            <when test="printCountOrder == 2">
                ORDER BY count desc
            </when>
            <otherwise>
                ORDER BY a.add_date desc,a.create_time desc
                -- 	,a.update_time,a.id DESC
            </otherwise>
        </choose>
    </select>

    <select id="findCountGroupName" resultType="map">
        SELECT
        COUNT(DISTINCT a.name) count
        FROM bas_product a
        WHERE
        a.del_flag = '1' and a.data_scope='1'
        <if test="name != null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="userId != null and userId != ''">
            AND a.user_id = #{userId}
        </if>
    </select>

    <select id="findProductCount" resultType="map">
        SELECT COUNT(1) productCount FROM bas_product WHERE `name` = #{name} and del_flag = '1' and data_scope ='1'
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
    </select>

    <select id="findListByUserId" resultMap="ProductResult">
        SELECT
            bp.id,
            bp.name,
            IFNULL(a.count,0) AS count
        FROM bas_product bp
        LEFT JOIN (
            SELECT
                IFNULL(COUNT(c.product_id),0) AS count,
                c.product_id,
                c.product_name
            FROM bas_certificate c
            WHERE c.del_flag = 1
            <if test="userId != null and userId != ''">
                AND c.user_id = #{userId}
            </if>
            GROUP BY c.product_id
            ORDER BY count DESC
        ) a ON a.product_id = bp.id
        WHERE
        bp.del_flag = '1'
        <if test="userId != null and userId != ''">
            AND bp.user_id = #{userId}
        </if>
        <if test="name != null and name != ''">
            AND bp.name like concat('%',#{name},'%')
        </if>
        ORDER BY count DESC,bp.add_date ASC
    </select>
    <select id="getSyncData" resultType="com.jkr.project.argi.domain.Product">
        select * from bas_product where del_flag = '1' and update_time <![CDATA[ >= ]]> #{date}
    </select>

    <update id="updatePrintAmount">
        UPDATE bas_product SET
           print_amount = print_amount+#{amount},
           print_date = now()
        WHERE id = #{id}
    </update>

    <update id="updateInvalidPrintAmount">
        UPDATE bas_product t
            INNER JOIN (
                SELECT
                    c.ent_id AS entId,
                    c.product_id as product_id,
                    SUM(IF(c.id!="" and c.del_flag = '1',1,0)) certificateAmount,
                    SUM(IF(c.print_count!="" and c.del_flag = '1',c.print_count,0)) certificatePrintAmount,
                    max(IF(c.del_flag = '1',c.create_time,null)) createDate
                FROM bas_certificate c
                where c.ent_id = #{entId}
                and c.product_id=#{productId}
                group by c.ent_id
            ) a ON t.id = a.product_id and t.ent_id = a.entId
            SET t.print_amount = a.certificatePrintAmount,
                t.print_date = a.createDate
        where t.id = #{productId}
    </update>
</mapper>
