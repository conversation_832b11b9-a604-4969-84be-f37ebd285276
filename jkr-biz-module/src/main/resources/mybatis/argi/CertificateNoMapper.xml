<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateNoMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateNo" id="CertificateNoResult">
        <result property="id"    column="id"    />
        <result property="certificateId"    column="certificate_id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="fullNumber"    column="full_number"    />
        <result property="printCount"    column="print_count"    />
        <result property="blockChainId"    column="block_chain_id"    />
        <result property="electricFlag"    column="electric_flag"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCertificateNoVo">
        select id, certificate_id, batch_no, serial_number, full_number, print_count, block_chain_id, electric_flag, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate_no
    </sql>

    <select id="selectCertificateNoList" parameterType="com.jkr.project.argi.domain.CertificateNo" resultMap="CertificateNoResult">
        <include refid="selectCertificateNoVo"/>
        <where>
            <if test="certificateId != null  and certificateId != ''"> and certificate_id = #{certificateId}</if>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
            <if test="fullNumber != null  and fullNumber != ''"> and full_number = #{fullNumber}</if>
            <if test="printCount != null "> and print_count = #{printCount}</if>
            <if test="blockChainId != null  and blockChainId != ''"> and block_chain_id = #{blockChainId}</if>
            <if test="electricFlag != null  and electricFlag != ''"> and electric_flag = #{electricFlag}</if>
            and del_flag = '1'
        </where>
        ORDER BY full_number DESC
    </select>

    <select id="selectCertificateNoById" parameterType="Long" resultMap="CertificateNoResult">
        <include refid="selectCertificateNoVo"/>
        where id = #{id}
    </select>

    <insert id="insertCertificateNo" parameterType="com.jkr.project.argi.domain.CertificateNo">
        insert into bas_certificate_no
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="certificateId != null">certificate_id,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="fullNumber != null">full_number,</if>
            <if test="printCount != null">print_count,</if>
            <if test="blockChainId != null">block_chain_id,</if>
            <if test="electricFlag != null">electric_flag,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="certificateId != null">#{certificateId},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="fullNumber != null">#{fullNumber},</if>
            <if test="printCount != null">#{printCount},</if>
            <if test="blockChainId != null">#{blockChainId},</if>
            <if test="electricFlag != null">#{electricFlag},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateNo" parameterType="com.jkr.project.argi.domain.CertificateNo">
        update bas_certificate_no
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateId != null">certificate_id = #{certificateId},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="fullNumber != null">full_number = #{fullNumber},</if>
            <if test="printCount != null">print_count = #{printCount},</if>
            <if test="blockChainId != null">block_chain_id = #{blockChainId},</if>
            <if test="electricFlag != null">electric_flag = #{electricFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_no set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_no set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateNoById" parameterType="Long">
        delete from bas_certificate_no where id = #{id}
    </delete>

    <delete id="deleteCertificateNoByIds" parameterType="String">
        delete from bas_certificate_no where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getFirstByCertificateId" resultType="com.jkr.project.argi.domain.CertificateNo">
        <include refid="selectCertificateNoVo"/>
        <where>
            del_flag = '1'
            AND certificate_id = #{certificateId}
        </where>
        ORDER BY full_number ASC LIMIT 1
    </select>

    <select id="getMaxSerialNumber" resultType="Integer">
        SELECT
            max(serial_number) serialNumber
        FROM bas_certificate_no a
        WHERE a.batch_no = #{batchNo}
    </select>

    <insert id="insertBatch">
        INSERT INTO bas_certificate_no(
            id,
            certificate_id,
            batch_no,
            serial_number,
            full_number,
            print_count,
            block_chain_id,
            electric_flag,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id},
            #{item.certificateId},
            #{item.batchNo},
            #{item.serialNumber},
            #{item.fullNumber},
            #{item.printCount},
            #{item.blockChainId},
            #{item.electricFlag},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            '1'
        )
        </foreach>
    </insert>

    <update id="updateBlockChainId">
        UPDATE bas_certificate_no SET
          block_chain_id = #{blockChainId},
          update_time = now()
        WHERE id = #{id}
    </update>

    <update id="updatePrintCount">
        UPDATE bas_certificate_no SET
          print_count=print_count+1,
          update_time = now()
        WHERE id = #{id}
    </update>
</mapper>
