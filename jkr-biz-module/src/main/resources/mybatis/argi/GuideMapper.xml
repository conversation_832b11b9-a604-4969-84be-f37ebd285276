<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.GuideMapper">

    <resultMap type="com.jkr.project.argi.domain.Guide" id="GuideResult">
            <result property="id"    column="id"    />
            <result property="title"    column="title"    />
            <result property="label"    column="label"    />
            <result property="businessType"    column="business_type"    />
            <result property="source"    column="source"    />
            <result property="content"    column="content"    />
            <result property="editDate"    column="edit_date"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectGuideVo">
        select * from bas_guide
    </sql>

    <select id="selectGuideList" parameterType="com.jkr.project.argi.domain.Guide" resultType="com.jkr.project.argi.domain.Guide">
        <include refid="selectGuideVo"/>
        <where>
                        <if test="title != null  and title != ''"> and title like concat('%',#{title},'%') </if>
                        <if test="status != null  and status != ''"> and status = #{status}</if>
                        <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
                        <if test="source != null  and source != ''"> and source = #{source}</if>
                        <if test="content != null  and content != ''"> and content = #{content}</if>
                        <if test="editDate != null "> and edit_date = #{editDate}</if>
                        <if test="beginTime != null and beginTime != ''"> and create_time &gt;= #{beginTime} </if>
                        <if test="endTime != null and endTime != ''"> and create_time &lt;= #{endTime} </if>
            and del_flag = '1' order by create_time desc
        </where>
    </select>

    <select id="selectGuideById" parameterType="Long" resultMap="GuideResult">
            <include refid="selectGuideVo"/>
            where id = #{id}
    </select>

    <insert id="insertGuide" parameterType="com.jkr.project.argi.domain.Guide">
        insert into bas_guide
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="title != null">title,</if>
                    <if test="label != null">label,</if>
                    <if test="businessType != null">business_type,</if>
                    <if test="source != null">source,</if>
                    <if test="content != null">content,</if>
                    <if test="editDate != null">edit_date,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="title != null">#{title},</if>
                    <if test="label != null">#{label},</if>
                    <if test="businessType != null">#{businessType},</if>
                    <if test="source != null">#{source},</if>
                    <if test="content != null">#{content},</if>
                    <if test="editDate != null">#{editDate},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateGuide" parameterType="com.jkr.project.argi.domain.Guide">
        update bas_guide
        <trim prefix="SET" suffixOverrides=",">
                    <if test="title != null">title = #{title},</if>
                    <if test="label != null">label = #{label},</if>
                    <if test="businessType != null">business_type = #{businessType},</if>
                    <if test="source != null">source = #{source},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="editDate != null">edit_date = #{editDate},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_guide set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_guide set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteGuideById" parameterType="Long">
        delete from bas_guide where id = #{id}
    </delete>

    <delete id="deleteGuideByIds" parameterType="String">
        delete from bas_guide where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
