<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.AnalysisMapper">

    <select id="findEntCount" resultType="int">
        SELECT IFNULL(COUNT(id),0) FROM bas_ent WHERE del_flag = '1' AND examine_status = '1'
    </select>

    <select id="findCertificateCount" resultType="int">
        SELECT IFNULL(SUM(IFNULL(print_count,0)),0) FROM bas_certificate WHERE del_flag = '1'
    </select>

    <select id="findProductCount" resultType="int">
        SELECT IFNULL(COUNT(id),0) FROM bas_product WHERE del_flag = '1'
    </select>

    <select id="findScanCount" resultType="int">
        SELECT IFNULL(COUNT(id),0) FROM bas_scan_record WHERE del_flag = '1'
    </select>

    <resultMap type="com.jkr.project.argi.domain.AnalysisInfo" id="AnalysisResult">
        <result property="dataValue" column="data_value"/>
        <result property="dataCode" column="data_code"/>
        <result property="dataLabel" column="data_label"/>
    </resultMap>

    <select id="findEntMainTypeCount" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.ent_num as data_value, a.main_type as data_code, b.dict_label as data_label
        FROM (
             SELECT IFNULL(COUNT(id),0) AS ent_num, main_type
             FROM bas_ent WHERE del_flag = '1' AND examine_status = '1'
            <if test="queryAreaCode != null">
                and county LIKE concat(#{queryAreaCode},'%')
            </if>
             GROUP BY main_type
             ORDER BY main_type ASC
         ) a LEFT JOIN sys_dict_data b ON a.main_type = b.dict_value AND b.dict_type='main_type' AND b.status='0'
    </select>

    <select id="findEntTypeCount" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.ent_num as data_value, a.ent_type as data_code, b.dict_label as data_label
        FROM (
            SELECT IFNULL(COUNT(id),0) AS ent_num, ent_type
            FROM bas_ent WHERE del_flag = '1' AND examine_status = '1'
            <if test="queryAreaCode != null">
                and area_code LIKE concat(#{queryAreaCode},'%')
            </if>
            GROUP BY ent_type
            ORDER BY ent_type ASC
        ) a LEFT JOIN sys_dict_data b ON a.ent_type = b.dict_value AND b.dict_type='ent_type' AND b.status='0'
    </select>

    <select id="findCertificateByMainType" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.dict_label as data_label, IFNULL(SUM(IFNULL(b.print_count,0)),0) as data_value
        FROM sys_dict_data a
        LEFT JOIN bas_certificate b ON a.dict_value = b.ent_main_type AND b.del_flag = '1'
        <if test="queryAreaCode != null and queryAreaCode != ''">
            AND b.ent_county LIKE #{queryAreaCode}"%"
        </if>
        WHERE a.dict_type='main_type' AND a.status='0'
        GROUP BY a.dict_label
    </select>

    <select id="findCertificateByEntType" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.dict_label as data_label, IFNULL(SUM(IFNULL(b.print_count,0)),0) as data_value
        FROM sys_dict_data a
        LEFT JOIN bas_certificate b ON a.dict_value = b.ent_type AND b.del_flag = '1'
        <if test="queryAreaCode != null and queryAreaCode != ''">
            AND b.ent_county LIKE #{queryAreaCode}"%"
        </if>
        WHERE a.dict_type='ent_type' AND a.status='0'
        GROUP BY a.dict_label
    </select>

    <select id="findCertificateByProductSortCode" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.dict_label as data_label, IFNULL(SUM(IFNULL(b.print_count,0)),0) as data_value
        FROM sys_dict_data a
        LEFT JOIN bas_certificate b ON a.dict_value = b.product_sort_code AND b.del_flag = '1'
        <if test="queryAreaCode != null and queryAreaCode != ''">
            AND b.ent_county LIKE #{queryAreaCode}"%"
        </if>
        WHERE a.dict_type='product_sort_code' AND a.status='0'
        GROUP BY a.dict_label
    </select>

    <select id="findProductByProductSortCode" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.dict_label as data_label, IFNULL(COUNT(b.id),0) as data_value
        FROM sys_dict_data a
        LEFT JOIN bas_product b ON a.dict_value = b.product_sort_code AND b.del_flag = '1'
        <if test="queryAreaCode != null and queryAreaCode != ''">
            AND b.county LIKE #{queryAreaCode}"%"
        </if>
        WHERE a.dict_type='product_sort_code' AND a.status='0'
        GROUP BY a.dict_label
    </select>

    <select id="findScanByProductSortCode" resultMap="AnalysisResult" parameterType="com.jkr.project.argi.domain.AnalysisInfo">
        SELECT a.dict_label as data_label, IFNULL(COUNT(b.id),0) as data_value
        FROM sys_dict_data a
        LEFT JOIN bas_certificate c ON a.dict_value = c.product_sort_code AND c.del_flag = '1'
        <if test="queryAreaCode != null and queryAreaCode != ''">
            AND c.ent_county LIKE #{queryAreaCode}"%"
        </if>
        LEFT JOIN bas_scan_record b ON c.id = b.certificate_id AND b.del_flag = '1'
        WHERE a.dict_type='product_sort_code' AND a.status='0'
        GROUP BY a.dict_label
    </select>
</mapper>
