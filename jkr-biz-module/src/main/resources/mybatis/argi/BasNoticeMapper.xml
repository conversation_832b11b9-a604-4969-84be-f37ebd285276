<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.BasNoticeMapper">

    <resultMap type="com.jkr.project.argi.domain.BasNotice" id="BasNoticeResult">
            <result property="id"    column="id"    />
            <result property="title"    column="title"    />
            <result property="content"    column="content"    />
            <result property="publishTime"    column="publish_time"    />
            <result property="status"    column="status"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectBasNoticeVo">
        select * from bas_notice
    </sql>

    <select id="selectBasNoticeList" parameterType="com.jkr.project.argi.domain.BasNotice" resultMap="BasNoticeResult">
        <include refid="selectBasNoticeVo"/>
        <where>
                        <if test="title != null  and title != ''"> and title like concat('%',#{title},'%')</if>
                        <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
                        <!-- 创建时间段查询 -->
                        <if test="createBeginTime != null and createBeginTime != ''">
                            and create_time &gt;= #{createBeginTime}
                        </if>
                        <if test="createEndTime != null and createEndTime != ''">
                            and create_time &lt;= #{createEndTime}
                        </if>
                        <!-- 发布时间段查询 -->
                        <if test="publishBeginTime != null and publishBeginTime != ''">
                            and publish_time &gt;= #{publishBeginTime}
                        </if>
                        <if test="publishEndTime != null and publishEndTime != ''">
                            and publish_time &lt;= #{publishEndTime}
                        </if>
            and del_flag = '1' order by create_time desc
        </where>
    </select>

    <select id="selectBasNoticeById" parameterType="Long" resultMap="BasNoticeResult">
            <include refid="selectBasNoticeVo"/>
            where id = #{id}
    </select>

    <insert id="insertBasNotice" parameterType="com.jkr.project.argi.domain.BasNotice">
        insert into bas_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="title != null">title,</if>
                    <if test="content != null">content,</if>
                    <if test="editDate != null">edit_date,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="title != null">#{title},</if>
                    <if test="content != null">#{content},</if>
                    <if test="editDate != null">#{editDate},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateBasNotice" parameterType="com.jkr.project.argi.domain.BasNotice">
        update bas_notice
        <trim prefix="SET" suffixOverrides=",">
                    <if test="title != null">title = #{title},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="editDate != null">edit_date = #{editDate},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_notice set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_notice set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteBasNoticeById" parameterType="Long">
        delete from bas_notice where id = #{id}
    </delete>

    <delete id="deleteBasNoticeByIds" parameterType="String">
        delete from bas_notice where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
