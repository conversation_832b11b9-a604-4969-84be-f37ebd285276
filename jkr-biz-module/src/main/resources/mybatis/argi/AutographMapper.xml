<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.AutographMapper">

    <resultMap type="com.jkr.project.argi.domain.Autograph" id="AutographResult">
            <result property="id"    column="id"    />
            <result property="entId"    column="ent_id"    />
            <result property="autograph"    column="autograph"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectAutographVo">
        select id, ent_id, autograph, del_flag, create_by, create_time, update_by, update_time, remark from bas_autograph
    </sql>
    <sql id="autographColumns">
        a.id AS "id",
		a.ent_id AS "entId",
		a.autograph AS "autograph",
		a.create_by AS "createBy.id",
		a.create_time AS "createTime",
		a.update_by AS "updateBy.id",
		a.update_time AS "updateTime",
		a.remark AS "remark",
		a.del_flag AS "delFlag"
    </sql>
    <select id="selectAutographList" parameterType="com.jkr.project.argi.domain.Autograph" resultMap="AutographResult">
        <include refid="selectAutographVo"/>
        <where>
                        <if test="entId != null  and entId != ''"> and ent_id = #{entId}</if>
                        <if test="autograph != null  and autograph != ''"> and autograph = #{autograph}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectAutographById" parameterType="Long" resultMap="AutographResult">
            <include refid="selectAutographVo"/>
            where id = #{id}
    </select>

    <insert id="insertAutograph" parameterType="com.jkr.project.argi.domain.Autograph">
        insert into bas_autograph
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="entId != null">ent_id,</if>
                    <if test="autograph != null">autograph,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="entId != null">#{entId},</if>
                    <if test="autograph != null">#{autograph},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateAutograph" parameterType="com.jkr.project.argi.domain.Autograph">
        update bas_autograph
        <trim prefix="SET" suffixOverrides=",">
                    <if test="entId != null">ent_id = #{entId},</if>
                    <if test="autograph != null">autograph = #{autograph},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_autograph set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_autograph set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteAutographById" parameterType="Long">
        delete from bas_autograph where id = #{id}
    </delete>

    <delete id="deleteAutographByIds" parameterType="String">
        delete from bas_autograph where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getByEntId" resultType="com.jkr.project.argi.domain.Autograph">
        SELECT
        <include refid="autographColumns"/>
        FROM bas_autograph a
        WHERE a.ent_id = #{entId}
        and a.del_flag = '1'
    </select>

    <delete id="deleteByEntId">
        delete from bas_autograph
        WHERE ent_id = #{entId}
    </delete>
</mapper>
