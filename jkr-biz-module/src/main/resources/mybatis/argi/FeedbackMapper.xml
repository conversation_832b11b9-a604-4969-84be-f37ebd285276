<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.FeedbackMapper">

    <resultMap type="com.jkr.project.argi.domain.Feedback" id="FeedbackResult">
            <result property="id"    column="id"    />
            <result property="memberId"    column="member_id"    />
            <result property="phone"    column="phone"    />
            <result property="content"    column="content"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFeedbackVo">
        select id, member_id, phone, content, del_flag, create_by, create_time, update_by, update_time, remark from bas_feedback
    </sql>

    <select id="selectFeedbackList" parameterType="com.jkr.project.argi.domain.Feedback" resultMap="FeedbackResult">
        <include refid="selectFeedbackVo"/>
        <where>
                        <if test="memberId != null  and memberId != ''"> and member_id = #{memberId}</if>
                        <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
                        <if test="content != null  and content != ''"> and content = #{content}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectFeedbackById" parameterType="Long" resultMap="FeedbackResult">
            <include refid="selectFeedbackVo"/>
            where id = #{id}
    </select>

    <insert id="insertFeedback" parameterType="com.jkr.project.argi.domain.Feedback">
        insert into bas_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="memberId != null">member_id,</if>
                    <if test="phone != null">phone,</if>
                    <if test="content != null">content,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="memberId != null">#{memberId},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="content != null">#{content},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateFeedback" parameterType="com.jkr.project.argi.domain.Feedback">
        update bas_feedback
        <trim prefix="SET" suffixOverrides=",">
                    <if test="memberId != null">member_id = #{memberId},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_feedback set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_feedback set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteFeedbackById" parameterType="Long">
        delete from bas_feedback where id = #{id}
    </delete>

    <delete id="deleteFeedbackByIds" parameterType="String">
        delete from bas_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
