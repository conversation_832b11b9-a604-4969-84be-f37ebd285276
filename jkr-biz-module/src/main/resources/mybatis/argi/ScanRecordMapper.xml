<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.ScanRecordMapper">

    <resultMap type="com.jkr.project.argi.domain.ScanRecord" id="ScanRecordResult">
        <result property="id"    column="id"    />
        <result property="certificateId"    column="certificate_id"    />
        <result property="fullNumber"    column="full_number"    />
        <result property="entId"    column="ent_id"    />
        <result property="scanDate"    column="scan_date"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="county"    column="county"    />
        <result property="ip"    column="ip"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <result property="productName" column="product_name"/>
        <result property="entName" column="ent_name"/>
        <result property="beginSerialNumber" column="begin_serial_number"/>
        <result property="endSerialNumber" column="end_serial_number"/>
        <result property="productSortName" column="product_sort_name"/>
    </resultMap>

    <sql id="selectScanRecordVo">
        select id, certificate_id, full_number, ent_id, scan_date, province, city, county, ip, del_flag, create_by, create_time, update_by, update_time, remark from bas_scan_record
    </sql>

    <sql id="Base_Column_List">
        <trim>
            ${alias}.id, ${alias}.certificate_id, ${alias}.full_number, ${alias}.ent_id, ${alias}.scan_date,
            ${alias}.province, ${alias}.city, ${alias}.county, ${alias}.ip, ${alias}.del_flag,
            ${alias}.create_by, ${alias}.create_time, ${alias}.update_by, ${alias}.update_time, ${alias}.remark
        </trim>
    </sql>

    <select id="selectScanRecordList" parameterType="com.jkr.project.argi.domain.ScanRecord" resultMap="ScanRecordResult">
        select
            <include refid="Base_Column_List">
                <property name="alias" value="a"/>
            </include>
            , c.product_name
            , c.ent_name
        from bas_scan_record a
        LEFT JOIN bas_certificate_no b ON a.full_number = b.full_number AND b.del_flag = '1'
        LEFT JOIN bas_certificate c ON c.id = b.certificate_id AND c.del_flag = '1'
        <where>
            <if test="entId != null  and entId != ''">
                and a.ent_id = #{entId}
            </if>
            <if test="productName != null and productName != ''">
                and c.product_name like CONCAT('%', #{productName}, '%')
            </if>
            <if test="scanDate != null">
                and DATE_FORMAT(a.scan_date,'%Y-%m-%d') = DATE_FORMAT(#{scanDate},'%Y-%m-%d')
            </if>
            <if test="beginScanDate != null and beginScanDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.scan_date,'%Y-%m-%d') >=DATE_FORMAT(#{beginScanDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endScanDate != null and endScanDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.scan_date,'%Y-%m-%d') <=DATE_FORMAT(#{endScanDate},'%Y-%m-%d') ]]>
            </if>
            and a.del_flag = '1'
        </where>
        ORDER BY a.update_time DESC
    </select>

    <select id="selectScanRecordById" parameterType="Long" resultMap="ScanRecordResult">
        <include refid="selectScanRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertScanRecord" parameterType="com.jkr.project.argi.domain.ScanRecord">
        insert into bas_scan_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="certificateId != null">certificate_id,</if>
            <if test="fullNumber != null">full_number,</if>
            <if test="entId != null">ent_id,</if>
            <if test="scanDate != null">scan_date,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="county != null">county,</if>
            <if test="ip != null">ip,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="certificateId != null">#{certificateId},</if>
            <if test="fullNumber != null">#{fullNumber},</if>
            <if test="entId != null">#{entId},</if>
            <if test="scanDate != null">#{scanDate},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="county != null">#{county},</if>
            <if test="ip != null">#{ip},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateScanRecord" parameterType="com.jkr.project.argi.domain.ScanRecord">
        update bas_scan_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateId != null">certificate_id = #{certificateId},</if>
            <if test="fullNumber != null">full_number = #{fullNumber},</if>
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="scanDate != null">scan_date = #{scanDate},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="county != null">county = #{county},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_scan_record set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_scan_record set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteScanRecordById" parameterType="Long">
        delete from bas_scan_record where id = #{id}
    </delete>

    <delete id="deleteScanRecordByIds" parameterType="String">
        delete from bas_scan_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="findEntScanList" resultMap="ScanRecordResult">
        SELECT
        a.id, a.scan_date, a.full_number, c.product_name, c.ent_name, c.id as certificate_id
        FROM bas_scan_record a
        LEFT JOIN bas_certificate_no b ON a.full_number = b.full_number AND b.del_flag = '1'
        LEFT JOIN bas_certificate c ON c.id = b.certificate_id AND c.del_flag = '1'
        <where>
            and a.del_flag = '1'
            <if test="entId != null">
                and a.ent_id = #{entId}
            </if>
            <if test="productName != null and productName != ''">
                and c.product_name like CONCAT('%', #{productName}, '%')
            </if>
            <if test="scanDate != null">
                and DATE_FORMAT(a.scan_date,'%Y-%m-%d') = DATE_FORMAT(#{scanDate},'%Y-%m-%d')
            </if>
            <if test="beginScanDate != null">
                AND
                <![CDATA[ DATE_FORMAT(a.scan_date,'%Y-%m-%d') >=DATE_FORMAT(#{beginScanDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endScanDate != null">
                AND
                <![CDATA[ DATE_FORMAT(a.scan_date,'%Y-%m-%d') <=DATE_FORMAT(#{endScanDate},'%Y-%m-%d') ]]>
            </if>
        </where>
        <choose>
            <when test="addProductOrder == 1">
                ORDER BY c.product_name asc
            </when>
            <when test="addProductOrder == 2">
                ORDER BY c.product_name desc
            </when>
            <when test="addScanDateOrder == 1">
                ORDER BY a.scan_date asc
            </when>
            <when test="addScanDateOrder == 2">
                ORDER BY a.scan_date desc
            </when>
            <otherwise>
                ORDER BY a.update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="findByCertificateId" resultMap="ScanRecordResult">
        <include refid="selectScanRecordVo"/>
        <where>
            certificate_id=#{certificateId}
            AND del_flag = '1'
        </where>
        ORDER BY scan_date
    </select>

    <select id="getCountAmountByCertificateId" resultType="map">
        SELECT
        a.certificate_id AS "certificateId",
        COUNT(a.certificate_id) AS "scanCountAmount",
        MIN(scan_date) AS "scanMinDate"
        FROM bas_scan_record a
        <where>
            a.certificate_id=#{certificateId}
            AND a.del_flag = '1'
        </where>
        GROUP BY a.certificate_id
    </select>

    <select id="findByFullNumber" resultMap="ScanRecordResult">
        <include refid="selectScanRecordVo"/>
        <where>
            full_number=#{fullNumber}
            AND del_flag = '1'
        </where>
        ORDER BY scan_date asc
    </select>

    <!--溯源统计查询列表-->
    <select id="findTraceStatisticList" resultMap="ScanRecordResult">
        SELECT DISTINCT
        a.id,
        a.certificate_id,
        a.full_number,
        a.ent_id,
        a.scan_date,
        a.city,
        c.begin_serial_number,
        c.end_serial_number,
        c.ent_name,
        c.product_name,
        c.product_sort_name
        FROM
        bas_scan_record a
        LEFT JOIN bas_certificate_no b ON a.full_number = b.full_number AND b.del_flag = '1'
        LEFT JOIN bas_certificate c ON c.id = b.certificate_id AND c.del_flag = '1'
        <where>
            a.del_flag = '1'
            AND a.full_number IS NOT NULL
            AND a.full_number != ''
            AND a.city IS NOT NULL
            AND a.city != ''
            AND a.scan_date IS NOT NULL
            <if test="entAreaCode != null and entAreaCode != ''">
                AND c.ent_county LIKE #{entAreaCode}"%"
            </if>
            <if test="batchNo != null and batchNo != ''">
                AND a.full_number like "%"#{batchNo}"%"
            </if>
            <if test="entName != null and entName != ''">
                AND c.ent_name like "%"#{entName}"%"
            </if>
            <if test="productName != null and productName != ''">
                AND c.product_name LIKE concat('%',#{productName},'%')
            </if>
            <if test="productSortCode != null and productSortCode != ''">
                AND c.product_sort_code = #{productSortCode}
            </if>
            <if test="scanAddress != null and scanAddress != ''">
                AND a.city like "%"#{scanAddress}"%"
            </if>
            <if test="params.areaWhere!='' and params.areaWhere!=null">
                ${params.areaWhere}
            </if>
            <if test="beginScanDate != null and beginScanDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.scan_date,'%Y-%m-%d') >=DATE_FORMAT(#{beginScanDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endScanDate != null and endScanDate != '' ">
                AND
                <![CDATA[ DATE_FORMAT(a.scan_date,'%Y-%m-%d') <=DATE_FORMAT(#{endScanDate},'%Y-%m-%d') ]]>
            </if>
        </where>
        ORDER BY
        a.scan_date DESC
    </select>
</mapper>
