<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.ProductStorageMapper">

    <resultMap type="com.jkr.project.argi.domain.ProductStorage" id="ProductStorageResult">
        <result property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="productId" column="product_id"/>
        <result property="storageTypeCode" column="storage_type_code"/>
        <result property="storageTypeName" column="storage_type_name"/>
        <result property="storageDays" column="storage_days"/>
        <result property="storageUnit" column="storage_unit"/>
        <result property="instruction" column="instruction"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectProductStorageVo">
        select id,
               ent_id,
               product_id,
               storage_type_code,
               storage_type_name,
               storage_days,
               storage_unit,
               instruction,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from bas_product_storage
    </sql>

    <select id="selectProductStorageList" parameterType="com.jkr.project.argi.domain.ProductStorage"
            resultMap="ProductStorageResult">
        <include refid="selectProductStorageVo"/>
        <where>
            <if test="entId != null  and entId != ''">
                and ent_id = #{entId}
            </if>
            <if test="productId != null  and productId != ''">
                and product_id = #{productId}
            </if>
            and del_flag = '1'
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectProductStorageById" parameterType="Long" resultMap="ProductStorageResult">
        <include refid="selectProductStorageVo"/>
        where id = #{id}
    </select>

    <insert id="insertProductStorage" parameterType="com.jkr.project.argi.domain.ProductStorage">
        insert into bas_product_storage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="entId != null">ent_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="storageTypeCode != null">storage_type_code,</if>
            <if test="storageTypeName != null">storage_type_name,</if>
            <if test="storageDays != null">storage_days,</if>
            <if test="storageUnit != null">storage_unit,</if>
            <if test="instruction != null">instruction,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="entId != null">#{entId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="storageTypeCode != null">#{storageTypeCode},</if>
            <if test="storageTypeName != null">#{storageTypeName},</if>
            <if test="storageDays != null">#{storageDays},</if>
            <if test="storageUnit != null">#{storageUnit},</if>
            <if test="instruction != null">#{instruction},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateProductStorage" parameterType="com.jkr.project.argi.domain.ProductStorage">
        update bas_product_storage
        <trim prefix="SET" suffixOverrides=",">
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="storageTypeCode != null">storage_type_code = #{storageTypeCode},</if>
            <if test="storageTypeName != null">storage_type_name = #{storageTypeName},</if>
            <if test="storageDays != null">storage_days = #{storageDays},</if>
            <if test="storageUnit != null">storage_unit = #{storageUnit},</if>
            <if test="instruction != null">instruction = #{instruction},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_product_storage set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_product_storage
        set del_flag = REPLACE(unix_timestamp(current_timestamp(3)), '.', '')
        where id = #{id}
    </update>

    <delete id="deleteProductStorageById" parameterType="Long">
        delete
        from bas_product_storage
        where id = #{id}
    </delete>

    <delete id="deleteProductStorageByIds" parameterType="String">
        delete from bas_product_storage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
