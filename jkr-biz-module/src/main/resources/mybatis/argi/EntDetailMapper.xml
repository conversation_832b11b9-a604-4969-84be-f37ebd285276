<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.EntDetailMapper">

    <resultMap type="com.jkr.project.argi.domain.EntDetail" id="EntDetailResult">
            <result property="id"    column="id"    />
            <result property="entId"    column="ent_id"    />
            <result property="productId"    column="product_id"    />
            <result property="productName"    column="product_name"    />
            <result property="scale"    column="scale"    />
            <result property="breedingUnitCode"    column="breeding_unit_code"    />
            <result property="breedingUnitLabel"    column="breeding_unit_label"    />
            <result property="outputUnitCode"    column="output_unit_code"    />
            <result property="outputUnitLabel"    column="output_unit_label"    />
            <result property="annualValue"    column="annual_value"    />
            <result property="annualOutput"    column="annual_output"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEntDetailVo">
        select id, ent_id, product_id, product_name, scale, breeding_unit_code, breeding_unit_label, output_unit_code, output_unit_label, annual_value, annual_output, del_flag, create_by, create_time, update_by, update_time, remark from bas_ent_detail
    </sql>
    <sql id="basEntDetailColumns">
        a.id AS "id",
		a.ent_id AS "entId",
		a.product_id AS "productId",
		a.product_name AS "productName",
		a.scale AS "scale",
		a.breeding_unit_code AS "breedingUnitCode",
		a.breeding_unit_label AS "breedingUnitLabel",
		a.output_unit_code AS "outputUnitCode",
		a.output_unit_label AS "outputUnitLabel",
		a.annual_value AS "annualValue",
		a.annual_output AS "annualOutput",
		a.create_by AS "createBy.id",
		a.create_date AS "createDate",
		a.update_by AS "updateBy.id",
		a.update_date AS "updateDate",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag"
    </sql>
    <sql id="basEntDetailJoins">
    </sql>

    <select id="selectEntDetailList" parameterType="com.jkr.project.argi.domain.EntDetail" resultMap="EntDetailResult">
        <include refid="selectEntDetailVo"/>
        <where>
                        <if test="entId != null  and entId != ''"> and ent_id = #{entId}</if>
                        <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
                        <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
                        <if test="scale != null "> and scale = #{scale}</if>
                        <if test="breedingUnitCode != null  and breedingUnitCode != ''"> and breeding_unit_code = #{breedingUnitCode}</if>
                        <if test="breedingUnitLabel != null  and breedingUnitLabel != ''"> and breeding_unit_label = #{breedingUnitLabel}</if>
                        <if test="outputUnitCode != null  and outputUnitCode != ''"> and output_unit_code = #{outputUnitCode}</if>
                        <if test="outputUnitLabel != null  and outputUnitLabel != ''"> and output_unit_label = #{outputUnitLabel}</if>
                        <if test="annualValue != null "> and annual_value = #{annualValue}</if>
                        <if test="annualOutput != null "> and annual_output = #{annualOutput}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectEntDetailById" parameterType="Long" resultMap="EntDetailResult">
            <include refid="selectEntDetailVo"/>
            where id = #{id}
    </select>

    <insert id="insertEntDetail" parameterType="com.jkr.project.argi.domain.EntDetail">
        insert into bas_ent_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="entId != null">ent_id,</if>
                    <if test="productId != null">product_id,</if>
                    <if test="productName != null">product_name,</if>
                    <if test="scale != null">scale,</if>
                    <if test="breedingUnitCode != null">breeding_unit_code,</if>
                    <if test="breedingUnitLabel != null">breeding_unit_label,</if>
                    <if test="outputUnitCode != null">output_unit_code,</if>
                    <if test="outputUnitLabel != null">output_unit_label,</if>
                    <if test="annualValue != null">annual_value,</if>
                    <if test="annualOutput != null">annual_output,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="entId != null">#{entId},</if>
                    <if test="productId != null">#{productId},</if>
                    <if test="productName != null">#{productName},</if>
                    <if test="scale != null">#{scale},</if>
                    <if test="breedingUnitCode != null">#{breedingUnitCode},</if>
                    <if test="breedingUnitLabel != null">#{breedingUnitLabel},</if>
                    <if test="outputUnitCode != null">#{outputUnitCode},</if>
                    <if test="outputUnitLabel != null">#{outputUnitLabel},</if>
                    <if test="annualValue != null">#{annualValue},</if>
                    <if test="annualOutput != null">#{annualOutput},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateEntDetail" parameterType="com.jkr.project.argi.domain.EntDetail">
        update bas_ent_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="entId != null">ent_id = #{entId},</if>
                    <if test="productId != null">product_id = #{productId},</if>
                    <if test="productName != null">product_name = #{productName},</if>
                    <if test="scale != null">scale = #{scale},</if>
                    <if test="breedingUnitCode != null">breeding_unit_code = #{breedingUnitCode},</if>
                    <if test="breedingUnitLabel != null">breeding_unit_label = #{breedingUnitLabel},</if>
                    <if test="outputUnitCode != null">output_unit_code = #{outputUnitCode},</if>
                    <if test="outputUnitLabel != null">output_unit_label = #{outputUnitLabel},</if>
                    <if test="annualValue != null">annual_value = #{annualValue},</if>
                    <if test="annualOutput != null">annual_output = #{annualOutput},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_ent_detail set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_ent_detail set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteEntDetailById" parameterType="Long">
        delete from bas_ent_detail where id = #{id}
    </delete>

    <delete id="deleteEntDetailByIds" parameterType="String">
        delete from bas_ent_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteByEntId">
        DELETE FROM bas_ent_detail WHERE ent_id = #{entId}
    </delete>
    <select id="findDetailList" resultType="com.jkr.project.argi.domain.EntDetail">
        SELECT
        <include refid="basEntDetailColumns"/>
        FROM bas_ent_detail a
        <include refid="basEntDetailJoins"/>
        <where>
            a.del_flag = '1'
            <if test="null != entId and '' != entId">
                AND a.ent_id= #{entId}
            </if>
        </where>
    </select>
</mapper>
