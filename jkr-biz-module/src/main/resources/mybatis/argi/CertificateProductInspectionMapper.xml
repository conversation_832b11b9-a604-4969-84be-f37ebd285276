<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateProductInspectionMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateProductInspection" id="CertificateProductInspectionResult">
        <result property="id"    column="id"    />
        <result property="certificateId"    column="certificate_id"    />
        <result property="productInspectionId"    column="product_inspection_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCertificateProductInspectionVo">
        select id, certificate_id, product_inspection_id, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate_product_inspection
    </sql>

    <select id="selectCertificateProductInspectionList" parameterType="com.jkr.project.argi.domain.CertificateProductInspection" resultMap="CertificateProductInspectionResult">
        <include refid="selectCertificateProductInspectionVo"/>
        <where>
            <if test="certificateId != null  and certificateId != ''"> and certificate_id = #{certificateId}</if>
            <if test="productInspectionId != null  and productInspectionId != ''"> and product_inspection_id = #{productInspectionId}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectCertificateProductInspectionById" parameterType="Long" resultMap="CertificateProductInspectionResult">
        <include refid="selectCertificateProductInspectionVo"/>
        where id = #{id}
    </select>

    <insert id="insertCertificateProductInspection" parameterType="com.jkr.project.argi.domain.CertificateProductInspection">
        insert into bas_certificate_product_inspection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="certificateId != null">certificate_id,</if>
            <if test="productInspectionId != null">product_inspection_id,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="certificateId != null">#{certificateId},</if>
            <if test="productInspectionId != null">#{productInspectionId},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateProductInspection" parameterType="com.jkr.project.argi.domain.CertificateProductInspection">
        update bas_certificate_product_inspection
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateId != null">certificate_id = #{certificateId},</if>
            <if test="productInspectionId != null">product_inspection_id = #{productInspectionId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_product_inspection set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_product_inspection set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateProductInspectionById" parameterType="Long">
        delete from bas_certificate_product_inspection where id = #{id}
    </delete>

    <delete id="deleteCertificateProductInspectionByIds" parameterType="String">
        delete from bas_certificate_product_inspection where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getProductInspectionByCertificateId" resultType="java.lang.String">
        select
            b.inspection_situation
        from bas_certificate_product_inspection a
        left join bas_product_inspection b on a.product_inspection_id = b.id and b.del_flag ='1'
        left join sys_dict_data c on b.inspection_situation = c.dict_value and c.dict_type='inspection_situation_code' and c.status='0'
        where  a.certificate_id = #{certificateId} and a.del_flag ='1'
        order by c.dict_sort
    </select>

    <insert id="insertBatch" >
        INSERT INTO bas_certificate_product_inspection(
            id,
            certificate_id,
            product_inspection_id,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id},
            #{item.certificateId},
            #{item.productInspectionId},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            '1'
        )
        </foreach>
    </insert>

    <select id="getCertificateProductInspectionByCertificateId" resultType="com.jkr.project.argi.domain.CertificateProductInspection">
        select
            b.inspection_situation inspectionSituation,
            a.certificate_id,
            a.product_inspection_id
        from bas_certificate_product_inspection a
                 left join bas_product_inspection b on a.product_inspection_id = b.id and b.del_flag ='1'
                 left join sys_dict_data c on b.inspection_situation = c.dict_value and c.dict_type='inspection_situation_code' and c.status='0'
        where  a.certificate_id = #{certificateId} and a.del_flag ='1'
        order by c.dict_sort
    </select>
</mapper>
