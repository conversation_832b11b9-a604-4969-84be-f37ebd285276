<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.DeviceParameterMapper">

    <resultMap type="com.jkr.project.argi.domain.DeviceParameter" id="DeviceParameterResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="type"    column="type"    />
        <result property="command"    column="command"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectDeviceParameterVo">
        select id, name, type, command, image_url, create_by, create_time, update_by, update_time, remark, del_flag from bas_device_parameter
    </sql>

    <select id="selectDeviceParameterList" parameterType="com.jkr.project.argi.domain.DeviceParameter" resultMap="DeviceParameterResult">
        <include refid="selectDeviceParameterVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="command != null  and command != ''"> and command = #{command}</if>
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            and del_flag = '1'
        </where>
        order by update_time desc
    </select>

    <select id="selectDeviceParameterById" parameterType="String" resultMap="DeviceParameterResult">
        <include refid="selectDeviceParameterVo"/>
        where id = #{id}
    </select>

    <insert id="insertDeviceParameter" parameterType="com.jkr.project.argi.domain.DeviceParameter">
        insert into bas_device_parameter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="command != null">command,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            del_flag,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="command != null">#{command},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            '1',
        </trim>
    </insert>

    <update id="updateDeviceParameter" parameterType="com.jkr.project.argi.domain.DeviceParameter">
        update bas_device_parameter
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="command != null">command = #{command},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_device_parameter set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="String">
        update bas_device_parameter set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteDeviceParameterById" parameterType="String">
        delete from bas_device_parameter where id = #{id}
    </delete>

    <delete id="deleteDeviceParameterByIds" parameterType="String">
        delete from bas_device_parameter where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
