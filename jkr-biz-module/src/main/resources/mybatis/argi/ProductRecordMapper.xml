<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.ProductRecordMapper">

    <resultMap type="com.jkr.project.argi.domain.ProductRecord" id="ProductRecordResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="sampleName"    column="sample_name"    />
        <result property="sampleNo"    column="sample_no"    />
        <result property="queryCodeUrl"    column="query_code_url"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <association property="product" javaType="com.jkr.project.argi.domain.Product">
            <result property="id" column="product_id"/>
            <result property="name" column="productName"/>
            <result property="entId" column="productEntId"/>
        </association>
    </resultMap>

    <sql id="selectProductRecordVo">
        select id, product_id, sample_name, sample_no, query_code_url, del_flag, create_by, create_time, update_by, update_time, remark from bas_product_record
    </sql>

    <sql id="Base_Column_List">
        <trim>
            ${alias}.id, ${alias}.product_id, ${alias}.sample_name, ${alias}.sample_no, ${alias}.query_code_url,
            ${alias}.del_flag, ${alias}.create_by, ${alias}.create_time, ${alias}.update_by, ${alias}.update_time, ${alias}.remark
        </trim>
    </sql>

    <select id="selectProductRecordList" parameterType="com.jkr.project.argi.domain.ProductRecord" resultMap="ProductRecordResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,p.name as productName
        ,p.ent_id as productEntId
        from bas_product_record a
        left join bas_product p on p.id=a.product_id
        <where>
            <if test=" null != productId and '' != productId ">
                AND a.product_id = #{productId}
            </if>
            <if test=" null != startYearMonth and '' != startYearMonth ">
                AND DATE_FORMAT(a.create_time,'%Y-%m') <![CDATA[ >= ]]>  #{startYearMonth}
            </if>
            and a.del_flag = '1'
        </where>
        ORDER BY a.update_time DESC
    </select>

    <select id="selectProductRecordById" parameterType="Long" resultMap="ProductRecordResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,p.name as productName
        ,p.ent_id as productEntId
        from bas_product_record a
        left join bas_product p on p.id=a.product_id
        where id = #{id}
    </select>

    <insert id="insertProductRecord" parameterType="com.jkr.project.argi.domain.ProductRecord">
        insert into bas_product_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="productId != null">product_id,</if>
            <if test="sampleName != null">sample_name,</if>
            <if test="sampleNo != null">sample_no,</if>
            <if test="queryCodeUrl != null">query_code_url,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="productId != null">#{productId},</if>
            <if test="sampleName != null">#{sampleName},</if>
            <if test="sampleNo != null">#{sampleNo},</if>
            <if test="queryCodeUrl != null">#{queryCodeUrl},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateProductRecord" parameterType="com.jkr.project.argi.domain.ProductRecord">
        update bas_product_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="sampleName != null">sample_name = #{sampleName},</if>
            <if test="sampleNo != null">sample_no = #{sampleNo},</if>
            <if test="queryCodeUrl != null">query_code_url = #{queryCodeUrl},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_product_record set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_product_record set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteProductRecordById" parameterType="Long">
        delete from bas_product_record where id = #{id}
    </delete>

    <delete id="deleteProductRecordByIds" parameterType="String">
        delete from bas_product_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getBySampleNo" resultMap="ProductRecordResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,p.name as productName
        ,p.ent_id as productEntId
        from bas_product_record a
        left join bas_product p on p.id=a.product_id
        WHERE a.sample_no = #{sampleNo}
    </select>

    <select id="findListByProductId" resultMap="ProductRecordResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,p.name as productName
        ,p.ent_id as productEntId
        from bas_product_record a
        left join bas_product p on p.id=a.product_id
        WHERE a.del_flag = '1'
        <if test=" '' != productId and null != productId">
            AND a.product_id = #{productId}
        </if>
        order by a.create_time desc
    </select>

    <select id="findListValidDetection" resultMap="ProductRecordResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,p.name as productName
        ,p.ent_id as productEntId
        from bas_product_record a
        left join bas_product p on p.id=a.product_id
        inner join bas_detection d on d.sample_no=a.sample_no
        <where>
            and a.del_flag = '1'
            <if test=" null != productId and '' != productId ">
                AND a.product_id = #{productId}
            </if>
            <if test=" null != startYearMonth and '' != startYearMonth ">
                AND DATE_FORMAT(d.test_date,'%Y-%m') <![CDATA[ >= ]]>  #{startYearMonth}
            </if>
        </where>
        group by a.id
        ORDER BY a.update_time DESC
    </select>
</mapper>
