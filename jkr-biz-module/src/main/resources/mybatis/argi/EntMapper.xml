<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.EntMapper">

    <resultMap type="com.jkr.project.argi.domain.Ent" id="EntResult">
            <result property="id"    column="id"    />
            <result property="name"    column="name"    />
            <result property="businessType"    column="business_type"    />
            <result property="entType"    column="ent_type"    />
            <result property="mainType"    column="main_type"    />
            <result property="identityType"    column="identity_type"    />
            <result property="farmType"    column="farm_type"    />
            <result property="socialCode"    column="social_code"    />
            <result property="cardNo"    column="card_no"    />
            <result property="cardDetail"    column="card_detail"    />
            <result property="legalPerson"    column="legal_person"    />
            <result property="contacts"    column="contacts"    />
            <result property="contactsPhone"    column="contacts_phone"    />
            <result property="province"    column="province"    />
            <result property="city"    column="city"    />
            <result property="county"    column="county"    />
            <result property="town"    column="town"    />
            <result property="village"    column="village"    />
            <result property="address"    column="address"    />
            <result property="detail"    column="detail"    />
            <result property="lng"    column="lng"    />
            <result property="lat"    column="lat"    />
            <result property="companyIntroduction"    column="company_introduction"    />
            <result property="entHonor"    column="ent_honor"    />
            <result property="submitDate"    column="submit_date"    />
            <result property="examineStatus"    column="examine_status"    />
            <result property="examineMan"    column="examine_man"    />
            <result property="examineOpinion"    column="examine_opinion"    />
            <result property="examineDate"    column="examine_date"    />
            <result property="tableName"    column="table_name"    />
            <result property="tableId"    column="table_id"    />
            <result property="basicFlag"    column="basic_flag"    />
            <result property="basicEnterFlag"    column="basic_enter_flag"    />
            <result property="certificateAmount"    column="certificate_amount"    />
            <result property="certificatePrintAmount"    column="certificate_print_amount"    />
            <result property="certificatePrintDate"    column="certificate_print_date"    />
            <result property="inspectionWriteAmount"    column="inspection_write_amount"    />
            <result property="changeStatus"    column="change_status"    />
            <result property="changeViewFlag"    column="change_view_flag"    />
            <result property="changeOpinion"    column="change_opinion"    />
            <result property="frozenFlag"    column="frozen_flag"    />
            <result property="userId"    column="user_id"    />
            <result property="dataScope"    column="data_scope"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
            <result property="lastModified"    column="last_modified"    />
    </resultMap>

    <sql id="entColumns">
        a.id AS "id",
		a.name AS "name",
		a.business_type AS "businessType",
		a.ent_type AS "entType",
		a.main_type AS "mainType",
		a.identity_type AS "identityType",
		a.farm_type AS "farmType",
		a.social_code AS "socialCode",
		a.card_no AS "cardNo",
		a.card_detail AS "cardDetail",
		a.legal_person AS "legalPerson",
		a.contacts AS "contacts",
		a.contacts_phone AS "contactsPhone",
		a.province AS "province",
		a.city AS "city",
		a.county AS "county",
		a.town AS "town",
		a.village AS "village",
		a.address As "address",
		a.detail AS "detail",
		a.lng AS "lng",
		a.lat AS "lat",
		a.company_introduction AS "companyIntroduction",
		a.submit_date AS "submitDate",
		a.examine_status AS "examineStatus",
		a.examine_man AS "examineMan",
		a.examine_opinion AS "examineOpinion",
		a.examine_date AS "examineDate",
		a.ent_honor AS "entHonor",
		a.table_name AS "tableName",
		a.table_id AS "tableId",
		a.basic_flag AS "basicFlag",
		a.basic_enter_flag AS "basicEnterFlag",
		a.certificate_amount AS "certificateAmount",
		a.certificate_print_amount AS "certificatePrintAmount",
		a.certificate_print_date AS "certificatePrintDate",
		a.inspection_write_amount AS "inspectionWriteAmount",
		a.change_status AS "changeStatus",
		a.change_view_flag AS "changeViewFlag",
		a.change_opinion AS "changeOpinion",
		a.frozen_flag AS "frozenFlag",
		a.create_by AS "createBy.id",
		a.create_time AS "createTime",
		a.update_by AS "updateBy.id",
		a.update_time AS "updateTime",
		a.remarks AS "remarks",
		a.del_flag AS "delFlag",
		a.user_id AS "userId",
		a.data_scope AS "dataScope"
    </sql>
    <sql id="entJoins">
        left join bas_autograph ba on ba.ent_id=a.id and ba.del_flag=1
    </sql>
    <sql id="productJoins">
        left join bas_product p on p.ent_id=a.id and p.del_flag=1
    </sql>

    <sql id="selectEntVo">
        select id, name, business_type, ent_type, main_type, identity_type, farm_type, social_code, card_no, card_detail, legal_person, contacts, contacts_phone, province, city, county, town, village, address, detail, lng, lat, company_introduction, ent_honor, submit_date, examine_status, examine_man, examine_opinion, examine_date, table_name, table_id, basic_flag, basic_enter_flag, certificate_amount, certificate_print_amount, certificate_print_date, inspection_write_amount, change_status, change_view_flag, change_opinion, frozen_flag, user_id, data_scope, del_flag, create_by, create_time, update_by, update_time, remark, last_modified from bas_ent
    </sql>
    <select id="selectEntList" parameterType="com.jkr.project.argi.domain.Ent" resultMap="EntResult">
        <include refid="selectEntVo"/>
        <where>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
                        <if test="entType != null  and entType != ''"> and ent_type = #{entType}</if>
                        <if test="mainType != null  and mainType != ''"> and main_type = #{mainType}</if>
                        <if test="identityType != null  and identityType != ''"> and identity_type = #{identityType}</if>
                        <if test="farmType != null  and farmType != ''"> and farm_type = #{farmType}</if>
                        <if test="socialCode != null  and socialCode != ''"> and social_code = #{socialCode}</if>
                        <if test="cardNo != null  and cardNo != ''"> and card_no = #{cardNo}</if>
                        <if test="cardDetail != null  and cardDetail != ''"> and card_detail = #{cardDetail}</if>
                        <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
                        <if test="contacts != null  and contacts != ''"> and contacts = #{contacts}</if>
                        <if test="contactsPhone != null  and contactsPhone != ''"> and contacts_phone = #{contactsPhone}</if>
                        <if test="province != null  and province != ''"> and province = #{province}</if>
                        <if test="city != null  and city != ''"> and city = #{city}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="town != null  and town != ''"> and town = #{town}</if>
                        <if test="village != null  and village != ''"> and village = #{village}</if>
                        <if test="address != null  and address != ''"> and address = #{address}</if>
                        <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
                        <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
                        <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
                        <if test="companyIntroduction != null  and companyIntroduction != ''"> and company_introduction = #{companyIntroduction}</if>
                        <if test="entHonor != null  and entHonor != ''"> and ent_honor = #{entHonor}</if>
                        <if test="submitDate != null "> and submit_date = #{submitDate}</if>
                        <if test="examineStatus != null  and examineStatus != ''"> and examine_status = #{examineStatus}</if>
                        <if test="examineMan != null  and examineMan != ''"> and examine_man = #{examineMan}</if>
                        <if test="examineOpinion != null  and examineOpinion != ''"> and examine_opinion = #{examineOpinion}</if>
                        <if test="examineDate != null "> and examine_date = #{examineDate}</if>
                        <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
                        <if test="tableId != null  and tableId != ''"> and table_id = #{tableId}</if>
                        <if test="basicFlag != null  and basicFlag != ''"> and basic_flag = #{basicFlag}</if>
                        <if test="basicEnterFlag != null  and basicEnterFlag != ''"> and basic_enter_flag = #{basicEnterFlag}</if>
                        <if test="certificateAmount != null "> and certificate_amount = #{certificateAmount}</if>
                        <if test="certificatePrintAmount != null "> and certificate_print_amount = #{certificatePrintAmount}</if>
                        <if test="certificatePrintDate != null "> and certificate_print_date = #{certificatePrintDate}</if>
                        <if test="inspectionWriteAmount != null "> and inspection_write_amount = #{inspectionWriteAmount}</if>
                        <if test="changeStatus != null  and changeStatus != ''"> and change_status = #{changeStatus}</if>
                        <if test="changeViewFlag != null  and changeViewFlag != ''"> and change_view_flag = #{changeViewFlag}</if>
                        <if test="changeOpinion != null  and changeOpinion != ''"> and change_opinion = #{changeOpinion}</if>
                        <if test="frozenFlag != null  and frozenFlag != ''"> and frozen_flag = #{frozenFlag}</if>
                        <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
                        <if test="dataScope != null  and dataScope != ''"> and data_scope = #{dataScope}</if>
                        <if test="lastModified != null "> and last_modified = #{lastModified}</if>
                        <if test="beginCreateDate != null">
                            AND Date(create_time) <![CDATA[ >= ]]>  Date(#{beginCreateDate})
                        </if>
                        <if test="endCreateDate != null">
                            AND Date(create_time)<![CDATA[ <= ]]> Date(#{endCreateDate})
                        </if>
                        <if test="beginCertificatePrintDate != null">
                            AND Date(certificate_print_date) <![CDATA[ >= ]]>  Date(#{beginCertificatePrintDate})
                        </if>
                        <if test="endCertificatePrintDate != null">
                            AND Date(certificate_print_date)<![CDATA[ <= ]]> Date(#{endCertificatePrintDate})
                        </if>
            and del_flag = '1'
        </where>
    </select>

    <select id="findListQuick" resultType="com.jkr.project.argi.domain.Ent">
        SELECT
        a.id AS "id",
        a.name AS "name",
        a.business_type AS "businessType",
        a.main_type AS "mainType",
        a.identity_type AS "identityType",
        a.legal_person AS "legalPerson",
        a.contacts AS "contacts",
        a.contacts_phone AS "contactsPhone",
        a.address As "address",
        a.detail AS "detail",
        a.create_time AS "createTime",
        a.submit_date AS "submitDate",
        a.certificate_amount AS "certificateAmount",
        a.certificate_print_amount AS "certificatePrintAmount",
        a.inspection_write_amount AS "inspectionWriteAmount",
        a.ent_type AS "entType",
        a.certificate_print_date AS "certificatePrintDate",
        a.card_no AS "cardNo",
        a.social_code AS "socialCode",
        a.frozen_flag AS "frozenFlag",
        a.examine_man AS "examineMan",
        a.examine_status AS "examineStatus",
        a.examine_date AS "examineDate",
        GROUP_CONCAT(DISTINCT p.product_sort_name) as productSortName
        FROM bas_ent a
        <include refid="productJoins"/>
        left join sys_dept d on d.dept_id=a.id
        <where>
            <if test="userId != null and userId != ''">
                and a.user_id = #{userId}
            </if>
            <if test="dataScope != null and dataScope != ''">
                and a.data_scope = #{dataScope}
            </if>
            <if test="entType !=null and entType !=''">
                AND a.ent_type = #{entType}
            </if>
            <if test="name !=null and name !=''">
                AND a.name LIKE concat('%',#{name},'%')
            </if>
            <if test="businessType !=null and businessType !=''">
                AND a.business_type = #{businessType}
            </if>
            <if test="county !=null and county !=''">
                AND a.county LIKE concat(#{county},'%')
            </if>
            <if test="loginAreaCode !=null and loginAreaCode !=''">
                AND d.area_code LIKE concat(#{loginAreaCode},'%')
            </if>
            <if test="queryAreaCode !=null and queryAreaCode !=''">
                AND d.area_code LIKE concat(#{queryAreaCode},'%')
            </if>
<!--            <if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">-->
<!--                ${sqlMap.areaWhere}-->
<!--            </if>-->
            <if test="cardNo !=null and cardNo !=''">
                AND a.card_no = #{cardNo}
            </if>
            <!-- 审核 -->
            <if test="examineStatus !=null and examineStatus !=''">
                AND a.examine_status = #{examineStatus}
            </if>
            <if test="remark !=null and remark !=''">
                AND examine_status	&lt; #{remark}
            </if>
            <if test="beginSubmitDate !=null">
                AND a.submit_date &gt;= #{beginSubmitDate}
            </if>
            <if test="endSubmitDate !=null">
                AND a.submit_date &lt;= #{endSubmitDate}
            </if>
            <if test="frozenFlag !=null and frozenFlag !=''">
                AND a.frozen_flag = #{frozenFlag}
            </if>
            <if test="identityType !=null and identityType !=''">
                AND a.identity_type = #{identityType}
            </if>
            <if test="productSortName !=null and productSortName !=''">
                AND p.product_sort_name = #{productSortName}
            </if>
            <if test="beginCreateDate != null">
                AND Date(a.create_time) <![CDATA[ >= ]]>  Date(#{beginCreateDate})
            </if>
            <if test="endCreateDate != null">
                AND Date(a.create_time)<![CDATA[ <= ]]> Date(#{endCreateDate})
            </if>
            <if test="beginCertificatePrintDate != null">
                AND Date(a.certificate_print_date) <![CDATA[ >= ]]>  Date(#{beginCertificatePrintDate})
            </if>
            <if test="endCertificatePrintDate != null">
                AND Date(a.certificate_print_date)<![CDATA[ <= ]]> Date(#{endCertificatePrintDate})
            </if>
            AND a.del_flag = 1
        </where>
        group by a.id
        <choose>
            <when test="orderBy != null and orderBy != ''">
                <choose>
                    <when test="orderBy == 'certificate_amount ASC'">
                        ORDER BY a.certificate_amount ASC
                    </when>
                    <when test="orderBy == 'certificate_amount DESC'">
                        ORDER BY a.certificate_amount DESC
                    </when>
                    <when test="orderBy == 'certificate_print_amount ASC'">
                        ORDER BY a.certificate_print_amount ASC
                    </when>
                    <when test="orderBy == 'certificate_print_amount DESC'">
                        ORDER BY a.certificate_print_amount DESC
                    </when>
                    <when test="orderBy == 'certificate_print_date ASC'">
                        ORDER BY a.certificate_print_date ASC
                    </when>
                    <when test="orderBy == 'certificate_print_date DESC'">
                        ORDER BY a.certificate_print_date DESC
                    </when>
                    <when test="orderBy == 'inspection_write_amount DESC'">
                        ORDER BY a.inspection_write_amount DESC
                    </when>
                    <when test="orderBy == 'inspection_write_amount ASC'">
                        ORDER BY a.inspection_write_amount ASC
                    </when>
                    <when test="orderBy == 'certificateAmount'">
                        ORDER BY a.certificate_print_amount desc,certificate_amount desc,a.update_time desc
                    </when>
                </choose>
            </when>

            <otherwise>
                <choose>
                    <when test="exportFlag == 'export'">
                        ORDER BY a.certificate_print_amount desc,certificate_amount desc,a.update_time desc
                    </when>
                    <otherwise>
                        ORDER BY FIELD(a.examine_status,0,-1,1),a.update_time DESC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

    <select id="selectEntById" parameterType="Long" resultMap="EntResult">
            <include refid="selectEntVo"/>
            where id = #{id}
    </select>

    <insert id="insertEnt" parameterType="com.jkr.project.argi.domain.Ent">
        insert into bas_ent
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="name != null">name,</if>
                    <if test="businessType != null">business_type,</if>
                    <if test="entType != null">ent_type,</if>
                    <if test="mainType != null">main_type,</if>
                    <if test="identityType != null">identity_type,</if>
                    <if test="farmType != null">farm_type,</if>
                    <if test="socialCode != null">social_code,</if>
                    <if test="cardNo != null">card_no,</if>
                    <if test="cardDetail != null">card_detail,</if>
                    <if test="legalPerson != null">legal_person,</if>
                    <if test="contacts != null">contacts,</if>
                    <if test="contactsPhone != null">contacts_phone,</if>
                    <if test="province != null">province,</if>
                    <if test="city != null">city,</if>
                    <if test="county != null">county,</if>
                    <if test="town != null">town,</if>
                    <if test="village != null">village,</if>
                    <if test="address != null">address,</if>
                    <if test="detail != null">detail,</if>
                    <if test="lng != null">lng,</if>
                    <if test="lat != null">lat,</if>
                    <if test="companyIntroduction != null">company_introduction,</if>
                    <if test="entHonor != null">ent_honor,</if>
                    <if test="submitDate != null">submit_date,</if>
                    <if test="examineStatus != null">examine_status,</if>
                    <if test="examineMan != null">examine_man,</if>
                    <if test="examineOpinion != null">examine_opinion,</if>
                    <if test="examineDate != null">examine_date,</if>
                    <if test="tableName != null">table_name,</if>
                    <if test="tableId != null">table_id,</if>
                    <if test="basicFlag != null">basic_flag,</if>
                    <if test="basicEnterFlag != null">basic_enter_flag,</if>
                    <if test="certificateAmount != null">certificate_amount,</if>
                    <if test="certificatePrintAmount != null">certificate_print_amount,</if>
                    <if test="certificatePrintDate != null">certificate_print_date,</if>
                    <if test="inspectionWriteAmount != null">inspection_write_amount,</if>
                    <if test="changeStatus != null">change_status,</if>
                    <if test="changeViewFlag != null">change_view_flag,</if>
                    <if test="changeOpinion != null">change_opinion,</if>
                    <if test="frozenFlag != null">frozen_flag,</if>
                    <if test="userId != null">user_id,</if>
                    <if test="dataScope != null">data_scope,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
                    <if test="lastModified != null">last_modified,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="name != null">#{name},</if>
                    <if test="businessType != null">#{businessType},</if>
                    <if test="entType != null">#{entType},</if>
                    <if test="mainType != null">#{mainType},</if>
                    <if test="identityType != null">#{identityType},</if>
                    <if test="farmType != null">#{farmType},</if>
                    <if test="socialCode != null">#{socialCode},</if>
                    <if test="cardNo != null">#{cardNo},</if>
                    <if test="cardDetail != null">#{cardDetail},</if>
                    <if test="legalPerson != null">#{legalPerson},</if>
                    <if test="contacts != null">#{contacts},</if>
                    <if test="contactsPhone != null">#{contactsPhone},</if>
                    <if test="province != null">#{province},</if>
                    <if test="city != null">#{city},</if>
                    <if test="county != null">#{county},</if>
                    <if test="town != null">#{town},</if>
                    <if test="village != null">#{village},</if>
                    <if test="address != null">#{address},</if>
                    <if test="detail != null">#{detail},</if>
                    <if test="lng != null">#{lng},</if>
                    <if test="lat != null">#{lat},</if>
                    <if test="companyIntroduction != null">#{companyIntroduction},</if>
                    <if test="entHonor != null">#{entHonor},</if>
                    <if test="submitDate != null">#{submitDate},</if>
                    <if test="examineStatus != null">#{examineStatus},</if>
                    <if test="examineMan != null">#{examineMan},</if>
                    <if test="examineOpinion != null">#{examineOpinion},</if>
                    <if test="examineDate != null">#{examineDate},</if>
                    <if test="tableName != null">#{tableName},</if>
                    <if test="tableId != null">#{tableId},</if>
                    <if test="basicFlag != null">#{basicFlag},</if>
                    <if test="basicEnterFlag != null">#{basicEnterFlag},</if>
                    <if test="certificateAmount != null">#{certificateAmount},</if>
                    <if test="certificatePrintAmount != null">#{certificatePrintAmount},</if>
                    <if test="certificatePrintDate != null">#{certificatePrintDate},</if>
                    <if test="inspectionWriteAmount != null">#{inspectionWriteAmount},</if>
                    <if test="changeStatus != null">#{changeStatus},</if>
                    <if test="changeViewFlag != null">#{changeViewFlag},</if>
                    <if test="changeOpinion != null">#{changeOpinion},</if>
                    <if test="frozenFlag != null">#{frozenFlag},</if>
                    <if test="userId != null">#{userId},</if>
                    <if test="dataScope != null">#{dataScope},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
                    <if test="lastModified != null">#{lastModified},</if>
        </trim>
    </insert>

    <update id="updateEnt" parameterType="com.jkr.project.argi.domain.Ent">
        update bas_ent
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">name = #{name},</if>
                    <if test="businessType != null">business_type = #{businessType},</if>
                    <if test="entType != null">ent_type = #{entType},</if>
                    <if test="mainType != null">main_type = #{mainType},</if>
                    <if test="identityType != null">identity_type = #{identityType},</if>
                    <if test="farmType != null">farm_type = #{farmType},</if>
                    <if test="socialCode != null">social_code = #{socialCode},</if>
                    <if test="cardNo != null">card_no = #{cardNo},</if>
                    <if test="cardDetail != null">card_detail = #{cardDetail},</if>
                    <if test="legalPerson != null">legal_person = #{legalPerson},</if>
                    <if test="contacts != null">contacts = #{contacts},</if>
                    <if test="contactsPhone != null">contacts_phone = #{contactsPhone},</if>
                    <if test="province != null">province = #{province},</if>
                    <if test="city != null">city = #{city},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="town != null">town = #{town},</if>
                    <if test="village != null">village = #{village},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="detail != null">detail = #{detail},</if>
                    <if test="lng != null">lng = #{lng},</if>
                    <if test="lat != null">lat = #{lat},</if>
                    <if test="companyIntroduction != null">company_introduction = #{companyIntroduction},</if>
                    <if test="entHonor != null">ent_honor = #{entHonor},</if>
                    <if test="submitDate != null">submit_date = #{submitDate},</if>
                    <if test="examineStatus != null">examine_status = #{examineStatus},</if>
                    <if test="examineMan != null">examine_man = #{examineMan},</if>
                    <if test="examineOpinion != null">examine_opinion = #{examineOpinion},</if>
                    <if test="examineDate != null">examine_date = #{examineDate},</if>
                    <if test="tableName != null">table_name = #{tableName},</if>
                    <if test="tableId != null">table_id = #{tableId},</if>
                    <if test="basicFlag != null">basic_flag = #{basicFlag},</if>
                    <if test="basicEnterFlag != null">basic_enter_flag = #{basicEnterFlag},</if>
                    <if test="certificateAmount != null">certificate_amount = #{certificateAmount},</if>
                    <if test="certificatePrintAmount != null">certificate_print_amount = #{certificatePrintAmount},</if>
                    <if test="certificatePrintDate != null">certificate_print_date = #{certificatePrintDate},</if>
                    <if test="inspectionWriteAmount != null">inspection_write_amount = #{inspectionWriteAmount},</if>
                    <if test="changeStatus != null">change_status = #{changeStatus},</if>
                    <if test="changeViewFlag != null">change_view_flag = #{changeViewFlag},</if>
                    <if test="changeOpinion != null">change_opinion = #{changeOpinion},</if>
                    <if test="frozenFlag != null">frozen_flag = #{frozenFlag},</if>
                    <if test="userId != null">user_id = #{userId},</if>
                    <if test="dataScope != null">data_scope = #{dataScope},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
                    <if test="lastModified != null">last_modified = #{lastModified},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_ent set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_ent set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteEntById" parameterType="Long">
        delete from bas_ent where id = #{id}
    </delete>

    <delete id="deleteEntByIds" parameterType="String">
        delete from bas_ent where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="getBySocialCodeOrCardNo" resultType="int">
        SELECT count(*)
        FROM bas_ent a
        <include refid="entJoins"/>
        <where>
            <if test="socialCode !=null and socialCode !=''">
                AND a.social_code = #{socialCode}
            </if>
            <if test="cardNo !=null and socialCode !=''">
                AND a.card_no = #{cardNo}
            </if>
            AND a.id != #{id}
            AND a.del_flag = #{DEL_FLAG_NORMAL}
        </where>
    </select>

    <select id="getEntStatistic" resultType="map">
        SELECT
        COUNT( CASE WHEN a.ent_type ='0' AND a.examine_status ='1' THEN id END ) AS 'entAmount',
        COUNT( CASE WHEN a.ent_type ='1' AND a.examine_status ='1' THEN id END ) AS 'personAmount',
        COUNT( CASE WHEN a.ent_type ='0' AND a.examine_status ='0' THEN id END ) AS 'entExamineAmount',
        COUNT( CASE WHEN a.ent_type ='1' AND a.examine_status ='0' THEN id END ) AS 'personExamineAmount'
        FROM bas_ent a
        left join sys_dept d on d.dept_id=a.id
        WHERE a.del_flag = '1' AND a.frozen_flag='0' and a.county LIKE concat(#{county},'%')
        <if test="loginAreaCode !=null and loginAreaCode !=''">
            AND d.area_code LIKE concat(#{loginAreaCode},'%')
        </if>
    </select>

    <select id="getByTableId" resultType="com.jkr.project.argi.domain.Ent">
        SELECT
        <include refid="entColumns"/>,
        ba.autograph
        FROM bas_ent a
        <include refid="entJoins"/>
        WHERE a.table_id = #{tableId} AND a.del_flag = '1'
        LIMIT 1
    </select>
    <update id="examineSave" parameterType="com.jkr.project.argi.domain.Ent">
        UPDATE bas_ent SET
           examine_status = #{examineStatus},
           examine_man = #{examineMan},
           examine_opinion = #{examineOpinion},
           examine_date = #{examineDate},
           update_by = #{updateBy},
           update_time = #{updateTime}
        WHERE id = #{id}
    </update>
    <update id="updateCertificateAmount">
        UPDATE bas_ent SET
           certificate_amount = certificate_amount+1,
           certificate_print_amount = certificate_print_amount+#{amount},
           certificate_print_date = now()
        WHERE id = #{id}
    </update>

    <update id="updateCertificatePrintAmount">
        UPDATE bas_ent SET
           certificate_print_amount = certificate_print_amount+#{amount},
           certificate_print_date = now()
        WHERE id = #{id}
    </update>

    <update id="updateInspectionWriteAmount">
        UPDATE bas_ent SET
            inspection_write_amount = inspection_write_amount+1
        WHERE id = #{id}
    </update>
    <update id="updateChangeView">
        UPDATE bas_ent SET
           change_view_flag = "1",
           update_by = #{updateBy.id},
           update_time = #{updateTime}
        WHERE id = #{id}
    </update>
    <update id="updateInvalidCertificateAmount">
        UPDATE bas_ent t
            INNER JOIN (
            SELECT
            c.ent_id AS entId,
            SUM(IF(c.id!="" and c.del_flag = '1',1,0)) certificateAmount,
            SUM(IF(c.print_count!="" and c.del_flag = '1',c.print_count,0)) certificatePrintAmount,
            MAX(IF(c.del_flag = '1',c.create_time,null)) createTime
            FROM
            bas_certificate c
            where c.ent_id = #{id}
            group by c.ent_id
            ) a ON t.id = a.entId
            SET t.certificate_amount = a.certificateAmount,
                t.certificate_print_amount = a.certificatePrintAmount,
                t.certificate_print_date = a.createTime
        where t.id = #{id}
    </update>
    <update id="updateFrozen">
        UPDATE bas_ent SET
           frozen_flag = #{frozenFlag},
           update_by = #{updateBy.id},
           update_time = #{updateTime}
        WHERE id = #{id}
    </update>
    <update id="updateTableId">
        UPDATE bas_ent SET
            table_id = #{tableId}
        WHERE id = #{id}
    </update>
    <select id="validateOnly" parameterType="string" resultType="int">
        SELECT
        ifNULL(count(1),0) cardSum
        FROM bas_ent a
        <where>
            a.del_flag = '1'
            <if test="null != contactsPhone and '' != contactsPhone">
                AND a.contacts_phone = #{contactsPhone}
            </if>
            <if test="null != id and '' != id">
                AND a.id != #{id}
            </if>
        </where>
    </select>
    <select id="validateIdCardOnly" parameterType="string" resultType="Integer">
        SELECT
        ifNULL(count(1),0) cardSum
        FROM bas_ent a
        <where>
            a.del_flag = '1'
            <if test="null != cardNo and '' != cardNo">
                AND a.card_no = #{cardNo}
            </if>
            <if test="null != id and '' != id">
                AND a.id != #{id}
            </if>
        </where>
    </select>

    <select id="getEntByIdCard" parameterType="string" resultType="com.jkr.project.argi.domain.Ent">
        SELECT
        <include refid="entColumns"/>
        FROM bas_ent a
        <where>
            del_flag = '1'
            and (a.card_no =#{cardNo} or a.contacts_phone =#{cardNo})
        </where>
    </select>
    <select id="getEntByTableId" parameterType="string" resultType="com.jkr.project.argi.domain.Ent">
        select
        <include refid="entColumns"/>
        from bas_ent a
        <where>
            del_flag = '1'
            and a.table_id = #{tableId}
        </where>
    </select>
<!--    <select id="findAreaCheckDataList" resultType="map">-->
<!--        SELECT-->
<!--        <include refid="entColumns"/>-->
<!--        FROM-->
<!--        bas_check c-->
<!--        INNER JOIN bas_ent a ON c.ent_id = a.id and a.del_flag = 1 AND a.examine_status = 1 AND a.frozen_flag=0-->
<!--        left join sys_dept d on d.dept_id=a.id-->
<!--        WHERE-->
<!--        c.check_type = 0-->
<!--        AND c.del_flag = 0-->
<!--&lt;!&ndash;        <if test="sqlMap.areaWhere!=null and sqlMap.areaWhere!=''">&ndash;&gt;-->
<!--&lt;!&ndash;            ${sqlMap.areaWhere}&ndash;&gt;-->
<!--&lt;!&ndash;        </if>&ndash;&gt;-->
<!--        <if test="loginAreaCode !=null and loginAreaCode !=''">-->
<!--            AND d.area_code LIKE concat(#{loginAreaCode},'%')-->
<!--        </if>-->
<!--        <if test="areaCode != null and areaCode != '' ">-->
<!--            AND a.county LIKE concat(#{areaCode},'%')-->
<!--        </if>-->
<!--        <if test="queryYear != null and queryYear != '' ">-->
<!--            AND YEAR(c.check_date) = #{queryYear}-->
<!--        </if>-->
<!--        <if test="queryMonth != null and queryMonth != '' ">-->
<!--            AND MONTH(c.check_date) = #{queryMonth}-->
<!--        </if>-->
<!--        <if test="entType != null and entType != '' ">-->
<!--            AND a.ent_type = #{entType}-->
<!--        </if>-->
<!--        GROUP BY a.id-->
<!--    </select>-->

    <select id="findAreaCertificateDataList" resultType="map">
        SELECT
        <include refid="entColumns"/>
        FROM bas_certificate c
        INNER JOIN bas_ent a ON c.ent_id = a.id and a.del_flag = '1' AND a.examine_status = '1' AND a.frozen_flag = '0'
        left join sys_dept d on d.dept_id=a.id
        WHERE
        c.del_flag=1
        AND c.ent_main_type != ''
        <if test="areaCode != null and areaCode != '' ">
            AND a.county LIKE concat(#{areaCode},'%')
        </if>
        <if test="loginAreaCode !=null and loginAreaCode !=''">
            AND d.area_code LIKE concat(#{loginAreaCode},'%')
        </if>
<!--        <if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">-->
<!--            ${sqlMap.areaWhere}-->
<!--        </if>-->
        <if test="queryYear != null and queryYear != '' ">
            AND YEAR(c.create_time) <![CDATA[ = ]]> #{queryYear}
        </if>
        <if test="queryMonth != null and queryMonth != '' ">
            AND MONTH(c.create_time) <![CDATA[ = ]]> #{queryMonth}
        </if>
        <if test="entType != null and entType != '' ">
            AND a.ent_type = #{entType}
        </if>
        GROUP BY a.id
    </select>
    <select id="getSyncData" resultType="com.jkr.project.argi.domain.Ent">
        SELECT
         *
        FROM bas_ent a
        WHERE a.del_flag = '1' AND a.examine_status = '1' AND a.frozen_flag = '0' and a.update_time <![CDATA[ >= ]]> #{date}
    </select>
</mapper>
