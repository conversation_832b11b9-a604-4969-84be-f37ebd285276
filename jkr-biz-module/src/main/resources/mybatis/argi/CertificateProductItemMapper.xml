<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateProductItemMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateProductItem" id="CertificateProductItemResult">
        <result property="id"    column="id"    />
        <result property="certificateId"    column="certificate_id"    />
        <result property="itemProductId"    column="item_product_id"    />
        <result property="itemProductName"    column="item_product_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectCertificateProductItemVo">
        select id, certificate_id, item_product_id, item_product_name, del_flag, create_by, create_time, update_by, update_time, remark from bas_certificate_product_item
    </sql>

    <select id="selectCertificateProductItemList" parameterType="com.jkr.project.argi.domain.CertificateProductItem" resultMap="CertificateProductItemResult">
        <include refid="selectCertificateProductItemVo"/>
        <where>
            <if test="certificateId != null  and certificateId != ''"> and certificate_id = #{certificateId}</if>
            <if test="itemProductId != null  and itemProductId != ''"> and item_product_id = #{itemProductId}</if>
            <if test="itemProductName != null  and itemProductName != ''"> and item_product_name like concat('%', #{itemProductName}, '%')</if>
            and del_flag = '1'
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectCertificateProductItemById" parameterType="Long" resultMap="CertificateProductItemResult">
        <include refid="selectCertificateProductItemVo"/>
        where id = #{id}
    </select>

    <insert id="insertCertificateProductItem" parameterType="com.jkr.project.argi.domain.CertificateProductItem">
        insert into bas_certificate_product_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="certificateId != null">certificate_id,</if>
            <if test="itemProductId != null">item_product_id,</if>
            <if test="itemProductName != null">item_product_name,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="certificateId != null">#{certificateId},</if>
            <if test="itemProductId != null">#{itemProductId},</if>
            <if test="itemProductName != null">#{itemProductName},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateProductItem" parameterType="com.jkr.project.argi.domain.CertificateProductItem">
        update bas_certificate_product_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="certificateId != null">certificate_id = #{certificateId},</if>
            <if test="itemProductId != null">item_product_id = #{itemProductId},</if>
            <if test="itemProductName != null">item_product_name = #{itemProductName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_product_item set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_product_item set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteCertificateProductItemById" parameterType="Long">
        delete from bas_certificate_product_item where id = #{id}
    </delete>

    <delete id="deleteCertificateProductItemByIds" parameterType="String">
        delete from bas_certificate_product_item where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertBatch">
        INSERT INTO bas_certificate_product_item(
            id,
            certificate_id,
            item_product_id,
            item_product_name,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            del_flag
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
        (
            #{item.id},
            #{item.certificateId},
            #{item.itemProductId},
            #{item.itemProductName},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            #{item.updateTime},
            #{item.remark},
            '1'
        )
        </foreach>
    </insert>
</mapper>
