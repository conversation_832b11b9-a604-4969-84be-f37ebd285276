<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.CertificateConfigMapper">

    <resultMap type="com.jkr.project.argi.domain.CertificateConfig" id="CertificateConfigResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="areaId" column="area_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>

        <association property="area" javaType="com.jkr.project.system.domain.SysArea">
            <result property="areaId" column="area_id"/>
            <result property="name" column="areaName"/>
            <result property="code" column="areaCode"/>
        </association>
    </resultMap>

    <sql id="selectCertificateConfigVo">
        select id,
               type,
               area_id,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from bas_certificate_config
    </sql>

    <sql id="Base_Column_List">
        <trim>
        ${alias}.id, ${alias}.type, ${alias}.area_id, ${alias}.del_flag, ${alias}.create_by,
        ${alias}.create_time, ${alias}.update_by, ${alias}.update_time, ${alias}.remark
    </trim>
    </sql>

    <select id="selectCertificateConfigList" parameterType="com.jkr.project.argi.domain.CertificateConfig"
            resultMap="CertificateConfigResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,sa.name AS areaName, sa.code AS areaCode
        from bas_certificate_config a
        left join sys_area sa on sa.area_id=a.area_id
        <where>
            <if test="type != null  and type != ''">and a.type = #{type}</if>
            <if test="areaId != null  and areaId != ''">and a.area_id = #{areaId}</if>
            <if test="area.code != null and area.code != ''">
                AND sa.code=#{area.code}
            </if>
            and a.del_flag = '1'
        </where>
        order by a.update_time desc
    </select>

    <select id="selectCertificateConfigById" parameterType="Long" resultMap="CertificateConfigResult">
        select
        <include refid="Base_Column_List">
            <property name="alias" value="a"/>
        </include>
        ,sa.name AS areaName, sa.code AS areaCode
        from bas_certificate_config a
        left join sys_area sa on sa.area_id=a.area_id
        where a.id = #{id}
    </select>

    <insert id="insertCertificateConfig" parameterType="com.jkr.project.argi.domain.CertificateConfig">
        insert into bas_certificate_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="areaId != null">area_id,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="areaId != null">#{areaId},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateCertificateConfig" parameterType="com.jkr.project.argi.domain.CertificateConfig">
        update bas_certificate_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_certificate_config set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_certificate_config
        set del_flag = REPLACE(unix_timestamp(current_timestamp(3)), '.', '')
        where id = #{id}
    </update>

    <delete id="deleteCertificateConfigById" parameterType="Long">
        delete
        from bas_certificate_config
        where id = #{id}
    </delete>

    <delete id="deleteCertificateConfigByIds" parameterType="String">
        delete from bas_certificate_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
