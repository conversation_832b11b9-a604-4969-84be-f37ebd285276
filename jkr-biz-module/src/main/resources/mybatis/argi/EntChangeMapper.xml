<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.EntChangeMapper">

    <resultMap type="com.jkr.project.argi.domain.EntChange" id="EntChangeResult">
            <result property="id"    column="id"    />
            <result property="entId"    column="ent_id"    />
            <result property="name"    column="name"    />
            <result property="businessType"    column="business_type"    />
            <result property="entType"    column="ent_type"    />
            <result property="mainType"    column="main_type"    />
            <result property="identityType"    column="identity_type"    />
            <result property="farmType"    column="farm_type"    />
            <result property="socialCode"    column="social_code"    />
            <result property="cardNo"    column="card_no"    />
            <result property="legalPerson"    column="legal_person"    />
            <result property="contacts"    column="contacts"    />
            <result property="contactsPhone"    column="contacts_phone"    />
            <result property="province"    column="province"    />
            <result property="city"    column="city"    />
            <result property="county"    column="county"    />
            <result property="address"    column="address"    />
            <result property="detail"    column="detail"    />
            <result property="lng"    column="lng"    />
            <result property="lat"    column="lat"    />
            <result property="companyIntroduction"    column="company_introduction"    />
            <result property="entHonor"    column="ent_honor"    />
            <result property="submitDate"    column="submit_date"    />
            <result property="examineStatus"    column="examine_status"    />
            <result property="examineMan"    column="examine_man"    />
            <result property="examineOpinion"    column="examine_opinion"    />
            <result property="examineDate"    column="examine_date"    />
            <result property="autograph"    column="autograph"    />
            <result property="basicFlag"    column="basic_flag"    />
            <result property="basicEnterFlag"    column="basic_enter_flag"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEntChangeVo">
        select id, ent_id, name, business_type, ent_type, main_type, identity_type, farm_type, social_code, card_no, legal_person, contacts, contacts_phone, province, city, county, address, detail, lng, lat, company_introduction, ent_honor, submit_date, examine_status, examine_man, examine_opinion, examine_date, autograph, basic_flag, basic_enter_flag, del_flag, create_by, create_time, update_by, update_time, remark from bas_ent_change
    </sql>
    <sql id="entChangeColumns">
        a.id AS "id",
		a.ent_id AS "entId",
		a.name AS "name",
		a.business_type AS "businessType",
        a.identity_type AS "identityType",
		a.ent_type AS "entType",
		a.main_type AS "mainType",
		a.farm_type AS "farmType",
		a.social_code AS "socialCode",
		a.card_no AS "cardNo",
		a.legal_person AS "legalPerson",
		a.contacts AS "contacts",
		a.contacts_phone AS "contactsPhone",
		a.province AS "province",
		a.city AS "city",
		a.county AS "county",
		a.address AS "address",
		a.detail AS "detail",
		a.lng AS "lng",
		a.lat AS "lat",
		a.company_introduction AS "companyIntroduction",
		a.ent_honor AS "entHonor",
		a.submit_date AS "submitDate",
		a.examine_status AS "examineStatus",
		a.examine_man AS "examineMan",
		a.examine_opinion AS "examineOpinion",
		a.examine_date AS "examineDate",
		a.autograph AS "autograph",
		a.basic_flag AS "basicFlag",
		a.basic_enter_flag AS "basicEnterFlag",
		a.create_by AS "createBy",
		a.create_time AS "createTime",
		a.update_by AS "updateBy",
		a.update_time AS "updateTime",
		a.remark AS "remark",
		a.del_flag AS "delFlag"
    </sql>

    <select id="selectEntChangeList" parameterType="com.jkr.project.argi.domain.EntChange" resultMap="EntChangeResult">
        <include refid="selectEntChangeVo"/>
        <where>
                        <if test="entId != null  and entId != ''"> and ent_id = #{entId}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="businessType != null  and businessType != ''"> and business_type = #{businessType}</if>
                        <if test="entType != null  and entType != ''"> and ent_type = #{entType}</if>
                        <if test="mainType != null  and mainType != ''"> and main_type = #{mainType}</if>
                        <if test="identityType != null  and identityType != ''"> and identity_type = #{identityType}</if>
                        <if test="farmType != null  and farmType != ''"> and farm_type = #{farmType}</if>
                        <if test="socialCode != null  and socialCode != ''"> and social_code = #{socialCode}</if>
                        <if test="cardNo != null  and cardNo != ''"> and card_no = #{cardNo}</if>
                        <if test="legalPerson != null  and legalPerson != ''"> and legal_person = #{legalPerson}</if>
                        <if test="contacts != null  and contacts != ''"> and contacts = #{contacts}</if>
                        <if test="contactsPhone != null  and contactsPhone != ''"> and contacts_phone = #{contactsPhone}</if>
                        <if test="province != null  and province != ''"> and province = #{province}</if>
                        <if test="city != null  and city != ''"> and city = #{city}</if>
                        <if test="county != null  and county != ''"> and county = #{county}</if>
                        <if test="address != null  and address != ''"> and address = #{address}</if>
                        <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
                        <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
                        <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
                        <if test="companyIntroduction != null  and companyIntroduction != ''"> and company_introduction = #{companyIntroduction}</if>
                        <if test="entHonor != null  and entHonor != ''"> and ent_honor = #{entHonor}</if>
                        <if test="submitDate != null "> and submit_date = #{submitDate}</if>
                        <if test="examineStatus != null  and examineStatus != ''"> and examine_status = #{examineStatus}</if>
                        <if test="examineMan != null  and examineMan != ''"> and examine_man = #{examineMan}</if>
                        <if test="examineOpinion != null  and examineOpinion != ''"> and examine_opinion = #{examineOpinion}</if>
                        <if test="examineDate != null "> and examine_date = #{examineDate}</if>
                        <if test="autograph != null  and autograph != ''"> and autograph = #{autograph}</if>
                        <if test="basicFlag != null  and basicFlag != ''"> and basic_flag = #{basicFlag}</if>
                        <if test="basicEnterFlag != null  and basicEnterFlag != ''"> and basic_enter_flag = #{basicEnterFlag}</if>
                        <if test="loginAreaCode !=null and loginAreaCode !='' and loginAreaCode.length() == 2 ">
                            AND province = #{province}
                        </if>
                        <if test="loginAreaCode !=null and loginAreaCode !='' and loginAreaCode.length() == 4 ">
                            AND city = #{city}
                        </if>
                        <if test="loginAreaCode !=null and loginAreaCode !='' and loginAreaCode.length() == 6 ">
                            AND county = #{county}
                        </if>
            and del_flag = '1'
        </where>
    </select>

    <select id="findList" parameterType="com.jkr.project.argi.domain.EntChange" resultType="com.jkr.project.argi.domain.EntChange">
        SELECT
        <include refid="entChangeColumns"/>
        FROM bas_ent_change a
        <where>
            a.del_flag = 1
            <if test="name !=null and name !=''">
                AND a.name LIKE concat('%',#{name},'%')
            </if>
            <if test="entType !=null and entType !=''">
                AND a.ent_type = #{entType}
            </if>
            <if test="entId !=null and entId !=''">
                AND a.ent_id = #{entId}
            </if>
            <if test="businessType != null and businessType != ''">
                AND a.business_type = #{businessType}
            </if>
            <if test="mainType != null and mainType != ''">
                AND a.main_type = #{mainType}
            </if>
            <if test="county != null and county != ''">
                AND a.county LIKE concat(#{county},'%')
            </if>
<!--            <if test="sqlMap.areaWhere!='' and sqlMap.areaWhere!=null">-->
<!--                ${sqlMap.areaWhere}-->
<!--            </if>-->
            <if test="examineStatus !=null and examineStatus !=''">
                AND a.examine_status = #{examineStatus}
            </if>
<!--            <if test="beginExamineDate !=null">-->
<!--                AND Date(a.submit_date) <![CDATA[ >= ]]> Date(#{beginExamineDate})-->
<!--            </if>-->
<!--            <if test="endExamineDate !=null">-->
<!--                AND Date(a.submit_date) <![CDATA[ <= ]]> Date(#{endExamineDate})-->
<!--            </if>-->
            <if test="beginExamineDate !=null">
                AND Date(a.examine_date) <![CDATA[ >= ]]> Date(#{beginExamineDate})
            </if>
            <if test="endExamineDate !=null">
                AND Date(a.examine_date) <![CDATA[ <= ]]> Date(#{endExamineDate})
            </if>
            <if test="beginSubmitDate !=null">
                AND Date(a.submit_date) <![CDATA[ >= ]]> Date(#{beginSubmitDate})
            </if>
            <if test="endSubmitDate !=null">
                AND Date(a.submit_date) <![CDATA[ <= ]]> Date(#{endSubmitDate})
            </if>
            <if test="loginAreaCode !=null and loginAreaCode !='' and loginAreaCode.length() == 2 ">
                AND province = #{province}
            </if>
            <if test="loginAreaCode !=null and loginAreaCode !='' and loginAreaCode.length() == 4 ">
                AND city = #{city}
            </if>
            <if test="loginAreaCode !=null and loginAreaCode !='' and loginAreaCode.length() == 6 ">
                AND county = #{county}
            </if>
        </where>
        ORDER BY FIELD(a.examine_status,0,-1,1),a.update_time DESC
<!--        <choose>-->
<!--            <when test="page !=null and page.orderBy != null and page.orderBy != ''">-->
<!--                ORDER BY ${page.orderBy}-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                ORDER BY a.update_time DESC-->
<!--            </otherwise>-->
<!--        </choose>-->
    </select>
    <select id="selectEntChangeById" parameterType="Long" resultMap="EntChangeResult">
            <include refid="selectEntChangeVo"/>
            where id = #{id}
    </select>

    <insert id="insertEntChange" parameterType="com.jkr.project.argi.domain.EntChange">
        insert into bas_ent_change
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="entId != null">ent_id,</if>
                    <if test="name != null">name,</if>
                    <if test="businessType != null">business_type,</if>
                    <if test="entType != null">ent_type,</if>
                    <if test="mainType != null">main_type,</if>
                    <if test="identityType != null">identity_type,</if>
                    <if test="farmType != null">farm_type,</if>
                    <if test="socialCode != null">social_code,</if>
                    <if test="cardNo != null">card_no,</if>
                    <if test="legalPerson != null">legal_person,</if>
                    <if test="contacts != null">contacts,</if>
                    <if test="contactsPhone != null">contacts_phone,</if>
                    <if test="province != null">province,</if>
                    <if test="city != null">city,</if>
                    <if test="county != null">county,</if>
                    <if test="address != null">address,</if>
                    <if test="detail != null">detail,</if>
                    <if test="lng != null">lng,</if>
                    <if test="lat != null">lat,</if>
                    <if test="companyIntroduction != null">company_introduction,</if>
                    <if test="entHonor != null">ent_honor,</if>
                    <if test="submitDate != null">submit_date,</if>
                    <if test="examineStatus != null">examine_status,</if>
                    <if test="examineMan != null">examine_man,</if>
                    <if test="examineOpinion != null">examine_opinion,</if>
                    <if test="examineDate != null">examine_date,</if>
                    <if test="autograph != null">autograph,</if>
                    <if test="basicFlag != null">basic_flag,</if>
                    <if test="basicEnterFlag != null">basic_enter_flag,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="entId != null">#{entId},</if>
                    <if test="name != null">#{name},</if>
                    <if test="businessType != null">#{businessType},</if>
                    <if test="entType != null">#{entType},</if>
                    <if test="mainType != null">#{mainType},</if>
                    <if test="identityType != null">#{identityType},</if>
                    <if test="farmType != null">#{farmType},</if>
                    <if test="socialCode != null">#{socialCode},</if>
                    <if test="cardNo != null">#{cardNo},</if>
                    <if test="legalPerson != null">#{legalPerson},</if>
                    <if test="contacts != null">#{contacts},</if>
                    <if test="contactsPhone != null">#{contactsPhone},</if>
                    <if test="province != null">#{province},</if>
                    <if test="city != null">#{city},</if>
                    <if test="county != null">#{county},</if>
                    <if test="address != null">#{address},</if>
                    <if test="detail != null">#{detail},</if>
                    <if test="lng != null">#{lng},</if>
                    <if test="lat != null">#{lat},</if>
                    <if test="companyIntroduction != null">#{companyIntroduction},</if>
                    <if test="entHonor != null">#{entHonor},</if>
                    <if test="submitDate != null">#{submitDate},</if>
                    <if test="examineStatus != null">#{examineStatus},</if>
                    <if test="examineMan != null">#{examineMan},</if>
                    <if test="examineOpinion != null">#{examineOpinion},</if>
                    <if test="examineDate != null">#{examineDate},</if>
                    <if test="autograph != null">#{autograph},</if>
                    <if test="basicFlag != null">#{basicFlag},</if>
                    <if test="basicEnterFlag != null">#{basicEnterFlag},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateEntChange" parameterType="com.jkr.project.argi.domain.EntChange">
        update bas_ent_change
        <trim prefix="SET" suffixOverrides=",">
                    <if test="entId != null">ent_id = #{entId},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="businessType != null">business_type = #{businessType},</if>
                    <if test="entType != null">ent_type = #{entType},</if>
                    <if test="mainType != null">main_type = #{mainType},</if>
                    <if test="identityType != null">identity_type = #{identityType},</if>
                    <if test="farmType != null">farm_type = #{farmType},</if>
                    <if test="socialCode != null">social_code = #{socialCode},</if>
                    <if test="cardNo != null">card_no = #{cardNo},</if>
                    <if test="legalPerson != null">legal_person = #{legalPerson},</if>
                    <if test="contacts != null">contacts = #{contacts},</if>
                    <if test="contactsPhone != null">contacts_phone = #{contactsPhone},</if>
                    <if test="province != null">province = #{province},</if>
                    <if test="city != null">city = #{city},</if>
                    <if test="county != null">county = #{county},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="detail != null">detail = #{detail},</if>
                    <if test="lng != null">lng = #{lng},</if>
                    <if test="lat != null">lat = #{lat},</if>
                    <if test="companyIntroduction != null">company_introduction = #{companyIntroduction},</if>
                    <if test="entHonor != null">ent_honor = #{entHonor},</if>
                    <if test="submitDate != null">submit_date = #{submitDate},</if>
                    <if test="examineStatus != null">examine_status = #{examineStatus},</if>
                    <if test="examineMan != null">examine_man = #{examineMan},</if>
                    <if test="examineOpinion != null">examine_opinion = #{examineOpinion},</if>
                    <if test="examineDate != null">examine_date = #{examineDate},</if>
                    <if test="autograph != null">autograph = #{autograph},</if>
                    <if test="basicFlag != null">basic_flag = #{basicFlag},</if>
                    <if test="basicEnterFlag != null">basic_enter_flag = #{basicEnterFlag},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_ent_change set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_ent_change set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteEntChangeById" parameterType="Long">
        delete from bas_ent_change where id = #{id}
    </delete>

    <delete id="deleteEntChangeByIds" parameterType="String">
        delete from bas_ent_change where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <update id="examineSave"  parameterType="com.jkr.project.argi.domain.EntChange">
        UPDATE bas_ent_change SET
              examine_status = #{examineStatus},
              examine_man = #{examineMan},
              examine_opinion = #{examineOpinion},
              examine_date = #{examineDate},
              update_by = #{updateBy},
              update_time = #{updateTime}
        WHERE id = #{id}
    </update>
    <select id="getUnfinished" resultType="com.jkr.project.argi.domain.EntChange">
        SELECT
        <include refid="selectEntChangeVo"/>
        FROM bas_ent_change a
        <where>
            a.del_flag = '1'
            and (a.examine_status = '0' or a.examine_status = '99')
            AND a.ent_id = #{entId}
        </where>
    </select>
</mapper>
