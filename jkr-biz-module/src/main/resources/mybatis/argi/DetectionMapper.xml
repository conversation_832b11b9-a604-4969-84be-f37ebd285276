<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.DetectionMapper">

    <resultMap type="com.jkr.project.argi.domain.Detection" id="DetectionResult">
            <result property="id"    column="id"    />
            <result property="sampleNo"    column="sample_no"    />
            <result property="sampleQrcodeUrl"    column="sample_qrcode_url"    />
            <result property="goodsName"    column="goods_name"    />
            <result property="recordNo"    column="record_no"    />
            <result property="testItemCode"    column="test_item_code"    />
            <result property="testItemLabel"    column="test_item_label"    />
            <result property="testDate"    column="test_date"    />
            <result property="testResult"    column="test_result"    />
            <result property="testNumericalUnit"    column="test_numerical_unit"    />
            <result property="limitStandard"    column="limit_standard"    />
            <result property="testMan"    column="test_man"    />
            <result property="instrumentName"    column="instrument_name"    />
            <result property="instrumentNo"    column="instrument_no"    />
            <result property="qualifiedRange"    column="qualified_range"    />
            <result property="jsonData"    column="json_data"    />
            <result property="lastModify"    column="last_modify"    />
            <result property="sort"    column="sort"    />
            <result property="status"    column="status"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDetectionVo">
        select id, sample_no, sample_qrcode_url, goods_name, record_no, test_item_code, test_item_label, test_date, test_result, test_numerical_unit, limit_standard, test_man, instrument_name, instrument_no, qualified_range, json_data, last_modify, sort, status, del_flag, create_by, create_time, update_by, update_time, remark from bas_detection
    </sql>

    <select id="selectDetectionList" parameterType="com.jkr.project.argi.domain.Detection" resultMap="DetectionResult">
        <include refid="selectDetectionVo"/>
        <where>
                        <if test="sampleNo != null  and sampleNo != ''"> and sample_no = #{sampleNo}</if>
                        <if test="sampleQrcodeUrl != null  and sampleQrcodeUrl != ''"> and sample_qrcode_url = #{sampleQrcodeUrl}</if>
                        <if test="goodsName != null  and goodsName != ''"> and goods_name like concat('%', #{goodsName}, '%')</if>
                        <if test="recordNo != null  and recordNo != ''"> and record_no = #{recordNo}</if>
                        <if test="testItemCode != null  and testItemCode != ''"> and test_item_code = #{testItemCode}</if>
                        <if test="testItemLabel != null  and testItemLabel != ''"> and test_item_label = #{testItemLabel}</if>
                        <if test="testDate != null "> and test_date = #{testDate}</if>
                        <if test="testResult != null  and testResult != ''"> and test_result = #{testResult}</if>
                        <if test="testNumericalUnit != null  and testNumericalUnit != ''"> and test_numerical_unit = #{testNumericalUnit}</if>
                        <if test="limitStandard != null  and limitStandard != ''"> and limit_standard = #{limitStandard}</if>
                        <if test="testMan != null  and testMan != ''"> and test_man = #{testMan}</if>
                        <if test="instrumentName != null  and instrumentName != ''"> and instrument_name like concat('%', #{instrumentName}, '%')</if>
                        <if test="instrumentNo != null  and instrumentNo != ''"> and instrument_no = #{instrumentNo}</if>
                        <if test="qualifiedRange != null  and qualifiedRange != ''"> and qualified_range = #{qualifiedRange}</if>
                        <if test="jsonData != null  and jsonData != ''"> and json_data = #{jsonData}</if>
                        <if test="lastModify != null "> and last_modify = #{lastModify}</if>
                        <if test="sort != null "> and sort = #{sort}</if>
                        <if test="status != null "> and status = #{status}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectDetectionById" parameterType="Long" resultMap="DetectionResult">
            <include refid="selectDetectionVo"/>
            where id = #{id}
    </select>

    <insert id="insertDetection" parameterType="com.jkr.project.argi.domain.Detection">
        insert into bas_detection
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="sampleNo != null">sample_no,</if>
                    <if test="sampleQrcodeUrl != null">sample_qrcode_url,</if>
                    <if test="goodsName != null">goods_name,</if>
                    <if test="recordNo != null">record_no,</if>
                    <if test="testItemCode != null">test_item_code,</if>
                    <if test="testItemLabel != null">test_item_label,</if>
                    <if test="testDate != null">test_date,</if>
                    <if test="testResult != null">test_result,</if>
                    <if test="testNumericalUnit != null">test_numerical_unit,</if>
                    <if test="limitStandard != null">limit_standard,</if>
                    <if test="testMan != null">test_man,</if>
                    <if test="instrumentName != null">instrument_name,</if>
                    <if test="instrumentNo != null">instrument_no,</if>
                    <if test="qualifiedRange != null">qualified_range,</if>
                    <if test="jsonData != null">json_data,</if>
                    <if test="lastModify != null">last_modify,</if>
                    <if test="sort != null">sort,</if>
                    <if test="status != null">status,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="sampleNo != null">#{sampleNo},</if>
                    <if test="sampleQrcodeUrl != null">#{sampleQrcodeUrl},</if>
                    <if test="goodsName != null">#{goodsName},</if>
                    <if test="recordNo != null">#{recordNo},</if>
                    <if test="testItemCode != null">#{testItemCode},</if>
                    <if test="testItemLabel != null">#{testItemLabel},</if>
                    <if test="testDate != null">#{testDate},</if>
                    <if test="testResult != null">#{testResult},</if>
                    <if test="testNumericalUnit != null">#{testNumericalUnit},</if>
                    <if test="limitStandard != null">#{limitStandard},</if>
                    <if test="testMan != null">#{testMan},</if>
                    <if test="instrumentName != null">#{instrumentName},</if>
                    <if test="instrumentNo != null">#{instrumentNo},</if>
                    <if test="qualifiedRange != null">#{qualifiedRange},</if>
                    <if test="jsonData != null">#{jsonData},</if>
                    <if test="lastModify != null">#{lastModify},</if>
                    <if test="sort != null">#{sort},</if>
                    <if test="status != null">#{status},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDetection" parameterType="com.jkr.project.argi.domain.Detection">
        update bas_detection
        <trim prefix="SET" suffixOverrides=",">
                    <if test="sampleNo != null">sample_no = #{sampleNo},</if>
                    <if test="sampleQrcodeUrl != null">sample_qrcode_url = #{sampleQrcodeUrl},</if>
                    <if test="goodsName != null">goods_name = #{goodsName},</if>
                    <if test="recordNo != null">record_no = #{recordNo},</if>
                    <if test="testItemCode != null">test_item_code = #{testItemCode},</if>
                    <if test="testItemLabel != null">test_item_label = #{testItemLabel},</if>
                    <if test="testDate != null">test_date = #{testDate},</if>
                    <if test="testResult != null">test_result = #{testResult},</if>
                    <if test="testNumericalUnit != null">test_numerical_unit = #{testNumericalUnit},</if>
                    <if test="limitStandard != null">limit_standard = #{limitStandard},</if>
                    <if test="testMan != null">test_man = #{testMan},</if>
                    <if test="instrumentName != null">instrument_name = #{instrumentName},</if>
                    <if test="instrumentNo != null">instrument_no = #{instrumentNo},</if>
                    <if test="qualifiedRange != null">qualified_range = #{qualifiedRange},</if>
                    <if test="jsonData != null">json_data = #{jsonData},</if>
                    <if test="lastModify != null">last_modify = #{lastModify},</if>
                    <if test="sort != null">sort = #{sort},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_detection set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_detection set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteDetectionById" parameterType="Long">
        delete from bas_detection where id = #{id}
    </delete>

    <delete id="deleteDetectionByIds" parameterType="String">
        delete from bas_detection where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
