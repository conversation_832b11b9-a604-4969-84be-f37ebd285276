<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.ProductSampleMapper">

    <resultMap type="com.jkr.project.argi.domain.ProductSample" id="ProductSampleResult">
        <result property="id" column="id"/>
        <result property="entId" column="ent_id"/>
        <result property="productId" column="product_id"/>
        <result property="inspectionSituation" column="inspection_situation"/>
        <result property="productInspectionId" column="product_inspection_id"/>
        <result property="sampleNo" column="sample_no"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectProductSampleVo">
        select id,
               ent_id,
               product_id,
               inspection_situation,
               product_inspection_id,
               sample_no,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from bas_product_sample
    </sql>

    <select id="selectProductSampleList" parameterType="com.jkr.project.argi.domain.ProductSample"
            resultMap="ProductSampleResult">
        <include refid="selectProductSampleVo"/>
        <where>
            <if test="entId != null  and entId != ''">
                and ent_id = #{entId}
            </if>
            <if test="productId != null  and productId != ''">
                and product_id = #{productId}
            </if>
            <if test="inspectionSituation != null  and inspectionSituation != ''">
                and inspection_situation = #{inspectionSituation}
            </if>
            <if test="productInspectionId != null  and productInspectionId != ''">
                and product_inspection_id = #{productInspectionId}
            </if>
            <if test="sampleNo != null  and sampleNo != ''">
                and sample_no = #{sampleNo}
            </if>
            and del_flag = '1'
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectProductSampleById" parameterType="Long" resultMap="ProductSampleResult">
        <include refid="selectProductSampleVo"/>
        where id = #{id}
    </select>

    <insert id="insertProductSample" parameterType="com.jkr.project.argi.domain.ProductSample">
        insert into bas_product_sample
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="entId != null">ent_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="inspectionSituation != null">inspection_situation,</if>
            <if test="productInspectionId != null">product_inspection_id,</if>
            <if test="sampleNo != null">sample_no,</if>
            del_flag,
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="entId != null">#{entId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="inspectionSituation != null">#{inspectionSituation},</if>
            <if test="productInspectionId != null">#{productInspectionId},</if>
            <if test="sampleNo != null">#{sampleNo},</if>
            '1',
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateProductSample" parameterType="com.jkr.project.argi.domain.ProductSample">
        update bas_product_sample
        <trim prefix="SET" suffixOverrides=",">
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="inspectionSituation != null">inspection_situation = #{inspectionSituation},</if>
            <if test="productInspectionId != null">product_inspection_id = #{productInspectionId},</if>
            <if test="sampleNo != null">sample_no = #{sampleNo},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_product_sample set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_product_sample
        set del_flag = REPLACE(unix_timestamp(current_timestamp(3)), '.', '')
        where id = #{id}
    </update>

    <delete id="deleteProductSampleById" parameterType="Long">
        delete
        from bas_product_sample
        where id = #{id}
    </delete>

    <delete id="deleteProductSampleByIds" parameterType="String">
        delete from bas_product_sample where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
