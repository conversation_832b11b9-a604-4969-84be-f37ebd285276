<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.FeaturedProductsMapper">

    <resultMap type="com.jkr.project.argi.domain.FeaturedProducts" id="FeaturedProductsResult">
            <result property="id"    column="id"    />
            <result property="officeCode"    column="office_code"    />
            <result property="code"    column="code"    />
            <result property="areaId"    column="area_id"    />
            <result property="name"    column="name"    />
            <result property="address"    column="address"    />
            <result property="businessName"    column="business_name"    />
            <result property="businessCode"    column="business_code"    />
            <result property="productDate"    column="product_date"    />
            <result property="scale"    column="scale"    />
            <result property="scaleUnit"    column="scale_unit"    />
            <result property="yield"    column="yield"    />
            <result property="yieldUnit"    column="yield_unit"    />
            <result property="outputValue"    column="output_value"    />
            <result property="supplyName"    column="supply_name"    />
            <result property="supplyCode"    column="supply_code"    />
            <result property="mapDisplay"    column="map_display"    />
            <result property="showSort"    column="show_sort"    />
            <result property="introduce"    column="introduce"    />
            <result property="productIntroduce"    column="product_introduce"    />
            <result property="isDefault"    column="is_default"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectFeaturedProductsVo">
        select id, office_code, code, area_id, name, address, business_name, business_code, product_date, scale, scale_unit, yield, yield_unit, output_value, supply_name, supply_code, map_display, show_sort, introduce, product_introduce, is_default, del_flag, create_by, create_time, update_by, update_time, remark from bas_featured_products
    </sql>

    <select id="selectFeaturedProductsList" parameterType="com.jkr.project.argi.domain.FeaturedProducts" resultMap="FeaturedProductsResult">
        <include refid="selectFeaturedProductsVo"/>
        <where>
                        <if test="officeCode != null  and officeCode != ''"> and office_code = #{officeCode}</if>
                        <if test="code != null  and code != ''"> and code = #{code}</if>
                        <if test="areaId != null  and areaId != ''"> and area_id = #{areaId}</if>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="address != null  and address != ''"> and address = #{address}</if>
                        <if test="businessName != null  and businessName != ''"> and business_name like concat('%', #{businessName}, '%')</if>
                        <if test="businessCode != null  and businessCode != ''"> and business_code = #{businessCode}</if>
                        <if test="productDate != null "> and product_date = #{productDate}</if>
                        <if test="scale != null  and scale != ''"> and scale = #{scale}</if>
                        <if test="scaleUnit != null  and scaleUnit != ''"> and scale_unit = #{scaleUnit}</if>
                        <if test="yield != null "> and yield = #{yield}</if>
                        <if test="yieldUnit != null  and yieldUnit != ''"> and yield_unit = #{yieldUnit}</if>
                        <if test="outputValue != null "> and output_value = #{outputValue}</if>
                        <if test="supplyName != null  and supplyName != ''"> and supply_name like concat('%', #{supplyName}, '%')</if>
                        <if test="supplyCode != null  and supplyCode != ''"> and supply_code = #{supplyCode}</if>
                        <if test="mapDisplay != null "> and map_display = #{mapDisplay}</if>
                        <if test="showSort != null "> and show_sort = #{showSort}</if>
                        <if test="introduce != null  and introduce != ''"> and introduce = #{introduce}</if>
                        <if test="productIntroduce != null  and productIntroduce != ''"> and product_introduce = #{productIntroduce}</if>
                        <if test="isDefault != null "> and is_default = #{isDefault}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectFeaturedProductsById" parameterType="Long" resultMap="FeaturedProductsResult">
            <include refid="selectFeaturedProductsVo"/>
            where id = #{id}
    </select>

    <insert id="insertFeaturedProducts" parameterType="com.jkr.project.argi.domain.FeaturedProducts">
        insert into bas_featured_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="officeCode != null">office_code,</if>
                    <if test="code != null">code,</if>
                    <if test="areaId != null">area_id,</if>
                    <if test="name != null">name,</if>
                    <if test="address != null">address,</if>
                    <if test="businessName != null">business_name,</if>
                    <if test="businessCode != null">business_code,</if>
                    <if test="productDate != null">product_date,</if>
                    <if test="scale != null">scale,</if>
                    <if test="scaleUnit != null">scale_unit,</if>
                    <if test="yield != null">yield,</if>
                    <if test="yieldUnit != null">yield_unit,</if>
                    <if test="outputValue != null">output_value,</if>
                    <if test="supplyName != null">supply_name,</if>
                    <if test="supplyCode != null">supply_code,</if>
                    <if test="mapDisplay != null">map_display,</if>
                    <if test="showSort != null">show_sort,</if>
                    <if test="introduce != null">introduce,</if>
                    <if test="productIntroduce != null">product_introduce,</if>
                    <if test="isDefault != null">is_default,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="officeCode != null">#{officeCode},</if>
                    <if test="code != null">#{code},</if>
                    <if test="areaId != null">#{areaId},</if>
                    <if test="name != null">#{name},</if>
                    <if test="address != null">#{address},</if>
                    <if test="businessName != null">#{businessName},</if>
                    <if test="businessCode != null">#{businessCode},</if>
                    <if test="productDate != null">#{productDate},</if>
                    <if test="scale != null">#{scale},</if>
                    <if test="scaleUnit != null">#{scaleUnit},</if>
                    <if test="yield != null">#{yield},</if>
                    <if test="yieldUnit != null">#{yieldUnit},</if>
                    <if test="outputValue != null">#{outputValue},</if>
                    <if test="supplyName != null">#{supplyName},</if>
                    <if test="supplyCode != null">#{supplyCode},</if>
                    <if test="mapDisplay != null">#{mapDisplay},</if>
                    <if test="showSort != null">#{showSort},</if>
                    <if test="introduce != null">#{introduce},</if>
                    <if test="productIntroduce != null">#{productIntroduce},</if>
                    <if test="isDefault != null">#{isDefault},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateFeaturedProducts" parameterType="com.jkr.project.argi.domain.FeaturedProducts">
        update bas_featured_products
        <trim prefix="SET" suffixOverrides=",">
                    <if test="officeCode != null">office_code = #{officeCode},</if>
                    <if test="code != null">code = #{code},</if>
                    <if test="areaId != null">area_id = #{areaId},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="businessName != null">business_name = #{businessName},</if>
                    <if test="businessCode != null">business_code = #{businessCode},</if>
                    <if test="productDate != null">product_date = #{productDate},</if>
                    <if test="scale != null">scale = #{scale},</if>
                    <if test="scaleUnit != null">scale_unit = #{scaleUnit},</if>
                    <if test="yield != null">yield = #{yield},</if>
                    <if test="yieldUnit != null">yield_unit = #{yieldUnit},</if>
                    <if test="outputValue != null">output_value = #{outputValue},</if>
                    <if test="supplyName != null">supply_name = #{supplyName},</if>
                    <if test="supplyCode != null">supply_code = #{supplyCode},</if>
                    <if test="mapDisplay != null">map_display = #{mapDisplay},</if>
                    <if test="showSort != null">show_sort = #{showSort},</if>
                    <if test="introduce != null">introduce = #{introduce},</if>
                    <if test="productIntroduce != null">product_introduce = #{productIntroduce},</if>
                    <if test="isDefault != null">is_default = #{isDefault},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_featured_products set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update bas_featured_products set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteFeaturedProductsById" parameterType="Long">
        delete from bas_featured_products where id = #{id}
    </delete>

    <delete id="deleteFeaturedProductsByIds" parameterType="String">
        delete from bas_featured_products where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
