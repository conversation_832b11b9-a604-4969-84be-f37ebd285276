<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.argi.mapper.AcquireRecordMapper">

    <resultMap type="com.jkr.project.argi.domain.AcquireRecord" id="AcquireRecordResult">
        <result property="id"    column="id"    />
        <result property="entId"    column="ent_id"    />
        <result property="acqurieFlag"    column="acqurie_flag"    />
        <result property="scanRecordId"    column="scan_record_id"    />
        <result property="fullNumber"    column="full_number"    />
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productNum"    column="product_num"    />
        <result property="productUnitCode"    column="product_unit_code"    />
        <result property="productUnitName"    column="product_unit_name"    />
        <result property="certificateEntName"    column="certificate_ent_name"    />
        <result property="certificateEntContactsPhone"    column="certificate_ent_contacts_phone"    />
        <result property="productProvince"    column="product_province"    />
        <result property="productCity"    column="product_city"    />
        <result property="productCounty"    column="product_county"    />
        <result property="productAddress"    column="product_address"    />
        <result property="productDetail"    column="product_detail"    />
        <result property="certificateDate"    column="certificate_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
    </resultMap>

    <sql id="selectAcquireRecordVo">
        select id, ent_id, acqurie_flag, scan_record_id, full_number, product_id, product_name, product_num, product_unit_code, product_unit_name, certificate_ent_name, certificate_ent_contacts_phone, product_province, product_city, product_county, product_address, product_detail, certificate_date, create_by, create_time, update_by, update_time, remark, del_flag from bas_acquire_record
    </sql>

    <select id="selectAcquireRecordList" parameterType="com.jkr.project.argi.domain.AcquireRecord" resultMap="AcquireRecordResult">
        <include refid="selectAcquireRecordVo"/>
        <where>
            <if test="entId != null  and entId != ''"> and ent_id = #{entId}</if>
            <if test="acqurieFlag != null  and acqurieFlag != ''"> and acqurie_flag = #{acqurieFlag}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productUnitCode != null  and productUnitCode != ''"> and product_unit_code = #{productUnitCode}</if>
            <if test="createDate != null "> and DATE_FORMAT(create_time,'%Y-%m-%d') = DATE_FORMAT(#{createDate},'%Y-%m-%d')</if>
            <if test="beginCreateDate != null">
                AND
                <![CDATA[ DATE_FORMAT(create_time,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
            </if>
            <if test="endCreateDate != null">
                AND
                <![CDATA[ DATE_FORMAT(create_time,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
            </if>
            and del_flag = '1'
        </where>
        <choose>
            <when test="addProductOrder == 1">
                ORDER BY product_name asc
            </when>
            <when test="addProductOrder == 2">
                ORDER BY product_name desc
            </when>
            <when test="addAcqurieFlagOrder == 1">
                ORDER BY acqurie_flag asc
            </when>
            <when test="addAcqurieFlagOrder == 2">
                ORDER BY acqurie_flag desc
            </when>
            <when test="addCreateDateOrder == 1">
                ORDER BY create_time asc
            </when>
            <when test="addCreateDateOrder == 2">
                ORDER BY create_time desc
            </when>
            <otherwise>
                ORDER BY update_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectAcquireRecordById" parameterType="String" resultMap="AcquireRecordResult">
        <include refid="selectAcquireRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertAcquireRecord" parameterType="com.jkr.project.argi.domain.AcquireRecord">
        insert into bas_acquire_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="entId != null">ent_id,</if>
            <if test="acqurieFlag != null">acqurie_flag,</if>
            <if test="scanRecordId != null">scan_record_id,</if>
            <if test="fullNumber != null">full_number,</if>
            <if test="productId != null">product_id,</if>
            <if test="productName != null">product_name,</if>
            <if test="productNum != null">product_num,</if>
            <if test="productUnitCode != null">product_unit_code,</if>
            <if test="productUnitName != null">product_unit_name,</if>
            <if test="certificateEntName != null">certificate_ent_name,</if>
            <if test="certificateEntContactsPhone != null">certificate_ent_contacts_phone,</if>
            <if test="productProvince != null">product_province,</if>
            <if test="productCity != null">product_city,</if>
            <if test="productCounty != null">product_county,</if>
            <if test="productAddress != null">product_address,</if>
            <if test="productDetail != null">product_detail,</if>
            <if test="certificateDate != null">certificate_date,</if>
            <if test="createBy != null">create_by,</if>
            create_time,
            <if test="updateBy != null">update_by,</if>
            update_time,
            <if test="remark != null">remark,</if>
            del_flag,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="entId != null">#{entId},</if>
            <if test="acqurieFlag != null">#{acqurieFlag},</if>
            <if test="scanRecordId != null">#{scanRecordId},</if>
            <if test="fullNumber != null">#{fullNumber},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productNum != null">#{productNum},</if>
            <if test="productUnitCode != null">#{productUnitCode},</if>
            <if test="productUnitName != null">#{productUnitName},</if>
            <if test="certificateEntName != null">#{certificateEntName},</if>
            <if test="certificateEntContactsPhone != null">#{certificateEntContactsPhone},</if>
            <if test="productProvince != null">#{productProvince},</if>
            <if test="productCity != null">#{productCity},</if>
            <if test="productCounty != null">#{productCounty},</if>
            <if test="productAddress != null">#{productAddress},</if>
            <if test="productDetail != null">#{productDetail},</if>
            <if test="certificateDate != null">#{certificateDate},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate(),
            <if test="updateBy != null">#{updateBy},</if>
            sysdate(),
            <if test="remark != null">#{remark},</if>
            '1',
        </trim>
    </insert>

    <update id="updateAcquireRecord" parameterType="com.jkr.project.argi.domain.AcquireRecord">
        update bas_acquire_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="entId != null">ent_id = #{entId},</if>
            <if test="acqurieFlag != null">acqurie_flag = #{acqurieFlag},</if>
            <if test="scanRecordId != null">scan_record_id = #{scanRecordId},</if>
            <if test="fullNumber != null">full_number = #{fullNumber},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productNum != null">product_num = #{productNum},</if>
            <if test="productUnitCode != null">product_unit_code = #{productUnitCode},</if>
            <if test="productUnitName != null">product_unit_name = #{productUnitName},</if>
            <if test="certificateEntName != null">certificate_ent_name = #{certificateEntName},</if>
            <if test="certificateEntContactsPhone != null">certificate_ent_contacts_phone = #{certificateEntContactsPhone},</if>
            <if test="productProvince != null">product_province = #{productProvince},</if>
            <if test="productCity != null">product_city = #{productCity},</if>
            <if test="productCounty != null">product_county = #{productCounty},</if>
            <if test="productAddress != null">product_address = #{productAddress},</if>
            <if test="productDetail != null">product_detail = #{productDetail},</if>
            <if test="certificateDate != null">certificate_date = #{certificateDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate(),
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update bas_acquire_record set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="String">
        update bas_acquire_record set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteAcquireRecordById" parameterType="String">
        delete from bas_acquire_record where id = #{id}
    </delete>

    <delete id="deleteAcquireRecordByIds" parameterType="String">
        delete from bas_acquire_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
