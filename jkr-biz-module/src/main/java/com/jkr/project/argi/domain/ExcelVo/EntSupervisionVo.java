package com.jkr.project.argi.domain.ExcelVo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jkr.framework.web.domain.BaseEntity;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 机构表 sys_dept
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntSupervisionVo extends BaseEntity {
	private static final long serialVersionUID = 1L;

	@Excel(name = "序号")
	private Integer excelNo;
	/**
	 * 机构名称
	 */
	@Excel(name = "部门名称", width = 30)
	private String deptName;

	/**
	 * 部门类型：1农业农村部门，2市场监管部门
	 */
	private String deptType;
	/**
	 * 部门类型：1农业农村部门，2市场监管部门
	 */
	@Excel(name = "部门类型")
	private String deptTypeName;

	/**
	 * 区域编码
	 */
	@Excel(name = "部门编码")
	private String areaCode;
	/**
	 * 部门级别：1省，2市（州），3县（区）
	 */
	@Excel(name = "部门级别", dictType = "area_level")
	private String areaLevel;
	/**
	 * 区域
	 */
	@Excel(name = "行政区域")
	private String areaName;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@Excel(name = "注册日期", width = 20, dateFormat = "yyyy-MM-dd")
	private Date createTime;
	/**
	 * 负责人
	 */
	@Excel(name = "联系人")
	private String leader;

	/**
	 * 联系电话
	 */
	@Excel(name = "联系电话")
	private String phone;

	/**
	 * 机构ID
	 */
	private Long deptId;

	/**
	 * 父机构ID
	 */
	private Long parentId;

	/**
	 * 祖级列表
	 */
	private String ancestors;

	/**
	 * 显示顺序
	 */
	private Integer orderNum;


	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 机构状态:0正常,1停用
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	private String delFlag;
	/**
	 * 监管机构（0非监管，1监管）
	 */
	private String supervisionFlag;

	/**
	 * 父机构名称
	 */
	private String parentName;
	private Date beginCreateDate;    //开始注册时间
	private Date endCreateDate;        //结束注册时间
	private String loginAreaCode;

}
