package com.jkr.project.argi.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import java.util.List;

/**
 * 产品子表(组合品使用)对象 bas_product_item
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
        @Data
        @EqualsAndHashCode(callSuper = true)
        @TableName("bas_product_item")
		public class ProductItem extends BaseModel
		{
		private static final long serialVersionUID = 1L;

				/** 产品表id */
				@Excel(name = "产品表id")
		private String productId;

				/** 子产品id(关联主表单品) */
				@Excel(name = "子产品id(关联主表单品)")
		private String itemProductId;

				/** 子产品名称 */
				@Excel(name = "子产品名称")
		private String itemProductName;

            /** 主键集合 */
            private List<Long> ids;
}
