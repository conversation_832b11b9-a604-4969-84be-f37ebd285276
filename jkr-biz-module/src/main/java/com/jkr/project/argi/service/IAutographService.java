package com.jkr.project.argi.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jkr.project.argi.domain .Autograph;

/**
 * 签名Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IAutographService  extends IService<Autograph> {
	/**
	 * 查询签名
	 *
	 * @param id 签名主键
	 * @return 签名
	 */
	public Autograph selectAutographById(Long id);

	/**
	 * 查询签名列表
	 *
	 * @param autograph 签名
	 * @return 签名集合
	 */
	public List<Autograph> selectAutographList(Autograph autograph);

	/**
	 * 新增签名
	 *
	 * @param autograph 签名
	 * @return 结果
	 */
	public int insertAutograph(Autograph autograph);

	/**
	 * 修改签名
	 *
	 * @param autograph 签名
	 * @return 结果
	 */
	public int updateAutograph(Autograph autograph);

	/**
	 * 批量删除签名
	 *
	 * @param ids 需要删除的签名主键集合
	 * @return 结果
	 */
	public int deleteAutographByIds(List<Long> ids);

	/**
	 * 删除签名信息
	 *
	 * @param id 签名主键
	 * @return 结果
	 */
	public int deleteAutographById(Long id);
	/**
	 * 根据企业ID获取签名
	 * <AUTHOR>
	 * @date 2025/5/22 15:08
	 * @param entId
	 * @since 1.0.0
	 * @return com.jkr.project.argi.domain.Autograph
	 */
	public Autograph getByEntId(String entId);

	public void deleteByEntId(String endId);

}
