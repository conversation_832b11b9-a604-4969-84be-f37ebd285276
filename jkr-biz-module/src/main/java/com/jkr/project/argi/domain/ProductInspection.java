package com.jkr.project.argi.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 产品检测对象 bas_product_inspection
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_product_inspection")
public class ProductInspection extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Excel(name = "主体id")
    private String entId;

    /**
     * 主体名称
     */
    @Excel(name = "主体名称")
    private String entName;

    /**
     * 产品id
     */
    @Excel(name = "产品id")
    private String productId;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称")
    private String productName;

    /**
     * 样品编号
     */
    @Excel(name = "样品编号")
    private String sampleNo;

    /**
     * 二维码url
     */
    @Excel(name = "二维码url")
    private String sampleQrcodeUrl;

    /**
     * 检测编号
     */
    @Excel(name = "检测编号")
    private String recordNo;

    /**
     * 检测情况
     */
    @Excel(name = "检测情况")
    private String inspectionSituation;

    /**
     * 检测项目
     */
    @Excel(name = "检测项目")
    private String inspectionItem;

    /**
     * 检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inspectionDate;

    /**
     * 检测标准
     */
    @Excel(name = "检测标准")
    private String inspectionStandard;

    /**
     * 检测结果
     */
    @Excel(name = "检测结果")
    private String inspectionResult;

    /**
     * 检测人员
     */
    @Excel(name = "检测人员")
    private String inspectionPerson;

    /**
     * 检测说明
     */
    @Excel(name = "检测说明")
    private String inspectionExplain;

    /**
     * 检测单位
     */
    @Excel(name = "检测单位")
    private String inspectionCompany;

    /**
     * 检测单位id(检测单位录入数据时填写)
     */
    @Excel(name = "检测单位id(检测单位录入数据时填写)")
    private String inspectionCompanyId;

    /**
     * 检测数值
     */
    @Excel(name = "检测数值")
    private String inspectionValue;

    /**
     * 合格范围
     */
    @Excel(name = "合格范围")
    private String acceptableRange;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private List<SysFile> fileList;
    @TableField(exist = false)
    private Date startDate;		// 开始时间
    @TableField(exist = false)
    private Date endDate;		// 开始时间
    @TableField(exist = false)
    private String orderBy;
    @TableField(exist = false)
    private List<String> inspectionSituationList;
}
