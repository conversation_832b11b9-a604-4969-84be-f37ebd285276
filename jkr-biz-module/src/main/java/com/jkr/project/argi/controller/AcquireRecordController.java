package com.jkr.project.argi.controller;

import java.util.List;

import com.jkr.common.enums.EnumProperty;
import com.jkr.common.utils.PageUtils;
import com.jkr.project.argi.service.ICertificateNoService;
import com.jkr.project.system.domain.SysArea;
import com.jkr.project.system.service.ISysAreaService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.AcquireRecord;
import com.jkr.project.argi.service.IAcquireRecordService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 收购记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/argi/acquireRecord")
public class AcquireRecordController extends BaseController {

    @Autowired
    private IAcquireRecordService acquireRecordService;
    @Autowired
    private ISysAreaService areaService;
    @Autowired
    private ICertificateNoService certificateNoService;

    /**
     * 查询收购记录列表
     */
    @PreAuthorize("@ss.hasPermi('argi:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(AcquireRecord acquireRecord) {
        startPage();
        List<AcquireRecord> list = acquireRecordService.selectAcquireRecordList(acquireRecord);
        return getDataTable(list);
    }

    @PostMapping("/findAcquireRecordPage")
    public TableDataInfo findAcquireRecordPage(@RequestBody AcquireRecord acquireRecord){
        PageUtils.startPage(acquireRecord.getPageNum(), acquireRecord.getPageSize());
        List<AcquireRecord> list = acquireRecordService.selectAcquireRecordList(acquireRecord);
        return getDataTable(list);
    }

    /**
     * 导出收购记录列表
     */
    @PreAuthorize("@ss.hasPermi('argi:record:export')")
    @Log(title = "导出收购记录列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AcquireRecord acquireRecord) {
        List<AcquireRecord> list = acquireRecordService.selectAcquireRecordList(acquireRecord);
        ExcelUtil<AcquireRecord> util = new ExcelUtil<AcquireRecord>(AcquireRecord.class);
        util.exportExcel(response, list, "收购记录数据");
    }

    /**
     * 获取收购记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:record:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(acquireRecordService.selectAcquireRecordById(id));
    }

    /**
     * 新增收购记录
     */
    //@PreAuthorize("@ss.hasPermi('argi:record:add')")
    @Log(title = "新增收购记录", businessType = BusinessType.INSERT)
    @PostMapping(value = "/save")
    public AjaxResult save(@Validated @RequestBody AcquireRecord acquireRecord) {
        try {
            if (StringUtils.isEmpty(acquireRecord.getAcqurieFlag())) {
                return AjaxResult.error("收购标识不能为空");
            }
            if (StringUtils.equals(acquireRecord.getAcqurieFlag(), EnumProperty.AcqurieFlagEnum.TYPE_1.getKey())) {
                if (StringUtils.isEmpty(acquireRecord.getScanRecordId())) {
                    return AjaxResult.error("查验信息不能为空");
                }
                if (StringUtils.isEmpty(acquireRecord.getFullNumber())) {
                    return AjaxResult.error("合格证号不能为空");
                }
            }
            acquireRecordService.insertAcquireRecord(acquireRecord);
        } catch(Exception e) {
            return AjaxResult.error("保存失败，错误如下：" + e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     * 修改收购记录
     */
    //@PreAuthorize("@ss.hasPermi('argi:record:edit')")
    @Log(title = "修改收购记录", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/update")
    public AjaxResult update(@Validated @RequestBody AcquireRecord acquireRecord) {
        return toAjax(acquireRecordService.updateAcquireRecord(acquireRecord));
    }

    /**
     * 删除收购记录
     */
    @PreAuthorize("@ss.hasPermi('argi:record:remove')")
    @Log(title = "删除收购记录", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(acquireRecordService.deleteAcquireRecordById(id));
    }

    /**
     * 批量删除收购记录
     */
    @PreAuthorize("@ss.hasPermi('argi:record:batchRemove')")
    @Log(title = "批量删除收购记录", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody AcquireRecord acquireRecord) {
        return toAjax(acquireRecordService.deleteAcquireRecordByIds(acquireRecord.getIds()));
    }

    @PostMapping("/getArea")
    public AjaxResult getArea(@RequestBody SysArea area){
        List<SysArea> areaList = areaService.findTreeList(area);
        return AjaxResult.success(areaList);
    }

    @PostMapping("/getCertificateInfoByFullNumber")
    public AjaxResult getCertificateInfoByFullNumber(@RequestParam(required=true) String fullNumber){
        if(StringUtils.isBlank(fullNumber)) {
            return AjaxResult.error("fullName不能为空");
        }
        return AjaxResult.success(certificateNoService.getByFullNumber(fullNumber));
    }
}
