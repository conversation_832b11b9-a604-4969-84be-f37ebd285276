package com.jkr.project.argi.domain.ExcelVo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import com.jkr.project.argi.domain.EntChange;
import com.jkr.project.argi.domain.EntDetail;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 主体信息对象 bas_ent
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EntPersonVo extends BaseModel {
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @Excel(name = "序号")
    private Integer excelNo;
    /**
     * 名称
     */
    @Excel(name = "主体姓名")
    private String name;

    /**
     * 法人
     */
//    @Excel(name = "企业法人")
    private String legalPerson;

    /**
     * 主体类型(0:种植；1:养殖；2.检测机构)
     */
    @Excel(name = "主体类型",dictType = "ent_business_type")
    private String businessType;

    /**
     * 身份标识：0-生产;1-收购;2-生产收购
     */
    @Excel(name = "业务角色",dictType = "identity_type")
    private String identityType;

    @TableField(exist = false)
    @Excel(name = "产品分类")
    private String productSortName;// 产品分类名称
    /**
     * 主体类别
     */
    @Excel(name = "主体类别",dictType = "main_type")
    private String mainType;

    /**
     * 地址(省市县)
     */
    @Excel(name = "所在区域", width = 30)
    private String address;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "注册日期", width = 20, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 开具次数
     */
    @Excel(name = "开具次数")
    private Long certificateAmount;

    /**
     * 开具数量
     */
    @Excel(name = "开具数量")
    private Long certificatePrintAmount;

    /**
     * 开具时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后一次开具日期", width = 20, dateFormat = "yyyy-MM-dd")
    private Date certificatePrintDate;
    /**
     * 主体性质(0:企业；1:个人；2.检测机构)
     */
    private String entType;



    /**
     * 养殖分类 (0:牧业 1:渔业)
     */
//    @Excel(name = "养殖分类 (0:牧业 1:渔业)")
    private String farmType;

    /**
     * 统一社会信用代码
     */
//    @Excel(name = "统一社会信用代码")
    private String socialCode;

    /**
     * 身份证号
     */
//    @Excel(name = "身份证号")
    private String cardNo;

    /**
     * 身份证详细地址（乡村版扫描出来的）
     */
    private String cardDetail;

    /**
     * 联系人
     */
//    @Excel(name = "联系人")
    private String contacts;

    /**
     * 联系人电话
     */
//    @Excel(name = "联系人电话")
    private String contactsPhone;

    /**
     * 省
     */
//    @Excel(name = "省")
    private String province;

    /**
     * 市
     */
//    @Excel(name = "市")
    private String city;

    /**
     * 县区
     */
//    @Excel(name = "县区")
    private String county;

    /**
     * 镇
     */
//    @Excel(name = "镇")
    private String town;

    /**
     * 村
     */
//    @Excel(name = "村")
    private String village;

    /**
     * 详细地址
     */
//    @Excel(name = "详细地址")
    private String detail;

    /**
     * 经度
     */
//    @Excel(name = "经度")
    private String lng;

    /**
     * 纬度
     */
//    @Excel(name = "纬度")
    private String lat;

    /**
     * 企业介绍
     */
//    @Excel(name = "企业介绍")
    private String companyIntroduction;

    /**
     * $column.columnComment
     */
//    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String entHonor;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date submitDate;

    /**
     * 审核状态（0：待审核；1：通过,-1驳回）
     */
//    @Excel(name = "审核状态", readConverterExp = "0=：待审核；1：通过,-1驳回")
    private String examineStatus;

    /**
     * 审核人
     */
//    @Excel(name = "审核人")
    private String examineMan;

    /**
     * 审核意见
     */
//    @Excel(name = "审核意见")
    private String examineOpinion;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date examineDate;

    /**
     * 用户表名称
     */
//    @Excel(name = "用户表名称")
    private String tableName;

    /**
     * 用户表id
     */
//    @Excel(name = "用户表id")
    private String tableId;

    /**
     * 基础信息采集对接标示(0:否 1:是)
     */
//    @Excel(name = "基础信息采集对接标示(0:否 1:是)")
    private String basicFlag;

    /**
     * 基础信息录入标示(0:否 1:是)
     */
//    @Excel(name = "基础信息录入标示(0:否 1:是)")
    private String basicEnterFlag;



    /**
     * 上传检测报告次数(检测机构用)
     */
//    @Excel(name = "上传检测报告次数(检测机构用)")
    private Long inspectionWriteAmount;

    /**
     * 变更业务状态
     */
//    @Excel(name = "变更业务状态")
    private String changeStatus;

    /**
     * 变更后查看标识 0:否 1:是
     */
//    @Excel(name = "变更后查看标识 0:否 1:是")
    private String changeViewFlag;

    /**
     * 变更业务意见
     */
//    @Excel(name = "变更业务意见")
    private String changeOpinion;

    /**
     * 冻结标识 0:否 1:是
     */
//    @Excel(name = "冻结标识 0:否 1:是")
    private String frozenFlag;

    /**
     * 用户id（乡村版合格证用）
     */
//    @Excel(name = "用户id", readConverterExp = "乡=村版合格证用")
    private String userId;

    /**
     * 数据来源（0：正常合格证系统 1：乡村版合格证数据）
     */
//    @Excel(name = "数据来源", readConverterExp = "0=：正常合格证系统,1=：乡村版合格证数据")
    private String dataScope;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
//    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastModified;

    /**
     * 主键集合
     */
    private List<Long> ids;

    @TableField(exist = false)
    private Date beginCreateDate;    //开始注册时间
    @TableField(exist = false)
    private Date endCreateDate;        //结束注册时间
    @TableField(exist = false)
    private Date beginCertificatePrintDate;    //开始开具时间
    @TableField(exist = false)
    private Date endCertificatePrintDate;        //结束开具时间
    @TableField(exist = false)
    private Date beginSubmitDate;    //提交开始时间
    @TableField(exist = false)
    private Date endSubmitDate;        //提交结束时间
    @TableField(exist = false)
    private String orderBy;//排序
    @TableField(exist = false)
    private String exportFlag;//导出标识
    @TableField(exist = false)
    private List<SysFile> fileList = Lists.newArrayList();
    @TableField(exist = false)
    private List<EntChange> changeList = Lists.newArrayList();
    @TableField(exist = false)
    private String autograph;
    @TableField(exist = false)
    private String loginAreaCode;
    @TableField(exist = false)
    private String queryAreaCode;
    @TableField(exist = false)
    private List<EntDetail> entDetailList;

    @TableField(exist = false)
    private String oldContactsPhone;// 电话号

    @TableField(exist = false)
    private String cityName;
    @TableField(exist = false)
    private String countyName;
    @TableField(exist = false)
    private int certificatePrintCount = 0;// 打印合格证次数
    @TableField(exist = false)
    private int certificateCount = 0;// 合格证数量
    @TableField(exist = false)
    private Date startDate;// 查询开始时间
    @TableField(exist = false)
    private Date endDate;// 查询开始时间
    @TableField(exist = false)
    private String productId;// 产品ID
    @TableField(exist = false)
    private boolean regionFlag;// PAD登录时该主体 是否在 当前管理员所在区域里
    @TableField(exist = false)
    private String areaCode;		// 行政区划编码
    @TableField(exist = false)
    private String queryYear;		// 查询年份
    @TableField(exist = false)
    private String queryMonth;		// 查询月

}
