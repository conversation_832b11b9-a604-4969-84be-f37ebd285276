package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Detection;

/**
 * 与快检系统对接Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface DetectionMapper extends BaseMapper<Detection>{
	/**
	 * 查询与快检系统对接
	 *
	 * @param id 与快检系统对接主键
	 * @return 与快检系统对接
	 */
	public Detection selectDetectionById(Long id);

	/**
	 * 查询与快检系统对接列表
	 *
	 * @param detection 与快检系统对接
	 * @return 与快检系统对接集合
	 */
	public List<Detection> selectDetectionList(Detection detection);

	/**
	 * 新增与快检系统对接
	 *
	 * @param detection 与快检系统对接
	 * @return 结果
	 */
	public int insertDetection(Detection detection);

	/**
	 * 修改与快检系统对接
	 *
	 * @param detection 与快检系统对接
	 * @return 结果
	 */
	public int updateDetection(Detection detection);

	/**
	 * 删除与快检系统对接
	 *
	 * @param id 与快检系统对接主键
	 * @return 结果
	 */
	public int deleteDetectionById(Long id);

	/**
	 * 批量删除与快检系统对接
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteDetectionByIds(Long[] ids);

	/**
	 * 批量逻辑删除与快检系统对接
	 *
	 * @param  ids 与快检系统对接主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过与快检系统对接主键id逻辑删除信息
	 *
	 * @param  id 与快检系统对接主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
