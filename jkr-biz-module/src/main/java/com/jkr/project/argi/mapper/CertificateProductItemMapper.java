package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateProductItem;
import org.apache.ibatis.annotations.Param;

/**
 * 合格证产品子表(组合品使用)Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateProductItemMapper extends BaseMapper<CertificateProductItem>{
	/**
	 * 查询合格证产品子表(组合品使用)
	 *
	 * @param id 合格证产品子表(组合品使用)主键
	 * @return 合格证产品子表(组合品使用)
	 */
	public CertificateProductItem selectCertificateProductItemById(Long id);

	/**
	 * 查询合格证产品子表(组合品使用)列表
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 合格证产品子表(组合品使用)集合
	 */
	public List<CertificateProductItem> selectCertificateProductItemList(CertificateProductItem certificateProductItem);

	/**
	 * 新增合格证产品子表(组合品使用)
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 结果
	 */
	public int insertCertificateProductItem(CertificateProductItem certificateProductItem);

	/**
	 * 修改合格证产品子表(组合品使用)
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 结果
	 */
	public int updateCertificateProductItem(CertificateProductItem certificateProductItem);

	/**
	 * 删除合格证产品子表(组合品使用)
	 *
	 * @param id 合格证产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int deleteCertificateProductItemById(Long id);

	/**
	 * 批量删除合格证产品子表(组合品使用)
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateProductItemByIds(Long[] ids);

	/**
	 * 批量逻辑删除合格证产品子表(组合品使用)
	 *
	 * @param  ids 合格证产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过合格证产品子表(组合品使用)主键id逻辑删除信息
	 *
	 * @param  id 合格证产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public int insertBatch(@Param("list") List<CertificateProductItem> list);
}
