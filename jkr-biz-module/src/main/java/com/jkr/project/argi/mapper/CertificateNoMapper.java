package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateNo;
import org.apache.ibatis.annotations.Param;

/**
 * 合格证流水Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateNoMapper extends BaseMapper<CertificateNo>{
	/**
	 * 查询合格证流水
	 *
	 * @param id 合格证流水主键
	 * @return 合格证流水
	 */
	public CertificateNo selectCertificateNoById(Long id);

	/**
	 * 查询合格证流水列表
	 *
	 * @param certificateNo 合格证流水
	 * @return 合格证流水集合
	 */
	public List<CertificateNo> selectCertificateNoList(CertificateNo certificateNo);

	/**
	 * 新增合格证流水
	 *
	 * @param certificateNo 合格证流水
	 * @return 结果
	 */
	public int insertCertificateNo(CertificateNo certificateNo);

	/**
	 * 修改合格证流水
	 *
	 * @param certificateNo 合格证流水
	 * @return 结果
	 */
	public int updateCertificateNo(CertificateNo certificateNo);

	/**
	 * 删除合格证流水
	 *
	 * @param id 合格证流水主键
	 * @return 结果
	 */
	public int deleteCertificateNoById(Long id);

	/**
	 * 批量删除合格证流水
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateNoByIds(Long[] ids);

	/**
	 * 批量逻辑删除合格证流水
	 *
	 * @param  ids 合格证流水主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过合格证流水主键id逻辑删除信息
	 *
	 * @param  id 合格证流水主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	List<CertificateNo> getFirstByCertificateId(@Param(value = "certificateId")String certificateId);

	public Integer getMaxSerialNumber(@Param("batchNo") String batchNo);

	public int insertBatch(@Param("list")List<CertificateNo> list);

	int updateBlockChainId(@Param(value = "id")String id,@Param(value = "blockChainId")String blockChainId);

	int updatePrintCount(@Param(value = "id")String id);

}
