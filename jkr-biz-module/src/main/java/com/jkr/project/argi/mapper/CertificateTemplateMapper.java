package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateTemplate;

/**
 * 合格证模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateTemplateMapper extends BaseMapper<CertificateTemplate>{
	/**
	 * 查询合格证模板
	 *
	 * @param id 合格证模板主键
	 * @return 合格证模板
	 */
	public CertificateTemplate selectCertificateTemplateById(Long id);

	/**
	 * 查询合格证模板列表
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 合格证模板集合
	 */
	public List<CertificateTemplate> selectCertificateTemplateList(CertificateTemplate certificateTemplate);

	/**
	 * 新增合格证模板
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 结果
	 */
	public int insertCertificateTemplate(CertificateTemplate certificateTemplate);

	/**
	 * 修改合格证模板
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 结果
	 */
	public int updateCertificateTemplate(CertificateTemplate certificateTemplate);

	/**
	 * 删除合格证模板
	 *
	 * @param id 合格证模板主键
	 * @return 结果
	 */
	public int deleteCertificateTemplateById(Long id);

	/**
	 * 批量删除合格证模板
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateTemplateByIds(Long[] ids);

	/**
	 * 批量逻辑删除合格证模板
	 *
	 * @param  ids 合格证模板主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过合格证模板主键id逻辑删除信息
	 *
	 * @param  id 合格证模板主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
