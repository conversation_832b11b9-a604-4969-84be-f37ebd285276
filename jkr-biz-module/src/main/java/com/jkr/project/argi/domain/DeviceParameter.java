package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 蓝牙设备参数对象 bas_device_parameter
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_device_parameter")
public class DeviceParameter extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 指令
     */
    @Excel(name = "指令")
    private String command;

    /**
     * 图片
     */
    @Excel(name = "图片")
    private String imageUrl;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;
	@TableField(exist = false)
	private List<CertificateTemplate> templateList;

    @TableField(exist = false)
    private List<SysFile> deviceImageFiles;
}
