package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateTemplate;
import com.jkr.project.argi.service.ICertificateTemplateService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 合格证模板Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateTemplate")
public class CertificateTemplateController extends BaseController {
    @Autowired
    private ICertificateTemplateService certificateTemplateService;

    /**
     * 查询合格证模板列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertificateTemplate certificateTemplate) {
        startPage();
        List<CertificateTemplate> list = certificateTemplateService.selectCertificateTemplateList(certificateTemplate);
        return getDataTable(list);
    }

    /**
     * 导出合格证模板列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:export')")
    @Log(title = "导出合格证模板列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertificateTemplate certificateTemplate) {
        List<CertificateTemplate> list = certificateTemplateService.selectCertificateTemplateList(certificateTemplate);
        ExcelUtil<CertificateTemplate> util = new ExcelUtil<CertificateTemplate>(CertificateTemplate.class);
        util.exportExcel(response, list, "合格证模板数据");
    }

    /**
     * 获取合格证模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(certificateTemplateService.selectCertificateTemplateById(id));
    }

    /**
     * 新增合格证模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:add')")
    @Log(title = "新增合格证模板", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody CertificateTemplate certificateTemplate) {
        return toAjax(certificateTemplateService.insertCertificateTemplate(certificateTemplate));
    }

    /**
     * 修改合格证模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:edit')")
    @Log(title = "修改合格证模板", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody CertificateTemplate certificateTemplate) {
        return toAjax(certificateTemplateService.updateCertificateTemplate(certificateTemplate));
    }

    /**
     * 删除合格证模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:remove')")
    @Log(title = "删除合格证模板", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(certificateTemplateService.deleteCertificateTemplateById(id));
    }

    /**
     * 批量删除合格证模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateTemplate:batchRemove')")
    @Log(title = "批量删除合格证模板", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody CertificateTemplate certificateTemplate) {
        return toAjax(certificateTemplateService.deleteCertificateTemplateByIds(certificateTemplate.getIds()));
    }
}
