package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.ProductStorage;
import com.jkr.project.argi.service.IProductStorageService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 产品保质方式Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/productStorage")
public class ProductStorageController extends BaseController {

    @Autowired
    private IProductStorageService productStorageService;

    /**
     * 查询产品保质方式列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductStorage productStorage) {
        startPage();
        List<ProductStorage> list = productStorageService.selectProductStorageList(productStorage);
        return getDataTable(list);
    }

    @PostMapping("/infoList")
    public AjaxResult infoList(@RequestBody ProductStorage productStorage){
        return AjaxResult.success(productStorageService.selectProductStorageList(productStorage));
    }

    /**
     * 导出产品保质方式列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:export')")
    @Log(title = "导出产品保质方式列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductStorage productStorage) {
        List<ProductStorage> list = productStorageService.selectProductStorageList(productStorage);
        ExcelUtil<ProductStorage> util = new ExcelUtil<ProductStorage>(ProductStorage.class);
        util.exportExcel(response, list, "产品保质方式数据");
    }

    /**
     * 获取产品保质方式详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productStorageService.selectProductStorageById(id));
    }

    /**
     * 新增产品保质方式
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:add')")
    @Log(title = "新增产品保质方式", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody ProductStorage productStorage) {
        return toAjax(productStorageService.insertProductStorage(productStorage));
    }

    /**
     * 修改产品保质方式
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:edit')")
    @Log(title = "修改产品保质方式", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody ProductStorage productStorage) {
        return toAjax(productStorageService.updateProductStorage(productStorage));
    }

    /**
     * 删除产品保质方式
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:remove')")
    @Log(title = "删除产品保质方式", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(productStorageService.deleteProductStorageById(id));
    }

    /**
     * 批量删除产品保质方式
     */
    @PreAuthorize("@ss.hasPermi('argi:productStorage:batchRemove')")
    @Log(title = "批量删除产品保质方式", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody ProductStorage productStorage) {
        return toAjax(productStorageService.deleteProductStorageByIds(productStorage.getIds()));
    }
}
