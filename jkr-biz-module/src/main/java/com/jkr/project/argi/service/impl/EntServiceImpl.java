package com.jkr.project.argi.service.impl;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jkr.common.constant.Constants;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.enums.EnumSubject;
import com.jkr.common.enums.MainTypeEnum;
import com.jkr.common.utils.*;
import com.jkr.project.argi.domain.Autograph;
import com.jkr.project.argi.domain.EntChange;
import com.jkr.project.argi.domain.ExcelVo.EntEnterpriseVo;
import com.jkr.project.argi.domain.ExcelVo.EntPersonVo;
import com.jkr.project.argi.domain.Product;
import com.jkr.project.argi.service.*;
import com.jkr.project.system.domain.SysDept;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.ISysDeptService;
import com.jkr.project.system.service.ISysFileService;
import com.jkr.project.system.service.ISysUserService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.EntMapper;
import com.jkr.project.argi.domain.Ent;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主体信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class EntServiceImpl extends ServiceImpl<EntMapper, Ent> implements IEntService {
	public static final String ENT_CACHE = "userCache";
	public static final String ENT_CACHE_TABLEID_ = "tableId_";
	@Autowired
	private EntMapper entMapper;
	@Autowired
	private ISysFileService fileService;
	@Lazy
	@Autowired
	private IEntDetailService entDetailService;
	@Autowired
	private IEntChangeService entChangeService;
	@Autowired
	private IAutographService autographService;
	@Autowired
	private ISysUserService  sysUserService;
	@Autowired
	private ISysDeptService sysDeptService;
	@Autowired
	private IProductService productService;

	/**
	 * 查询主体信息
	 *ta
	 * @param id 主体信息主键
	 * @return 主体信息
	 */
	@Override
	public Ent selectEntById(Long id) {
		Ent ent = entMapper.selectEntById(id);
		SysFile attachment = new SysFile();
		attachment.setTableName(PropertyEnum.FileTypeEnum.FT_3.getLabel());
		//获取附件信息
		attachment.setTableId(ent.getId());
		List<SysFile> attachmentList = fileService.findUrlsFileList(attachment);
		ent.setFileList(attachmentList);
		ent.setChangeList(entChangeService.findPassList(ent.getId()));
//		ent.setAutograph(autographService.getByEntId(String.valueOf(ent.getId())));
		if(EnumProperty.EntTypeEnum.PERSON.getKey().equals(ent.getEntType())){
			Autograph autograph = autographService.getByEntId(String.valueOf(ent.getId()));
			if(null!=autograph){
				ent.setAutograph(autograph.getAutograph());
			}
		}
		Product product = new Product();
		product.setEntId(String.valueOf(ent.getId()));
		List<Product> products = productService.selectProductList(product);
		ent.setProductList(products);
		return ent;
	}

	/**
	 * 查询主体信息列表
	 *
	 * @param ent 主体信息
	 * @return 主体信息
	 */
	@Override
	public List<Ent> selectEntList(Ent ent) {
		return entMapper.selectEntList(ent);
	}
	@Override
	public List<Ent> findListQuick(Ent ent) {
		// 数据范围
		if(!Constants.BUSINESS_SYSTEM_FLAG.equals(SecurityUtils.getLoginUser().getUsername())){
			ent.setLoginAreaCode(SecurityUtils.getLoginUser().getUser().getDept().getAreaCode());
		}
		return entMapper.findListQuick(ent);
	}

	/**
	 *
	 * @Title: getEntStatistic
	 * @author: LJX
	 * @date: 2020年7月19日 下午6:06:23
	 * @Description: 企业基本情况统计
	 * @param:  county
	 * @return: Map<String,Object>
	 * @throws
	 */
	public Map<String,Object> getEntStatistic(String county) {
		Ent ent = new Ent();
		ent.setCounty(county);
		if(!Constants.BUSINESS_SYSTEM_FLAG.equals(SecurityUtils.getLoginUser().getUsername())){
			ent.setLoginAreaCode(SecurityUtils.getLoginUser().getUser().getDept().getAreaCode());
		}
		return entMapper.getEntStatistic(ent);
	}

	/**
	 *
	 * @Title: verificationSocialCode
	 * @author: LJX
	 * @date: 2020年7月19日 上午8:45:35
	 * @Description: 检验统一社会信用代码唯一性
	 * @param:  socialCode
	 * @return: Boolean
	 * @throws
	 */
	@Override
	public Boolean verificationSocialCode(String socialCode) {
		Ent ent = new Ent();
		ent.setSocialCode(socialCode);
		ent.setId(SecurityUtils.getLoginUser().getUserId());
		if(entMapper.getBySocialCodeOrCardNo(ent)>0) {
			return false;
		}
		return true;
	}
	@Override
	public boolean checkSocialCode(Ent ent){
		if(ObjectUtil.isNotNull(ent.getId())){
			ent.setId(Long.valueOf(ent.getSocialCode()));
		}
		if(entMapper.getBySocialCodeOrCardNo(ent)>0) {
			return false;
		}
		return true;
	}

	/**
	 * 新增主体信息
	 *
	 * @param ent 主体信息
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertEnt(Ent ent) {
		ent.insertInit(SecurityUtils.getLoginUser().getUsername());

			return entMapper.insertEnt(ent);
	}

	/**
	 * 修改主体信息
	 *
	 * @param ent 主体信息
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateEnt(Ent ent) {
		ent.updateInit(SecurityUtils.getLoginUser().getUsername());

		return entMapper.updateEnt(ent);
	}

	/**
	 * 批量删除主体信息
	 *
	 * @param ids 需要删除的主体信息主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteEntByIds(List<Long> ids) {
		return entMapper.logicRemoveByIds(ids);
		//return entMapper.deleteEntByIds(ids);
	}

	/**
	 * 删除主体信息信息
	 *
	 * @param id 主体信息主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteEntById(Long id) {
		return entMapper.logicRemoveById(id);
		//return entMapper.deleteEntById(id);
	}

	/**
	 *@Title: saveEnt
	 *@Description: 主体保存
	 *@Param: ent
	 *@return:
	 * @throws Exception
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public String saveEnt(Ent ent){
		//第一次提交和重新提交需要赋值提交时间
		if("0".equals(ent.getExamineStatus())){
			ent.setSubmitDate(new Date());
		}
		//保存主体信息
		ent.setTableId(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
		ent.setTableName("sys_user");

		save(ent);
		//先删除原有附件
		SysFile attachment = new SysFile();
		attachment.setTableId(ent.getId());
		attachment.setTableName(EnumProperty.TableNameEnum.ENT.getKey());
		attachment.setFieldType("fieldType");
		List<SysFile> fileList = fileService.findUrlsFileList(attachment);
		if (CollUtil.isNotEmpty(fileList)) {
			fileService.deleteSysFileByIds(fileList.stream().map(SysFile::getFileId).collect(Collectors.toList()).toArray(Long[]::new));
		}
		//保存附件
		List<SysFile> finalList = ent.getFileList();
		if (!finalList.isEmpty()) {
			for (SysFile att : finalList) {
				att.setTableId(ent.getId());
				att.setTableName(EnumProperty.TableNameEnum.ENT.getKey());
				att.setFieldType("fieldType");
			}
			fileService.insertSysFileList(finalList);
		}

		if(EnumProperty.EntTypeEnum.PERSON.getKey().equals(ent.getEntType())){
			autographService.deleteByEntId(String.valueOf(ent.getId()));
			Autograph autograph = new Autograph();
			autograph.setAutograph(ent.getAutograph());
			autograph.setEntId(String.valueOf(ent.getId()));
			autographService.save(autograph);
		}
		//同步保存机构表数据
		sysDeptService.deleteDeptById(ent.getId());
		SysDept dept=new SysDept();
		dept.setDeptId(ent.getId());
		dept.setDeptName(ent.getName());
		dept.setLeader(ent.getContacts());
		dept.setPhone(ent.getContactsPhone());
		dept.setSupervisionFlag(Constants.NO);
		dept.setAreaName(ent.getAddress());
		String areaCode=ent.getProvince();
		dept.setAreaLevel(EnumProperty.AreaLevelEnum.PROVINCE.getKey());
//		if (StringUtils.isNotBlank(ent.getVillage())){
//			areaCode=ent.getVillage();
//		}else if (StringUtils.isNotBlank(ent.getTown())){
//			areaCode=ent.getTown();
//		}else
			if (StringUtils.isNotBlank(ent.getCounty())){
			areaCode=ent.getCounty();
			dept.setAreaLevel(EnumProperty.AreaLevelEnum.COUNTY.getKey());
		}else if (StringUtils.isNotBlank(ent.getCity())){
			areaCode=ent.getCity();
			dept.setAreaLevel(EnumProperty.AreaLevelEnum.CITY.getKey());
		}
		dept.setAreaCode(areaCode);
		sysDeptService.insertEntDept(dept);
		// 主体信息子表
		entDetailService.deleteByEntId(String.valueOf(ent.getId()));
		if(CollectionUtils.isNotEmpty(ent.getEntDetailList())){
			entDetailService.saveEntBatch(ent.getEntDetailList(), String.valueOf(ent.getId()));
		}
		return String.valueOf(ent.getId());
	}

	/**
	 *
	 * @title: checkIdCard
	 * @description: 身份证号校验
	 * @param: @param ent
	 * @return: ResponseResult
	 */
	@Override
	public boolean checkIdCard(Ent ent){
		Long count = lambdaQuery().eq(Ent::getCardNo,ent.getCardNo()).ne(Ent::getId,ent.getId()).count();
		if(count > 0) {
			return false;
		}else{
			return true;
		}
	}
	/**
	 *
	 * @title: getByTableId
	 * @author: lxy
	 * @date: 2020年7月21日 下午1:40:55
	 * @description: 根据tableId获取ent对象
	 * @param: @param tableId
	 * @param: @return
	 * @return: Ent
	 */
	@Override
	public Ent getByTableId(String tableId) {
		if(StringUtils.isBlank(tableId)) {
			return null;
		}
		Ent ent = (Ent) CacheUtils.get(ENT_CACHE, ENT_CACHE_TABLEID_ + tableId);
		if (ent ==  null){
			ent = entMapper.getByTableId(tableId);
			if (ent == null){
				return null;
			}
			CacheUtils.put(ENT_CACHE, ENT_CACHE_TABLEID_ + ent.getTableId(), ent);
		}
		return ent;
	}

	/**
	 *
	 * @Title: examineSave
	 * @author: LJX
	 * @date: 2020年7月22日 下午7:40:07
	 * @Description: 审批保存
	 * @param:  ent
	 * @throws
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void examineSave(Ent ent) {
		ent.updateInit(SecurityUtils.getUsername());
		ent.setExamineDate(new Date());
		ent.setExamineMan(SecurityUtils.getLoginUser().getUser().getNickName());
		entMapper.examineSave(ent);
//		this.clearCache(ent.getTableId());
	}
	/**
	 *
	 * @title: findMainTypeList
	 * @description: 获取主体类别集合
	 * @param: @param entType
	 * @param: @return
	 * @return: List<Map<String,Object>>
	 */
	@Override
	public List<EnumSubject> findMainTypeList(String entType){
		if(StringUtils.isBlank(entType)) {
			return null;
		}
		List<EnumSubject> list=new ArrayList<EnumSubject>();
		if(entType.equals(PropertyEnum.EntTypeEnum.ET_0.getKey())) {
			// 企业
			for(MainTypeEnum.EntEnum e : MainTypeEnum.EntEnum.values()){
				EnumSubject subject=new EnumSubject(e.getKey(), e.getValue());
				list.add(subject);
			}
		}else if(entType.equals(PropertyEnum.EntTypeEnum.ET_1.getKey())){
			// 个人
			for(MainTypeEnum.PersonEnum e : MainTypeEnum.PersonEnum.values()){
				EnumSubject subject=new EnumSubject(e.getKey(), e.getValue());
				list.add(subject);
			}
		}
//		else if(entType.equals(PropertyEnum.EntTypeEnum.ET_2.getKey())){
//			// 机构
//			for(MainTypeEnum.CompanyEnum e : MainTypeEnum.CompanyEnum.values()){
//				EnumSubject subject=new EnumSubject(e.getKey(), e.getValue());
//				list.add(subject);
//			}
//		}
		return list;
	}
	/**
	 *
	 * @title: updateCertificateAmount
	 * @author: lxy
	 * @date: 2021年1月13日 下午4:24:34
	 * @description: 更新主体开具次数、开具数量信息
	 * @param: id
	 * @param: amount
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int updateCertificateAmount(String id,Integer amount) {
		if(StringUtils.isBlank(id) || null ==amount || amount.equals(0)) {
			return 0;
		}
		return entMapper.updateCertificateAmount(id, amount);
	}
	/**
	 * 更新主体开具数量信息（重复打印 不更新开具次数）
	 * @param id
	 * @param amount
	 * @return
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int updateCertificatePrintAmount(String id,Integer amount) {
		if(StringUtils.isBlank(id) || null ==amount || amount.equals(0)) {
			return 0;
		}
		return entMapper.updateCertificatePrintAmount(id, amount);
	}

	/**
	 * 更新上传检测报告次数
	 * @param id
	 * @return
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int updateInspectionWriteAmount(String id) {
		if(StringUtils.isBlank(id)) {
			return 0;
		}
		return entMapper.updateInspectionWriteAmount(id);
	}

	/**
	 *
	 * @title: updateExamineStatusChangeTemp
	 * @author: lxy
	 * @date: 2021年2月25日 下午7:41:08
	 * @description: 更新状态：变更暂存
	 * @param: @param entId
	 * @return: void
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void updateExamineStatusChangeTemp(String entId) {
		if(StringUtils.isBlank(entId)) {
			return ;
		}
		Ent ent=this.getById(entId);
		if(null!=ent) {
			ent.setExamineStatus(EnumProperty.ExamineStatusEnum.CHANGETEMP.getKey());
			//每次变更申请时，清空相关属性，防止记录数据混淆
			ent.setChangeStatus("");
			ent.setChangeOpinion("");
			ent.setChangeViewFlag("");
			this.save(ent);
		}
	}
	/**
	 *
	 * @title: clearCache
	 * @author: lxy
	 * @date: 2021年2月24日 下午7:23:57
	 * @description: 清除主体缓存
	 * @param: @param tableId
	 * @return: void
	 */
	@Override
	public void clearCache(String tableId) {
		CacheUtils.remove(ENT_CACHE, ENT_CACHE_TABLEID_ + tableId);
	}

	/**
	 *
	 * @title: updateEntChange
	 * @author: lxy
	 * @date: 2021年2月25日 下午2:57:17
	 * @description: 主体变更 更新方法
	 * @param: entChange
	 * @return: void
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void updateEntChange(EntChange entChangeExamine) {
		EntChange entChange = entChangeService.selectEntChangeById(entChangeExamine.getId());
		if(StringUtils.isNotBlank(entChange.getEntId())) {
			Ent ent =entMapper.selectEntById(Long.valueOf(entChange.getEntId()));
			if(null!=ent) {
				//变更审核通过 更新主体信息
				if(EnumProperty.ExamineStatusEnum.PASS.getKey().equals(entChange.getExamineStatus())) {
					//基本信息
					ent.setName(entChange.getName());
					ent.setBusinessType(entChange.getBusinessType());
					ent.setEntType(entChange.getEntType());
					ent.setIdentityType(entChange.getIdentityType());
					ent.setMainType(entChange.getMainType());
					ent.setFarmType(entChange.getFarmType());
					ent.setSocialCode(entChange.getSocialCode());
					ent.setCardNo(entChange.getCardNo());
					ent.setLegalPerson(entChange.getLegalPerson());
					ent.setContacts(entChange.getContacts());
					ent.setContactsPhone(entChange.getContactsPhone());
					ent.setProvince(entChange.getProvince());
					ent.setCity(entChange.getCity());
					ent.setCounty(entChange.getCounty());
					ent.setAddress(entChange.getAddress());
					ent.setDetail(entChange.getDetail());
					ent.setLng(entChange.getLng());
					ent.setLat(entChange.getLat());
					ent.setCompanyIntroduction(entChange.getCompanyIntroduction());
					ent.setEntHonor(entChange.getEntHonor());

					//更新机构表信息
					SysDept dept = new SysDept();
					dept.setUpdateBy(entChange.getUpdateBy());
					dept.setUpdateTime(new Date());
					dept.setDeptId(Long.valueOf(entChange.getEntId()));
					dept.setDeptName(ent.getName());
					dept.setLeader(ent.getContacts());
					dept.setPhone(ent.getContactsPhone());
					dept.setSupervisionFlag(Constants.NO);
					dept.setAreaName(ent.getAddress());
					dept.setAreaLevel(EnumProperty.AreaLevelEnum.PROVINCE.getKey());
					String areaCode=ent.getProvince();
					// TODO 变更表是三级行政区划
//					if (StringUtils.isNotBlank(ent.getVillage())){
//						areaCode=ent.getVillage();
//					}else if (StringUtils.isNotBlank(ent.getTown())){
//						areaCode=ent.getTown();
//					}else
						if (StringUtils.isNotBlank(ent.getCounty())){
						areaCode=ent.getCounty();
						dept.setAreaLevel(EnumProperty.AreaLevelEnum.COUNTY.getKey());
					}else if (StringUtils.isNotBlank(ent.getCity())){
						areaCode=ent.getCity();
						dept.setAreaLevel(EnumProperty.AreaLevelEnum.CITY.getKey());
					}
					dept.setAreaCode(areaCode);
					sysDeptService.updateEntDept(dept);

					//个人主体更新签名
					if(EnumProperty.EntTypeEnum.PERSON.getKey().equals(entChange.getEntType())){
						autographService.deleteByEntId(String.valueOf(ent.getId()));
						Autograph autograph = new Autograph();
						autograph.insertInit(ent.getCreateBy());
						autograph.setAutograph(entChange.getAutograph());
						autograph.setEntId(String.valueOf(ent.getId()));
						autographService.save(autograph);
					}
					//附件
					//先删除原有附件
					SysFile attachment = new SysFile();
					attachment.setTableId(ent.getId());
					attachment.setTableName(EnumProperty.TableNameEnum.ENT.getKey());
					attachment.setFieldType("fieldType");
					List<SysFile> fileList = fileService.findUrlsFileList(attachment);
					if (CollUtil.isNotEmpty(fileList)) {
						fileService.deleteSysFileByIds(fileList.stream().map(SysFile::getFileId).collect(Collectors.toList()).toArray(Long[]::new));
					}
					//保存附件
					attachment.setTableId(entChange.getId());
					attachment.setTableName(EnumProperty.TableNameEnum.ENT_CHANGE.getKey());
					List<SysFile> list=fileService.findUrlsFileList(attachment);
					List<SysFile> attList=new ArrayList<SysFile>();
					if (list!=null && !list.isEmpty()) {
						for (SysFile att : list) {
							SysFile entAtt=new SysFile();
							BeanUtil.copyProperties(att, entAtt, "");
							entAtt.setTableId(ent.getId());
							entAtt.setTableName(EnumProperty.TableNameEnum.ENT.getKey());
							entAtt.setFieldType("fieldType");
							attList.add(entAtt);

						}
					}
					//判断附件有效性
					if(!attList.isEmpty()) {
						fileService.insertSysFileList(list);
					}
					ent.setExamineStatus(EnumProperty.ExamineStatusEnum.PASS.getKey());
				}
				ent.setChangeStatus(entChangeExamine.getExamineStatus());
				ent.setChangeOpinion(entChangeExamine.getExamineOpinion());
				ent.setChangeViewFlag(Constants.NO);
				updateEnt(ent);
				//清除主体缓存
//				this.clearCache(ent.getTableId());

			}
		}
	}

	/**
	 *
	 * @title: updateChangeView
	 * @author: lxy
	 * @date: 2021年3月6日15:55:16
	 * @description: 主体变更业务查看状态更新
	 * @param: @param ent
	 * @param: @return
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int updateChangeView() {
		Ent ent=this.getByTableId(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
		//清除主体缓存
		this.clearCache(ent.getTableId());
		ent.updateInit(SecurityUtils.getUsername());
		return baseMapper.updateChangeView(ent);
	}

	/**
	 *
	 * @title: updateInvalidCertificateAmount
	 * @author: lxy
	 * @date: 2021年1月13日 下午4:24:34
	 * @description: 合格证作废，更新主体开具次数、开具数量信息
	 * @param: id
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void updateInvalidCertificateAmount(String id) {
		if(StringUtils.isBlank(id)) {
			return;
		}
		Ent ent=getById(id);
		if(null==ent) {
			return;
		}
		//清除主体缓存
		this.clearCache(ent.getTableId());
		baseMapper.updateInvalidCertificateAmount(id);
	}
	/**
	 *
	 * @title: frozen
	 * @author: lxy
	 * @date: 2021年3月6日15:55:16
	 * @description: 主体-冻结
	 * @param: ent
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int frozen(Ent ent) {
		ent.setFrozenFlag(Constants.YES);
		//清除主体缓存
		this.clearCache(ent.getTableId());
		ent.updateInit(SecurityUtils.getUsername());
		return baseMapper.updateFrozen(ent);
	}
	/**
	 *
	 * @title: cancelFrozen
	 * @author: lxy
	 * @date: 2021年3月6日15:55:16
	 * @description: 主体-取消冻结
	 * @param: ent
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int cancelFrozen(Ent ent) {
		ent.setFrozenFlag(Constants.NO);
		//清除主体缓存
		this.clearCache(ent.getTableId());
		ent.updateInit(SecurityUtils.getUsername());
		return baseMapper.updateFrozen(ent);
	}
	/**
	 *
	 * @title: updateTableId
	 * @author: lxy
	 * @date: 2021年3月6日15:55:16
	 * @description: 主体更新tableId
	 * @param: ent
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public int updateTableId(String id,String tableId) {
		if(StringUtils.isBlank(id) || StringUtils.isBlank(tableId)) {
			return 0;
		}
		//清除主体缓存
		this.clearCache(tableId);
		Ent ent=new Ent();
		ent.setId(Long.valueOf(id));
		ent.setTableId(tableId);
		return baseMapper.updateTableId(ent);
	}
	/**
	 *
	 * @title: validateOnly
	 * @author: wanghe
	 * @date: 2021年9月10日 下午4:40:11
	 * @param:  entity
	 * @description: 手机号唯一
	 * @return: ResponseResult
	 * @throws
	 */
	@Override
	public Integer validateOnly(Ent entity) {
		return entMapper.validateOnly(entity);
	}
	/**
	 * 校验身份证号唯一
	 *4
	 * <AUTHOR>
	 * @date 2021年09月15日 18:29:02
	 * @param entity
	 * @return java.lang.Integer
	 */
	@Override
	public Integer validateIdCard(Ent entity){
		return entMapper.validateIdCardOnly(entity);
	}

	/**
	 * 根据身份证号获取主体信息
	 *
	 * <AUTHOR>
	 * @date 2021年09月15日 18:52:28
	 * @param idCard
	 * @return com.thinkgem.jeesite.modules.bas.entity.Ent
	 */
	@Override
	public Ent getEntByIdCard(String idCard){
		return entMapper.getEntByIdCard(idCard);
	}
	/**
	 * 根据tableId查询主体信息
	 *
	 * <AUTHOR>
	 * @date 2021年09月27日 10:53:33
	 * @param tableId
	 * @return com.thinkgem.jeesite.modules.bas.entity.Ent
	 */
	@Override
	public Ent getEntByTableId(String tableId){
		return entMapper.getEntByTableId(tableId);
	}
	/**
	 * TODO 根据手机号 判断 主体是否已经存在 存在校验类型 是否是企业 是不让开证
	 *
	 * <AUTHOR>
	 * @date 2021年09月27日 16:07:12
	 * @Param [phoneNumber, userId]
	 * @return java.lang.String
	 */
	@Override
	public String phoneCheck(String phoneNumber,String userId){
		//查询手机号是否存在
		SysUser user=sysUserService.selectUserByUserNameOrMobile(phoneNumber);
		if(null != user){
			//判断手机号 所属主体 类型是否是企业
			Ent ent  = this.getByTableId(String.valueOf(user.getUserId()));
			if(null == ent){// 如果没查到主体 证明 只是在小程序登录了 没有注册信息
				return "2";
			}
			String entType = ent.getEntType();
			if(PropertyEnum.EntTypeEnum.ET_0.getKey().equals(entType)){
				return "0";
			}
		}
		return "2";
	}
	/**
	 * 企业信息导出
	 * @Title: exportExcel
	 * @Author: HeXingChen
	 * @Date: 2022年06月07日 11:46
	 * @param ent
	 * @param response
	 * @return: void
	 */
	@Override
	public void exportExcel(Ent ent, HttpServletResponse response) throws IOException {
		ent.setExamineStatus(PropertyEnum.FlagEnum.TRUE.getKey());
		// 数据范围
		if(!Constants.BUSINESS_SYSTEM_FLAG.equals(SecurityUtils.getLoginUser().getUsername())){
			ent.setLoginAreaCode(SecurityUtils.getLoginUser().getUser().getDept().getAreaCode());
		}
		ent.setExportFlag("export");
		List<Ent> dataList = entMapper.findListQuick(ent);
		List<List<Object>> rows = new ArrayList<>();
		List<Object> row;
		if(PropertyEnum.EntTypeEnum.ET_1.getKey().equals(ent.getEntType())) {
			row = CollUtil.newArrayList("序号",
					"主体姓名",
					"联系电话",
					"所在区域",
					"详细地址",
					"主体类型",
					"产品分类",
					"主体类别",
					"注册日期",
					"开证次数",
					"开证数量",
					"最后一次开证日期");
		}else if(PropertyEnum.EntTypeEnum.ET_0.getKey().equals(ent.getEntType())){
			row = CollUtil.newArrayList("序号",
					"企业名称",
					"企业法人",
					"统一社会信用代码",
					"企业联系人",
					"联系电话",
					"所在区域",
					"详细地址",
					"主体类型",
					"产品分类",
					"主体类别",
					"注册日期",
					"开证次数",
					"开证数量",
					"最后一次开证日期");
		}else{
			row = CollUtil.newArrayList("序号",
					"企业名称",
					"企业法人",
					"统一社会信用代码",
					"企业联系人",
					"联系电话",
					"所在区域",
					"详细地址",
					"主体类型",
					"主体类别",
					"注册日期",
					"上传检测文件次数");
		}
		rows.add(row);
		if (dataList != null && !dataList.isEmpty()) {
			int i = 1;
			for (Ent data : dataList) {
				if (PropertyEnum.EntTypeEnum.ET_1.getKey().equals(ent.getEntType())){
					row = CollUtil.newArrayList(i + "",
							StrUtil.isNotBlank(data.getName())?data.getName():"--",
							StrUtil.isNotBlank(data.getContactsPhone())?data.getContactsPhone():"--",
							StrUtil.isNotBlank(data.getAddress())?data.getAddress():"--",
							StrUtil.isNotBlank(data.getDetail())?data.getDetail():"--",
							StrUtil.isNotBlank(data.getBusinessType())?(Objects.requireNonNull(PropertyEnum.BusinessTypeEnum.getValueByKey(data.getBusinessType())).getValue()):"--",
							StrUtil.isNotBlank(data.getProductSortName())?data.getProductSortName():"--",
							StrUtil.isNotBlank(data.getMainType())?(Objects.requireNonNull(MainTypeEnum.PersonEnum.getPersonEnumByKey(data.getMainType())).getValue()):"--",
							StrUtil.isNotBlank(String.valueOf(data.getCreateTime()))? DateUtil.format(data.getCreateTime(),"yyyy-MM-dd"):"--",
							StrUtil.isNotBlank(String.valueOf(data.getCertificateAmount()))?String.valueOf(data.getCertificateAmount()):"--",
							StrUtil.isNotBlank(String.valueOf(data.getCertificatePrintAmount()))?(String.valueOf(data.getCertificatePrintAmount())):"--",
							null != data.getCertificatePrintDate()?(DateUtil.format(data.getCertificatePrintDate(),"yyyy-MM-dd")):"--");
					rows.add(row);
				}else if (PropertyEnum.EntTypeEnum.ET_0.getKey().equals(ent.getEntType())){
					row = CollUtil.newArrayList(i + "",
							StrUtil.isNotBlank(data.getName())?data.getName():"--",
							StrUtil.isNotBlank(data.getLegalPerson())?data.getLegalPerson():"--",
							StrUtil.isNotBlank(data.getSocialCode())?data.getSocialCode():"--",
							StrUtil.isNotBlank(data.getContacts())?data.getContacts():"--",
							StrUtil.isNotBlank(data.getContactsPhone())?data.getContactsPhone():"--",
							StrUtil.isNotBlank(data.getAddress())?data.getAddress():"--",
							StrUtil.isNotBlank(data.getDetail())?data.getDetail():"--",
							StrUtil.isNotBlank(data.getBusinessType())?(Objects.requireNonNull(PropertyEnum.BusinessTypeEnum.getValueByKey(data.getBusinessType())).getValue()):"--",
							StrUtil.isNotBlank(data.getProductSortName())?data.getProductSortName():"--",
							StrUtil.isNotBlank(data.getMainType())?(Objects.requireNonNull(MainTypeEnum.EntEnum.getEntEnumByKey(data.getMainType())).getValue()):"--",
							StrUtil.isNotBlank(String.valueOf(data.getCreateTime()))?DateUtil.format(data.getCreateTime(),"yyyy-MM-dd"):"--",
							StrUtil.isNotBlank(String.valueOf(data.getCertificateAmount()))?data.getCertificateAmount():"--",
							StrUtil.isNotBlank(String.valueOf(data.getCertificatePrintAmount()))?data.getCertificatePrintAmount():"--",
							null != data.getCertificatePrintDate()?DateUtil.format(data.getCertificatePrintDate(),"yyyy-MM-dd"):"--");
					rows.add(row);
				} else{
					row = CollUtil.newArrayList(i + "",
							StrUtil.isNotBlank(data.getName())?data.getName():"--",
							StrUtil.isNotBlank(data.getLegalPerson())?data.getLegalPerson():"--",
							StrUtil.isNotBlank(data.getSocialCode())?data.getSocialCode():"--",
							StrUtil.isNotBlank(data.getContacts())?data.getContacts():"--",
							StrUtil.isNotBlank(data.getContactsPhone())?data.getContactsPhone():"--",
							StrUtil.isNotBlank(data.getAddress())?data.getAddress():"--",
							StrUtil.isNotBlank(data.getDetail())?data.getDetail():"--",
							StrUtil.isNotBlank(data.getBusinessType())?"机构":"--",
							StrUtil.isNotBlank(data.getMainType())?Objects.requireNonNull(MainTypeEnum.CompanyEnum.getCompanyEnumByKey(data.getMainType())).getValue():"--",
							StrUtil.isNotBlank(String.valueOf(data.getCreateTime()))?DateUtil.format(data.getCreateTime(),"yyyy-MM-dd"):"--",
							StrUtil.isNotBlank(String.valueOf(data.getInspectionWriteAmount()))?data.getInspectionWriteAmount():"--");
					rows.add(row);
				}
				i++;
			}
		}
		String fileName = (PropertyEnum.EntTypeEnum.ET_1.getKey().equals(ent.getEntType()) ? "个人主体名录_" : ((PropertyEnum.EntTypeEnum.ET_0.getKey().equals(ent.getEntType()) ? "企业主体名录_":"机构主体名录_"))) + DateUtils.dateTime() + ".xlsx";
		ServletOutputStream out = response.getOutputStream();
		ExcelWriter writer = ExcelUtil.getWriter(true);
		if(PropertyEnum.EntTypeEnum.ET_1.getKey().equals(ent.getEntType())) {
			writer.getSheet().setColumnWidth(0,2112);		//序号
			writer.getSheet().setColumnWidth(1,4641);		//主体名称
			writer.getSheet().setColumnWidth(2,4257);		//联系电话
			writer.getSheet().setColumnWidth(3,7777);		//所在区域
			writer.getSheet().setColumnWidth(4,9953);		//详细地址
			writer.getSheet().setColumnWidth(5,3809);		//主体类型
			writer.getSheet().setColumnWidth(6,6113);		//产品分类
			writer.getSheet().setColumnWidth(7,3520);		//主体类别
			writer.getSheet().setColumnWidth(8,3392);		//注册日期
			writer.getSheet().setColumnWidth(9,2786);		//开证次数
			writer.getSheet().setColumnWidth(10,2786);		//开证数量
			writer.getSheet().setColumnWidth(11,4130);		//最后一次开证日期
		}else if (PropertyEnum.EntTypeEnum.ET_0.getKey().equals(ent.getEntType())){
			writer.getSheet().setColumnWidth(0, 2112);		//序号
			writer.getSheet().setColumnWidth(1, 7808);	//企业名称
			writer.getSheet().setColumnWidth(2, 2112);		//企业法人
			writer.getSheet().setColumnWidth(3, 5440);		//统一社会信用代码
			writer.getSheet().setColumnWidth(4, 2914);		//企业联系人
			writer.getSheet().setColumnWidth(5, 3392);		//联系电话
			writer.getSheet().setColumnWidth(6, 4961);	//所在区域
			writer.getSheet().setColumnWidth(7, 6049);	//详细地址
			writer.getSheet().setColumnWidth(8, 2432);		//主体类型
			writer.getSheet().setColumnWidth(9, 4001);		//产品分类
			writer.getSheet().setColumnWidth(10, 3520);	//主体类别
			writer.getSheet().setColumnWidth(11, 2944);	//注册日期
			writer.getSheet().setColumnWidth(12, 2786);	//开证次数
			writer.getSheet().setColumnWidth(13, 2786);	//开证数量
			writer.getSheet().setColumnWidth(14, 4130);	//最后一次开证日期
		}else {
			writer.getSheet().setColumnWidth(0, 2112);		//序号
			writer.getSheet().setColumnWidth(1, 7808);	//企业名称
			writer.getSheet().setColumnWidth(2, 2112);		//企业法人
			writer.getSheet().setColumnWidth(3, 5440);		//统一社会信用代码
			writer.getSheet().setColumnWidth(4, 2914);		//企业联系人
			writer.getSheet().setColumnWidth(5, 3392);		//联系电话
			writer.getSheet().setColumnWidth(6, 4961);	//所在区域
			writer.getSheet().setColumnWidth(7, 6049);	//详细地址
			writer.getSheet().setColumnWidth(8, 2432);		//主体类型
			writer.getSheet().setColumnWidth(9, 3520);	//主体类别
			writer.getSheet().setColumnWidth(10, 2944);	//注册日期
			writer.getSheet().setColumnWidth(11, 4500);	//开证次数
		}
		for (int i = 0;i<rows.size();i++){
			writer.getSheet().setDefaultRowHeight((short)600);
		}
		writer.write(rows, true);
		for(int i=0;i< (PropertyEnum.EntTypeEnum.ET_1.getKey().equals(ent.getEntType())?12:((PropertyEnum.EntTypeEnum.ET_0.getKey().equals(ent.getEntType()) ? 15:12)));i++){
			setCellStyle(writer,i,PropertyEnum.EntTypeEnum.ET_1.getKey().equals(ent.getEntType()));
		}
		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
		response.setHeader("Content-Disposition","attachment;filename=" + Encodes.urlEncode(fileName));
		writer.flush(out, true);
		writer.close();
		IoUtil.close(out);
	}


	/**
	 * 导出设置表格头部颜色
	 * @Title: setCellStyle
	 * @Author: HeXingChen
	 * @Date: 2022年06月08日 16:15
	 * @param writer
	 * @param x
	 * @param isTrue
	 * @return: void
	 */
	private void setCellStyle(ExcelWriter writer, int x, Boolean isTrue) {
		CellStyle cellStyle = writer.createCellStyle(x, 0);
		// 顶边栏
		cellStyle.setBorderTop(BorderStyle.THIN);
		cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
		// 右边栏
		cellStyle.setBorderRight(BorderStyle.THIN);
		cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
		// 底边栏
		cellStyle.setBorderBottom(BorderStyle.THIN);
		cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		// 左边栏
		cellStyle.setBorderLeft(BorderStyle.THIN);
		cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		// 居中
		cellStyle.setAlignment(HorizontalAlignment.CENTER);
		cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		// 字体
		Font font = writer.createFont();
		font.setBold(true);
		cellStyle.setFont(font);
		// 填充前景色(两个一起使用)
		cellStyle.setFillForegroundColor(isTrue ? IndexedColors.TAN.getIndex() : IndexedColors.PALE_BLUE.getIndex());
		cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
	}

	/**
	 *
	 * @Title: findAreaCertificateDataListPage
	 * @author: lxy
	 * @date: 2022年7月14日18:09:44
	 * @Description: 行政区划合格证开证主体查询数据列表
	 * @param:  page
	 * @param:  ent
	 * @return: Page<Ent>
	 * @throws
	 */
	@Override
	public  List<Ent> findAreaCertificateDataListPage( Ent ent) {
		// 拼接 机构与地区编码绑定查询
		if(!Constants.BUSINESS_SYSTEM_FLAG.equals(SecurityUtils.getLoginUser().getUsername())){
			ent.setLoginAreaCode(SecurityUtils.getLoginUser().getUser().getDept().getAreaCode());
		}
		List<Ent> list = entMapper.findAreaCertificateDataList(ent);
		return list;
	}
	@Override
	public void export(HttpServletResponse response, Ent ent) {
		ent.setExamineStatus(EnumProperty.ExamineStatusEnum.PASS.getKey());
		List<Ent> list = findListQuick(ent);

		if(EnumProperty.EntTypeEnum.ENTERPRISE.getKey().equals(ent.getEntType())){
			List<EntEnterpriseVo> entEnterpriseVoList=new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				EntEnterpriseVo ee=new EntEnterpriseVo();
				BeanUtils.copyProperties(list.get(i),ee);
				ee.setExcelNo(i+1);
				ee.setProductSortName(StringUtils.isEmpty(ee.getProductSortName())?"--":ee.getProductSortName());
				entEnterpriseVoList.add(ee);
			}
			com.jkr.common.utils.poi.ExcelUtil<EntEnterpriseVo> util = new com.jkr.common.utils.poi.ExcelUtil<EntEnterpriseVo>(EntEnterpriseVo. class);
			util.exportExcel(response, entEnterpriseVoList, "主体信息数据");
		} else if (EnumProperty.EntTypeEnum.PERSON.getKey().equals(ent.getEntType())){
			List<EntPersonVo> entPersonVoList=new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				EntPersonVo ep=new EntPersonVo();
				BeanUtils.copyProperties(list.get(i),ep);
				ep.setExcelNo(i+1);
				ep.setProductSortName(StringUtils.isEmpty(ep.getProductSortName())?"--":ep.getProductSortName());
				entPersonVoList.add(ep);
			}
			com.jkr.common.utils.poi.ExcelUtil<EntPersonVo> util = new com.jkr.common.utils.poi.ExcelUtil<EntPersonVo>(EntPersonVo. class);
			util.exportExcel(response, entPersonVoList, "主体信息数据");
		}
	}

	@Override
	public List<Ent> getSyncData(Date date) {
		if (ObjectUtil.isNull(date)) {
			return List.of();
		}
		return  entMapper.getSyncData(date);
	}
}
