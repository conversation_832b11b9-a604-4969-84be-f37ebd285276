package com.jkr.project.argi.domain;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import java.util.List;

/**
 * 主体信息子表对象 bas_ent_detail
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
        @Data
        @EqualsAndHashCode(callSuper = true)
        @TableName("bas_ent_detail")
		public class EntDetail extends BaseModel
		{
		private static final long serialVersionUID = 1L;

				/** 主体id */
				@Excel(name = "主体id")
		private String entId;

				/** 产品ID */
				@Excel(name = "产品ID")
		private String productId;

				/** 产品name */
				@Excel(name = "产品name")
		private String productName;

				/** 种养殖规模 */
				@Excel(name = "种养殖规模")
		private BigDecimal scale;

				/** 种养殖规模单位type */
				@Excel(name = "种养殖规模单位type")
		private String breedingUnitCode;

				/** 种养殖规模单位name */
				@Excel(name = "种养殖规模单位name")
		private String breedingUnitLabel;

				/** 年产量单位code值 */
				@Excel(name = "年产量单位code值")
		private String outputUnitCode;

				/** 年产量label值 */
				@Excel(name = "年产量label值")
		private String outputUnitLabel;

				/** 年产值 */
				@Excel(name = "年产值")
		private BigDecimal annualValue;

				/** 年产量 */
				@Excel(name = "年产量")
		private BigDecimal annualOutput;

            /** 主键集合 */
            private List<Long> ids;
}
