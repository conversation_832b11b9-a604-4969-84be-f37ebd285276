package com.jkr.project.argi.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Ent;
import org.apache.ibatis.annotations.Param;

/**
 * 主体信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface EntMapper extends BaseMapper<Ent>{
	/**
	 * 查询主体信息
	 *
	 * @param id 主体信息主键
	 * @return 主体信息
	 */
	public Ent selectEntById(Long id);

	/**
	 * 查询主体信息列表
	 *
	 * @param ent 主体信息
	 * @return 主体信息集合
	 */
	public List<Ent> selectEntList(Ent ent);
	public List<Ent> findListQuick(Ent ent);

	/**
	 * 新增主体信息
	 *
	 * @param ent 主体信息
	 * @return 结果
	 */
	public int insertEnt(Ent ent);

	/**
	 * 修改主体信息
	 *
	 * @param ent 主体信息
	 * @return 结果
	 */
	public int updateEnt(Ent ent);

	/**
	 * 删除主体信息
	 *
	 * @param id 主体信息主键
	 * @return 结果
	 */
	public int deleteEntById(Long id);

	/**
	 * 批量删除主体信息
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteEntByIds(Long[] ids);

	/**
	 * 批量逻辑删除主体信息
	 *
	 * @param  ids 主体信息主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过主体信息主键id逻辑删除信息
	 *
	 * @param  id 主体信息主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	/**
	 *
	 * @Title: getBySocialCodeOrCardNo
	 * @author: LJX
	 * @date: 2020年7月19日 上午8:48:29
	 * @Description:根据统一社会信用代码/身份证号获取机构数
	 * @param:  ent
	 * @throws
	 */
	int getBySocialCodeOrCardNo(Ent ent);

	/**
	 * 根据县区code统计企业基本情况
	 * <AUTHOR>
	 * @date 2025/5/22 19:27
	 * @param ent
	 * @since 1.0.0
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 */
	Map<String, Object> getEntStatistic(Ent ent);

	/**
	 *
	 * @title: getByTableId
	 * @description: 根据tableId获取ent对象
	 * @param: @param tableId
	 * @param: @return
	 * @return: Ent
	 */
	public Ent getByTableId(@Param("tableId") String tableId);
	/**
	 *
	 * @Title: examineSave
	 * @Description: 审批保存
	 * @param:  ent
	 * @return: void
	 * @throws
	 */
	void examineSave(Ent ent);
	/**
	 *
	 * @title: updateCertificateAmount
	 * @author: lxy
	 * @date: 2021年1月13日 下午4:24:34
	 * @description: 更新主体开具次数、开具数量信息
	 * @param: id
	 * @param: amount
	 * @return: int
	 */
	public int updateCertificateAmount(@Param("id") String id,@Param("amount") Integer amount);

	/**
	 * 更新主体开具数量信息（重复打印 不更新开具次数）
	 * @param id
	 * @param amount
	 * @return
	 */
	public int updateCertificatePrintAmount(@Param("id") String id,@Param("amount") Integer amount);
	/**
	 * 更新上传检测报告次数
	 * @param id
	 * @return
	 */
	public int updateInspectionWriteAmount(@Param("id") String id);/**
	 *
	 * @title: updateChangeView
	 * @description: 主体变更业务查看状态更新
	 * @param: @param ent
	 * @param: @return
	 * @return: int
	 */
	public int updateChangeView(Ent ent);
	/**
	 *
	 * @title: updateInvalidCertificateAmount
	 * @author: lxy
	 * @date: 2021年1月13日 下午4:24:34
	 * @description: 合格证作废，更新主体开具次数、开具数量信息
	 * @param: id
	 * @return: int
	 */
	public int updateInvalidCertificateAmount(@Param("id") String id);
	/**
	 *
	 * @title: updateFrozen
	 * @author: lxy
	 * @date: 2021年3月6日15:55:16
	 * @description: 主体冻结状态更新
	 * @param: ent
	 * @return: int
	 */
	public int updateFrozen(Ent ent);
	/**
	 *
	 * @title: updateTableId
	 * @author: lxy
	 * @date: 2021年3月6日15:55:16
	 * @description: 主体更新tableId
	 * @param: ent
	 * @return: int
	 */
	public int updateTableId(Ent ent);
	/**
	 *
	 * @title: validateOnly
	 * @author: wanghe
	 * @date: 2021年9月10日 下午4:40:11
	 * @param:  entity
	 * @description: 手机号唯一
	 * @return: ResponseResult
	 * @throws
	 */
	Integer validateOnly(Ent entity);
	/**
	 * 校验身份证号唯一
	 *
	 * <AUTHOR>
	 * @date 2021年09月15日 18:27:42
	 * @param entity
	 * @return java.lang.Integer
	 */
	Integer validateIdCardOnly(Ent entity);

	/**
	 * 根据身份证号查询主体信息
	 *
	 * <AUTHOR>
	 * @date 2021年09月15日 18:51:29
	 * @param idCard
	 * @return com.thinkgem.jeesite.modules.bas.entity.Ent
	 */
	Ent getEntByIdCard(String idCard);
	/**
	 * 根据tableId查询主体信息
	 *
	 * <AUTHOR>
	 * @date 2021年09月27日 10:52:40
	 * @param tableId
	 * @return com.thinkgem.jeesite.modules.bas.entity.Ent
	 */
	Ent getEntByTableId(String tableId);
//	/**
//	 * 行政区划执法检查主体查询数据列表
//	 * 2022年7月14日18:05:30
//	 * lxy
//	 * @param entity
//	 * @return List<Ent>
//	 */
//	public List<Ent> findAreaCheckDataList(Ent entity);
	/**
	 * 行政区划合格证开证主体查询数据列表
	 * 2022年7月14日18:05:30
	 * lxy
	 * @param entity
	 * @return List<Ent>
	 */
	public List<Ent> findAreaCertificateDataList(Ent entity);


    List<Ent> getSyncData(@Param("date") Date date);
}
