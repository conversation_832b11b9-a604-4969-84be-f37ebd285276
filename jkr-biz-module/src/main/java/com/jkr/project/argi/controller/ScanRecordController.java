package com.jkr.project.argi.controller;

import java.text.SimpleDateFormat;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.jkr.common.utils.PageUtils;
import com.jkr.project.argi.domain.Certificate;
import com.jkr.project.argi.domain.CertificateNo;
import com.jkr.project.argi.service.ICertificateNoService;
import com.jkr.project.argi.service.ICertificateService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.ScanRecord;
import com.jkr.project.argi.service.IScanRecordService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 扫描日志Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/scanRecord")
public class ScanRecordController extends BaseController {

    @Autowired
    private IScanRecordService scanRecordService;
    @Autowired
    private ICertificateService certificateService;
    @Autowired
    private ICertificateNoService certificateNoService;

    /**
     * 查询扫描日志列表
     */
    @PreAuthorize("@ss.hasPermi('argi:scanRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScanRecord scanRecord) {
        startPage();
        List<ScanRecord> list = scanRecordService.selectScanRecordList(scanRecord);
        return getDataTable(list);
    }

    /**
     * 导出扫描日志列表
     */
    @PreAuthorize("@ss.hasPermi('argi:scanRecord:export')")
    @Log(title = "导出扫描日志列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanRecord scanRecord) {
        List<ScanRecord> list = scanRecordService.selectScanRecordList(scanRecord);
        ExcelUtil<ScanRecord> util = new ExcelUtil<ScanRecord>(ScanRecord.class);
        util.exportExcel(response, list, "扫描日志数据");
    }

    /**
     * 获取扫描日志详细信息
     */
    //@PreAuthorize("@ss.hasPermi('argi:scanRecord:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(scanRecordService.selectScanRecordById(id));
    }

    @PostMapping("/findScanRecordPage")
    public TableDataInfo findScanRecordPage(@RequestBody ScanRecord scanRecord){
        PageUtils.startPage(scanRecord.getPageNum(), scanRecord.getPageSize());
        List<ScanRecord> list = scanRecordService.findEntScanPage(scanRecord);
        return getDataTable(list);
    }

    @PostMapping("/save")
    public AjaxResult save(@RequestBody ScanRecord scanRecord){
        try {
            CertificateNo certificateNo = certificateNoService.selectCertificateNoById(Long.parseLong(scanRecord.getCertificateId()));
            if (certificateNo == null) {
                return AjaxResult.error("检测到非本系统开具的合格证");
            } else {
                Certificate certificate = certificateService.selectCertificateById(Long.parseLong(certificateNo.getCertificateId()));
                if (certificate == null) {
                    return AjaxResult.error("检测到非本系统开具的合格证");
                }
                if (!StringUtils.equals("0", certificate.getDelFlag())) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return AjaxResult.error(StrUtil.format("该合格证已于{}被标记为失效", sdf.format(certificate.getUpdateTime())));
                }
            }
            return AjaxResult.success(scanRecordService.saveScanRecord(certificateNo.getCertificateId(), certificateNo.getFullNumber(), scanRecord.getEntId()));
        } catch(Exception e) {
            return AjaxResult.error("发现未知错误，请再次查验或联系开证方");
        }
    }

    /**
     * 新增扫描日志
     */
    @PreAuthorize("@ss.hasPermi('argi:scanRecord:add')")
    @Log(title = "新增扫描日志", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody ScanRecord scanRecord) {
        return toAjax(scanRecordService.insertScanRecord(scanRecord));
    }

    /**
     * 修改扫描日志
     */
    @PreAuthorize("@ss.hasPermi('argi:scanRecord:edit')")
    @Log(title = "修改扫描日志", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody ScanRecord scanRecord) {
        return toAjax(scanRecordService.updateScanRecord(scanRecord));
    }

    /**
     * 删除扫描日志
     */
    @PreAuthorize("@ss.hasPermi('argi:scanRecord:remove')")
    @Log(title = "删除扫描日志", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(scanRecordService.deleteScanRecordById(id));
    }

    /**
     * 批量删除扫描日志
     */
    @PreAuthorize("@ss.hasPermi('argi:scanRecord:batchRemove')")
    @Log(title = "批量删除扫描日志", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody ScanRecord scanRecord) {
        return toAjax(scanRecordService.deleteScanRecordByIds(scanRecord.getIds()));
    }
}
