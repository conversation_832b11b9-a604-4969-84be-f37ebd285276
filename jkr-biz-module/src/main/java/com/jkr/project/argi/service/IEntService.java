package com.jkr.project.argi.service;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jkr.common.enums.EnumSubject;
import com.jkr.project.argi.domain .Ent;
import com.jkr.project.argi.domain.EntChange;
import com.jkr.project.system.domain.SysDept;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主体信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IEntService extends IService<Ent> {
	/**
	 * 查询主体信息
	 *
	 * @param id 主体信息主键
	 * @return 主体信息
	 */
	public Ent selectEntById(Long id);

	/**
	 * 查询主体信息列表
	 *
	 * @param ent 主体信息
	 * @return 主体信息集合
	 */
	public List<Ent> selectEntList(Ent ent);
	public List<Ent> findListQuick(Ent ent);

	boolean checkSocialCode(Ent ent);

	/**
	 * 新增主体信息
	 *
	 * @param ent 主体信息
	 * @return 结果
	 */
	public int insertEnt(Ent ent);

	/**
	 * 修改主体信息
	 *
	 * @param ent 主体信息
	 * @return 结果
	 */
	public int updateEnt(Ent ent);

	/**
	 * 批量删除主体信息
	 *
	 * @param ids 需要删除的主体信息主键集合
	 * @return 结果
	 */
	public int deleteEntByIds(List<Long> ids);

	/**
	 * 删除主体信息信息
	 *
	 * @param id 主体信息主键
	 * @return 结果
	 */
	public int deleteEntById(Long id);

	/**
	 * 检验统一社会信用代码唯一性
	 * <AUTHOR>
	 * @date 2025/5/22 10:56
	 * @param socialCode
	 * @since 1.0.0
	 * @return java.lang.Boolean
	 */
	Boolean verificationSocialCode(String socialCode);

	String saveEnt(Ent ent) ;

	boolean checkIdCard(Ent ent);

	Ent getByTableId(String tableId);

	void examineSave(Ent ent);

	List<EnumSubject> findMainTypeList(String entType);

	int updateCertificateAmount(String id, Integer amount);

	int updateCertificatePrintAmount(String id, Integer amount);

	int updateInspectionWriteAmount(String id);

	void updateExamineStatusChangeTemp(String entId);

	void clearCache(String tableId);

    void updateEntChange(EntChange entChange);

	int updateChangeView();

	void updateInvalidCertificateAmount(String id);

	int frozen(Ent ent);

	int cancelFrozen(Ent ent);

	int updateTableId(String id, String tableId);

	Integer validateOnly(Ent entity);

	Integer validateIdCard(Ent entity);

	Ent getEntByIdCard(String idCard);

	Ent getEntByTableId(String tableId);

	String phoneCheck(String phoneNumber, String userId);

	void exportExcel(Ent ent, HttpServletResponse response) throws IOException;

	List<Ent> findAreaCertificateDataListPage(Ent ent);

	void export(HttpServletResponse response, Ent ent);

	List<Ent> getSyncData(Date date);
}
