package com.jkr.project.argi.controller;

import java.util.List;

import com.jkr.common.enums.CertificateConfigTypeEnum;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateConfig;
import com.jkr.project.argi.service.ICertificateConfigService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 电子合格证配置Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateConfig")
public class CertificateConfigController extends BaseController {

    @Autowired
    private ICertificateConfigService certificateConfigService;

    /**
     * 查询电子合格证配置列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertificateConfig certificateConfig) {
        startPage();
        List<CertificateConfig> list = certificateConfigService.selectCertificateConfigList(certificateConfig);
        return getDataTable(list);
    }

    /**
     * 导出电子合格证配置列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:export')")
    @Log(title = "导出电子合格证配置列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertificateConfig certificateConfig) {
        List<CertificateConfig> list = certificateConfigService.selectCertificateConfigList(certificateConfig);
        ExcelUtil<CertificateConfig> util = new ExcelUtil<CertificateConfig>(CertificateConfig.class);
        util.exportExcel(response, list, "电子合格证配置数据");
    }

    /**
     * 获取电子合格证配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(certificateConfigService.selectCertificateConfigById(id));
    }

    /**
     * 新增电子合格证配置
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:add')")
    @Log(title = "新增电子合格证配置", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody CertificateConfig certificateConfig) {
        return toAjax(certificateConfigService.insertCertificateConfig(certificateConfig));
    }

    /**
     * 修改电子合格证配置
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:edit')")
    @Log(title = "修改电子合格证配置", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody CertificateConfig certificateConfig) {
        return toAjax(certificateConfigService.updateCertificateConfig(certificateConfig));
    }

    /**
     * 删除电子合格证配置
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:remove')")
    @Log(title = "删除电子合格证配置", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(certificateConfigService.deleteCertificateConfigById(id));
    }

    /**
     * 批量删除电子合格证配置
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfig:batchRemove')")
    @Log(title = "批量删除电子合格证配置", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody CertificateConfig certificateConfig) {
        return toAjax(certificateConfigService.deleteCertificateConfigByIds(certificateConfig.getIds()));
    }

    /**
     *
     * @title: check
     * @author: lxy
     * @date: 2021年1月6日 下午5:05:06
     * @description: 合格证配置校验
     * @param: certificateConfig
     * @return: ResponseResult
     */
    @PostMapping("/check")
    public AjaxResult check(@RequestBody CertificateConfig certificateConfig){
        List<CertificateConfig> list=certificateConfigService.selectCertificateConfigList(certificateConfig);
        if(!list.isEmpty()) {
            String msg="";
            if(CertificateConfigTypeEnum.CODE_0.getKey().equals(certificateConfig.getType())) {
                msg="通用区域配置已存在";
            }else {
                msg="该指定区域配置已存在";
            }
            return AjaxResult.error(msg);
        }
        return AjaxResult.success();
    }

    /**
     *
     * @title: getConfig
     * @author: lxy
     * @date: 2021年1月7日10:42:25
     * @description: 获取配置信息
     * @param: certificateConfig
     * @return: ResponseResult
     */
    @PostMapping("/getConfig")
    public AjaxResult getConfig(@RequestBody CertificateConfig certificateConfig){
        try {
            return AjaxResult.success(certificateConfigService.getCertificateConfig(certificateConfig));
        } catch(Exception e) {
            return AjaxResult.error("获取电子合格证配置错误，错误如下：" + e.getMessage());
        }
    }

    /**
    * @title: getTreeData
    * @author: fengbingqi
    * @date: 2025/7/17 14:12
    * @description:
    * @param: []
    * @return: com.jkr.framework.web.domain.AjaxResult
    */
    @GetMapping("/getTreeData")
    public AjaxResult getTreeData(){
        try {
            return AjaxResult.success(certificateConfigService.findDeviceTemplateTreeList());
        } catch(Exception e) {
            return AjaxResult.error("获取合格证树形结构配置，错误如下：" + e.getMessage());
        }
    }
}
