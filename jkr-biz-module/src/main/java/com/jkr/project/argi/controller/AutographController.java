package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Autograph;
import com.jkr.project.argi.service.IAutographService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 签名Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/autograph")
public class AutographController extends BaseController {
	@Autowired
	private IAutographService autographService;

/**
 * 查询签名列表
 */
@PreAuthorize("@ss.hasPermi('argi:autograph:list')")
@GetMapping("/list")
	public TableDataInfo list(Autograph autograph) {
		startPage();
		List<Autograph> list = autographService.selectAutographList(autograph);
		return getDataTable(list);
	}

	/**
	 * 导出签名列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:autograph:export')")
	@Log(title = "导出签名列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, Autograph autograph) {
		List<Autograph> list = autographService.selectAutographList(autograph);
		ExcelUtil<Autograph> util = new ExcelUtil<Autograph>(Autograph. class);
		util.exportExcel(response, list, "签名数据");
	}

	/**
	 * 获取签名详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:autograph:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(autographService.selectAutographById(id));
	}

	/**
	 * 新增签名
	 */
	@PreAuthorize("@ss.hasPermi('argi:autograph:add')")
	@Log(title = "新增签名", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody Autograph autograph) {
		return toAjax(autographService.insertAutograph(autograph));
	}

	/**
	 * 修改签名
	 */
	@PreAuthorize("@ss.hasPermi('argi:autograph:edit')")
	@Log(title = "修改签名", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody Autograph autograph) {
		return toAjax(autographService.updateAutograph(autograph));
	}

	/**
	 * 删除签名
	 */
	@PreAuthorize("@ss.hasPermi('argi:autograph:remove')")
	@Log(title = "删除签名", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(autographService.deleteAutographById(id));
	}

	/**
	 * 批量删除签名
	 */
	@PreAuthorize("@ss.hasPermi('argi:autograph:batchRemove')")
	@Log(title = "批量删除签名", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody Autograph autograph) {
		return toAjax(autographService.deleteAutographByIds(autograph.getIds()));
	}
}
