package com.jkr.project.argi.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import com.jkr.project.argi.domain.CertificateTemplate;
import com.jkr.project.argi.domain.DeviceParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateConfigTemplateMapper;
import com.jkr.project.argi.domain.CertificateConfigTemplate;
import com.jkr.project.argi.service.ICertificateConfigTemplateService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 电子合格证配置-模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateConfigTemplateServiceImpl implements ICertificateConfigTemplateService {
	@Autowired
	private CertificateConfigTemplateMapper certificateConfigTemplateMapper;

	/**
	 * 查询电子合格证配置-模板
	 *
	 * @param id 电子合格证配置-模板主键
	 * @return 电子合格证配置-模板
	 */
	@Override
	public CertificateConfigTemplate selectCertificateConfigTemplateById(Long id) {
		return certificateConfigTemplateMapper.selectCertificateConfigTemplateById(id);
	}

	/**
	 * 查询电子合格证配置-模板列表
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 电子合格证配置-模板
	 */
	@Override
	public List<CertificateConfigTemplate> selectCertificateConfigTemplateList(CertificateConfigTemplate certificateConfigTemplate) {
		return certificateConfigTemplateMapper.selectCertificateConfigTemplateList(certificateConfigTemplate);
	}

	/**
	 * 新增电子合格证配置-模板
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateConfigTemplate(CertificateConfigTemplate certificateConfigTemplate) {
		certificateConfigTemplate.insertInit(SecurityUtils.getLoginUser().getUsername());
		return certificateConfigTemplateMapper.insertCertificateConfigTemplate(certificateConfigTemplate);
	}

	/**
	 * 修改电子合格证配置-模板
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateConfigTemplate(CertificateConfigTemplate certificateConfigTemplate) {
		certificateConfigTemplate.updateInit(SecurityUtils.getLoginUser().getUsername());
		return certificateConfigTemplateMapper.updateCertificateConfigTemplate(certificateConfigTemplate);
	}

	/**
	 * 批量删除电子合格证配置-模板
	 *
	 * @param ids 需要删除的电子合格证配置-模板主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateConfigTemplateByIds(List<Long> ids) {
		return certificateConfigTemplateMapper.logicRemoveByIds(ids);
		//return certificateConfigTemplateMapper.deleteCertificateConfigTemplateByIds(ids);
	}

	/**
	 * 删除电子合格证配置-模板信息
	 *
	 * @param id 电子合格证配置-模板主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateConfigTemplateById(Long id) {
		return certificateConfigTemplateMapper.logicRemoveById(id);
		//return certificateConfigTemplateMapper.deleteCertificateConfigTemplateById(id);
	}

	/**
	 *
	 * @title: saveBatch
	 * @author: lxy
	 * @date: 2021年1月6日 下午2:14:49
	 * @description: 批量保存
	 * @param: certificateConfigId
	 * @param: treeIds
	 * @return: void
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveBatch(String certificateConfigId, String[] treeIds) {
		if (StringUtils.isNotBlank(certificateConfigId) && treeIds.length > 0) {
			List<CertificateConfigTemplate> list = new ArrayList<CertificateConfigTemplate>();
			for (String ids : treeIds) {
				CertificateConfigTemplate certificateConfigTemplate = new CertificateConfigTemplate();
				certificateConfigTemplate.setCertificateConfigId(certificateConfigId);
				String[] id = ids.split("-");
				certificateConfigTemplate.setDeviceId(id[0]);
				certificateConfigTemplate.setTemplateId(id[1]);
				certificateConfigTemplate.insertInit(SecurityUtils.getLoginUser().getUsername());
				list.add(certificateConfigTemplate);
			}
			// 批量保存
			certificateConfigTemplateMapper.insertBatch(list);
		}
	}

	/**
	 *
	 * @title: getDeviceTemplateIds
	 * @author: lxy
	 * @date: 2021年1月6日 下午2:49:13
	 * @description: 拼装获取设备、合格证模板ids
	 * @param: @param
	 *             certificateConfigId
	 * @param: @return
	 * @return: String
	 */
	@Override
	public String getDeviceTemplateIds(String certificateConfigId) {
		if (StringUtils.isBlank(certificateConfigId)) {
			return null;
		}
		CertificateConfigTemplate certificateConfigTemplateParam = new CertificateConfigTemplate();
		certificateConfigTemplateParam.setCertificateConfigId(certificateConfigId);
		List<CertificateConfigTemplate> list = certificateConfigTemplateMapper.selectCertificateConfigTemplateList(certificateConfigTemplateParam);
		List<String> idsList = new ArrayList<String>();
		Map<String, Object> deviceIdMap = new HashMap<String, Object>();
		for (CertificateConfigTemplate certificateConfigTemplate : list) {
			// 为了ztree默认回显处理
			/*if (!deviceIdMap.containsKey(certificateConfigTemplate.getDeviceId())) {
				deviceIdMap.put(certificateConfigTemplate.getDeviceId(), certificateConfigTemplate.getDeviceId());
				idsList.add(certificateConfigTemplate.getDeviceId() + "-" + certificateConfigTemplate.getDeviceId());
			}*/
			idsList.add(certificateConfigTemplate.getDeviceId() + "-" + certificateConfigTemplate.getTemplateId());
		}
		return idsList.stream().collect(Collectors.joining(","));
	}

	/**
	 *
	 * @title: deleteByCertificateConfigId
	 * @author: lxy
	 * @date: 2021年1月6日 下午3:07:37
	 * @description: 删除
	 * @param: certificateConfigId
	 * @return: int
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteByCertificateConfigId(String certificateConfigId) {
		CertificateConfigTemplate certificateConfigTemplate = new CertificateConfigTemplate();
		certificateConfigTemplate.setCertificateConfigId(certificateConfigId);
		return certificateConfigTemplateMapper.deleteByCertificateConfigId(certificateConfigTemplate);
	}

	/**
	 *
	 * @title: findDeviceTemplateConfigList
	 * @author: lxy
	 * @date: 2021年1月7日 上午11:31:34
	 * @description: 获取打印机、模板配置信息
	 * @param: @param certificateConfigId
	 * @param: @return
	 * @return: List<Object>
	 */
	@Override
	public List<Object> findDeviceTemplateConfigList(String certificateConfigId) {
		CertificateConfigTemplate certificateConfigTemplate = new CertificateConfigTemplate();
		certificateConfigTemplate.setCertificateConfigId(certificateConfigId);
		List<CertificateConfigTemplate> list = certificateConfigTemplateMapper.selectCertificateConfigTemplateList(certificateConfigTemplate);
		Map<String, Object> deviceMap = new HashMap<String, Object>();
		Map<String, Object> templateMap = new HashMap<String, Object>();
		for (CertificateConfigTemplate config : list) {
			// 获取去重后的设备数据
			if (!deviceMap.containsKey(config.getDeviceId())) {
				deviceMap.put(config.getDeviceId(), config.getDeviceParameter());
			}
			// 按设备id对合格证模板分组处理
			List<CertificateTemplate> templateList = null;
			if (!templateMap.containsKey(config.getDeviceId())) {
				templateList = new ArrayList<CertificateTemplate>();
			} else {
				templateList = (List<CertificateTemplate>) templateMap.get(config.getDeviceId());
			}
			templateList.add(config.getCertificateTemplate());
			templateMap.put(config.getDeviceId(), templateList);
		}
		List<Object> resultList = new ArrayList<Object>();
		// 拼装设备及所配置的模板信息
		for (Map.Entry<String, Object> m : deviceMap.entrySet()) {
			if(templateMap.containsKey(m.getKey())) {
				DeviceParameter device=(DeviceParameter) m.getValue();
				device.setTemplateList((List<CertificateTemplate>) templateMap.get(m.getKey()));
			}
			resultList.add(m.getValue());
		}
		return resultList;
	}

}
