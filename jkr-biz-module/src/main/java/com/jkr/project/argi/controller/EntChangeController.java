package com.jkr.project.argi.controller;

import java.util.List;
import java.util.Map;

import com.jkr.common.utils.ValidationUtils;
import com.jkr.project.argi.service.IEntService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.EntChange;
import com.jkr.project.argi.service.IEntChangeService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 主体信息变更Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/entChange")
public class EntChangeController extends BaseController {
	@Autowired
	private IEntChangeService entChangeService;

	/**
	 * 查询主体信息变更列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:list')")
	@GetMapping("/list")
	public TableDataInfo list(EntChange entChange) {
		startPage();
		List<EntChange> list = entChangeService.selectEntChangeList(entChange);
		return getDataTable(list);
	}

	/**
	 * 导出主体信息变更列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:export')")
	@Log(title = "导出主体信息变更列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, EntChange entChange) {
		List<EntChange> list = entChangeService.selectEntChangeList(entChange);
		ExcelUtil<EntChange> util = new ExcelUtil<EntChange>(EntChange. class);
		util.exportExcel(response, list, "主体信息变更数据");
	}

	/**
	 * 获取主体信息变更详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(entChangeService.selectEntChangeById(id));
	}

	/**
	 * 新增主体信息变更
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:add')")
	@Log(title = "新增主体信息变更", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody EntChange entChange) {
		return toAjax(entChangeService.insertEntChange(entChange));
	}

	/**
	 * 修改主体信息变更
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:edit')")
	@Log(title = "修改主体信息变更", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody EntChange entChange) {
		return toAjax(entChangeService.updateEntChange(entChange));
	}

	/**
	 * 删除主体信息变更
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:remove')")
	@Log(title = "删除主体信息变更", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(entChangeService.deleteEntChangeById(id));
	}

	/**
	 * 批量删除主体信息变更
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:batchRemove')")
	@Log(title = "批量删除主体信息变更", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody EntChange entChange) {
		return toAjax(entChangeService.deleteEntChangeByIds(entChange.getIds()));
	}
	/**
	 *
	 * @title: getChange
	 * @author: 2021年2月25日19:11:51
	 * @date: 2021年2月25日19:11:54
	 * @description: 获取主体变更信息
	 * @param: entChange
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "getChange", method = RequestMethod.POST)
	public AjaxResult getChange(@RequestBody EntChange entChange) {
		try {
			return AjaxResult.success(entChangeService.getChange(entChange));
		} catch(Exception e) {
			return AjaxResult.error("获取数据失败，错误如下：" + e.getMessage());
		}
	}
	/**
	 *
	 * @title: save
	 * @author: lxy
	 * @date: 2021年2月24日16:10:30
	 * @description: 主体变更信息保存方法
	 * @param: entChange
	 * @param: request
	 * @param: response
	 * @return: ResponseResult
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:edit')")
	@RequestMapping(value = "save", method = RequestMethod.POST)
	public AjaxResult save(@RequestBody EntChange entChange){
		List<Map<String, Object>> errorList = ValidationUtils.validationResult(entChange);
		if(!errorList.isEmpty()) {
			return AjaxResult.error("校验未通过",errorList);
		}
		try {
			return AjaxResult.success(entChangeService.saveEntChange(entChange));
		} catch(Exception e) {
			return AjaxResult.error("保存失败，错误如下：" + e.getMessage());
		}
	}

	/**
	 *
	 * @Title: examineSave
	 * @author: LJX
	 * @date: 2020年7月22日 下午7:39:23
	 * @Description: 审批保存
	 * @param:  ent
	 * @param:  model
	 * @return: ResponseResult
	 * @throws
	 */
	@PreAuthorize("@ss.hasPermi('argi:entChange:edit')")
	@RequestMapping(value = "/examine/save", method = RequestMethod.POST)
	public AjaxResult examineSave(@RequestBody EntChange entChange ) {
		try {
			entChangeService.examineSave(entChange);
		} catch (Exception e) {
			return AjaxResult.error("保存失败，错误如下：" + e.getMessage());
		}
		return AjaxResult.success();
	}
}
