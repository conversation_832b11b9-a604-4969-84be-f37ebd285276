package com.jkr.project.argi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jkr.project.argi.domain.AcquireRecord;
import com.jkr.project.argi.domain.AnalysisInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 首页统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface AnalysisMapper extends BaseMapper<AnalysisInfo>{

    /**
     * 入住主体数量统计
     * @return
     */
    public int findEntCount();

    /**
     * 开证数量统计
     * @return
     */
    public int findCertificateCount();

    /**
     * 产品数量统计
     * @return
     */
    public int findProductCount();

    /**
     * 溯源统计
     * @return
     */
    public int findScanCount();

    /**
     * 主体类别统计
     * @return
     */
    public List<AnalysisInfo> findEntMainTypeCount(AnalysisInfo analysisInfo);

    /**
     * 主体性质统计
     * @return
     */
    public List<AnalysisInfo> findEntTypeCount(AnalysisInfo analysisInfo);

    /**
     * 根据主体类别统计开证数量
     * @return
     */
    public List<AnalysisInfo> findCertificateByMainType(AnalysisInfo analysisInfo);

    /**
     * 根据主体性质统计开证数量
     * @return
     */
    public List<AnalysisInfo> findCertificateByEntType(AnalysisInfo analysisInfo);

    /**
     * 根据产品类别统计开证数量
     * @return
     */
    public List<AnalysisInfo> findCertificateByProductSortCode(AnalysisInfo analysisInfo);

    /**
     * 根据产品类别统计产品数量
     * @return
     */
    public List<AnalysisInfo> findProductByProductSortCode(AnalysisInfo analysisInfo);

    /**
     * 根据产品类别统计溯源数量
     * @return
     */
    public List<AnalysisInfo> findScanByProductSortCode(AnalysisInfo analysisInfo);
}
