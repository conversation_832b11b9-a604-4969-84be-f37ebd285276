package com.jkr.project.argi.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.PropertyEnum;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.domain.ProductRecord;
import com.jkr.project.argi.domain.ProductSample;
import com.jkr.project.argi.service.IEntService;
import com.jkr.project.argi.service.IProductRecordService;
import com.jkr.project.argi.service.IProductSampleService;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ProductInspectionMapper;
import com.jkr.project.argi.domain.ProductInspection;
import com.jkr.project.argi.service.IProductInspectionService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品检测Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ProductInspectionServiceImpl implements IProductInspectionService {

	@Autowired
	private ProductInspectionMapper productInspectionMapper;
	@Autowired
	private ISysFileService fileService;
	@Autowired
	private IProductRecordService productRecordService;
	@Autowired
	private IProductSampleService productSampleService;
	@Autowired
	private IEntService entService;

	/**
	 * 查询产品检测
	 *
	 * @param id 产品检测主键
	 * @return 产品检测
	 */
	@Override
	public ProductInspection selectProductInspectionById(Long id) {
		return productInspectionMapper.selectProductInspectionById(id);
	}

	/**
	 * 查询产品检测列表
	 *
	 * @param productInspection 产品检测
	 * @return 产品检测
	 */
	@Override
	public List<ProductInspection> selectProductInspectionList(ProductInspection productInspection) {
		return productInspectionMapper.selectProductInspectionList(productInspection);
	}

	/**
	 * 新增产品检测
	 *
	 * @param productInspection 产品检测
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertProductInspection(ProductInspection productInspection) {
		productInspection.insertInit(SecurityUtils.getLoginUser().getUsername());
		return productInspectionMapper.insertProductInspection(productInspection);
	}

	/**
	 * 修改产品检测
	 *
	 * @param productInspection 产品检测
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateProductInspection(ProductInspection productInspection) {
		productInspection.updateInit(SecurityUtils.getLoginUser().getUsername());
		return productInspectionMapper.updateProductInspection(productInspection);
	}

	/**
	 * 批量删除产品检测
	 *
	 * @param ids 需要删除的产品检测主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductInspectionByIds(List<Long> ids) {
		return productInspectionMapper.logicRemoveByIds(ids);
		//return productInspectionMapper.deleteProductInspectionByIds(ids);
	}

	/**
	 * 删除产品检测信息
	 *
	 * @param id 产品检测主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductInspectionById(Long id) {
		return productInspectionMapper.logicRemoveById(id);
		//return productInspectionMapper.deleteProductInspectionById(id);
	}

	@Override
	public ProductInspection getEnter(String id) {
		ProductInspection productInspection = this.selectProductInspectionById(Long.parseLong(id));
		if (productInspection != null) {
			//获取附件
			SysFile attachmentParam = new SysFile();
			attachmentParam.setTableId(Long.parseLong(id));
			List<SysFile> attachmentList = fileService.findUrlsFileList(attachmentParam);
			productInspection.setFileList(attachmentList);
		}
		return productInspection;
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveEnter(ProductInspection productInspection) {
		productInspection.insertInit(SecurityUtils.getLoginUser().getUsername());
		productInspectionMapper.insertProductInspection(productInspection);
		// 更新主体表里的上传次数
		/*if (StrUtil.isNotBlank(productInspection.getInspectionCompanyId())) {
			entService.updateInspectionWriteAmount(productInspection.getInspectionCompanyId());
		}*/
		// 先删除原有附件
		SysFile attachment = new SysFile();
		attachment.setTableId(productInspection.getId());
		attachment.setTableName("bas_product_inspection");
		List<SysFile> fileList = fileService.findUrlsFileList(attachment);
		if (CollUtil.isNotEmpty(fileList)) {
			fileService.deleteSysFileByIds(fileList.stream().map(SysFile::getFileId).collect(Collectors.toList()).toArray(Long[]::new));
		}
		// 保存新附件
		List<SysFile> finalList = productInspection.getFileList();
		if (!finalList.isEmpty()) {
			for (SysFile att : finalList) {
				att.setTableId(productInspection.getId());
				attachment.setTableName("bas_product_inspection");
			}
			fileService.insertSysFileList(finalList);
		}
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateNewestSample(ProductInspection productInspection) {
		//获取最新检查记录
		ProductInspection newset = productInspectionMapper.getNewest(productInspection);
		//更新样品记录
		productSampleService.updateNewestSample(newset.getId()+"",productInspection.getEntId(), productInspection.getProductId(), productInspection.getInspectionSituation());
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int syncSaveQuick(List<Map<String, Object>> list) {
		try {
			if (null == list || list.isEmpty()) {
				return 0;
			}
			List<ProductInspection> batchList = new ArrayList<ProductInspection>();
			ProductInspection productInspection = null;
			for (Map<String, Object> obj : list) {
				productInspection = new ProductInspection();
				productInspection.insertInit(SecurityUtils.getLoginUser().getUsername());

				String sampleNo = String.valueOf(obj.get("sampleNo"));
				ProductRecord productRecord = productRecordService.getBySampleNo(sampleNo);
				if (null != productRecord && null != productRecord.getProduct()) {
					productInspection.setEntId(productRecord.getProduct().getEntId());
					productInspection.setProductId(productRecord.getProduct().getId()+"");
					productInspection.setProductName(productRecord.getProduct().getName());
					productInspection.setSampleQrcodeUrl(productRecord.getQueryCodeUrl());
				}
				productInspection.setSampleNo(sampleNo);
				productInspection.setInspectionSituation(PropertyEnum.InspectionSituationEnum.IS_02.getKey());
				productInspection.setRecordNo(String.valueOf(obj.get("recordNo")));
				productInspection.setInspectionItem(String.valueOf(obj.get("testItemLabel")));
				productInspection.setInspectionDate(DateUtils.parseDate(String.valueOf(obj.get("testDate")), "yyyy-MM-dd HH:mm:ss"));
				productInspection.setInspectionStandard(String.valueOf(obj.get("limitStandard")));
				productInspection.setInspectionResult(String.valueOf(obj.get("testResult")));
				productInspection.setInspectionPerson(String.valueOf(obj.get("testMan")));
				batchList.add(productInspection);
			}
			return productInspectionMapper.insertBatch(batchList);
		}catch(Exception e) {
			e.printStackTrace();
			return 0;
		}
	}

	@Override
	public ProductInspection getProductInspection(String entId, String productId, String inspectionSituation) {
		if(StringUtils.isBlank(entId) || StringUtils.isBlank(productId) || StringUtils.isBlank(inspectionSituation)) {
			return null;
		}
		ProductInspection productInspection=new ProductInspection();
		productInspection.setEntId(entId);
		productInspection.setProductId(productId);
		productInspection.setInspectionSituation(inspectionSituation);
		List<ProductInspection> list = productInspectionMapper.selectProductInspectionList(productInspection);
		if(null!=list && !list.isEmpty()) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<ProductInspection> findNewestList(String entId, String productId, String inspectionSituation) {
		//基础参数校验
		if(StringUtils.isBlank(entId) || StringUtils.isBlank(productId) || StringUtils.isBlank(inspectionSituation)) {
			return null;
		}
		//获取该产品最新的检测结果数据
		ProductSample productSample = productSampleService.getProductSample(entId,productId,inspectionSituation);
		if(null==productSample) {
			return null;
		}

		//快检业务 根据检测码获取数据集合
		if(PropertyEnum.InspectionSituationEnum.IS_02.getKey().equals(inspectionSituation)) {
			String sampleNo=productSample.getSampleNo();
			if(StringUtils.isBlank(sampleNo)) {
				return null;
			}
			//根据检测码查询检测记录
			ProductInspection productInspectionParam=new ProductInspection();
			productInspectionParam.setSampleNo(sampleNo);
			List<ProductInspection> list = productInspectionMapper.selectProductInspectionList(productInspectionParam);
			return list;
		}
		//非快检业务，直接通过检测报告id获取检测数据
		List<ProductInspection> resultList=new ArrayList<ProductInspection>();
		if(StringUtils.isBlank(productSample.getProductInspectionId())) {
			return null;
		}
		ProductInspection productInspection=productInspectionMapper.selectProductInspectionById(Long.parseLong(productSample.getProductInspectionId()));
		resultList.add(productInspection);
		return resultList;
	}

	@Override
	public Map<String, Object> findNewestListByInspectionSituationList(String entId, String productId, List<String> inspectionSituationList) {
		//基础参数校验
		if(StringUtils.isBlank(entId) || StringUtils.isBlank(productId) ||  inspectionSituationList==null || inspectionSituationList.isEmpty()) {
			return null;
		}
		Map<String,Object> resultMap = new HashMap<>();
		List<ProductInspection> resultList=new ArrayList<ProductInspection>();
		String msg = "";
		for(String inspectionSituation:inspectionSituationList){
			List<ProductInspection> list = findNewestList(entId,productId,inspectionSituation);
			if(list == null || list.isEmpty()){
				if(PropertyEnum.InspectionSituationEnum.IS_05.getKey().equals(inspectionSituation)){
					msg=msg +"质量安全控制符合要求、";
				}else if(PropertyEnum.InspectionSituationEnum.IS_04.getKey().equals(inspectionSituation)){
					msg=msg +"自行检测合格、";
				}else if(PropertyEnum.InspectionSituationEnum.IS_03.getKey().equals(inspectionSituation)){
					msg=msg +"委托检测合格、";
				}
			}else{
				ProductInspection productInspection = list.get(0);
				if(productInspection==null || !"0".equals(productInspection.getInspectionResult())){
					if(PropertyEnum.InspectionSituationEnum.IS_03.getKey().equals(inspectionSituation)){
						msg=msg +"委托检测合格、";
					}else if(PropertyEnum.InspectionSituationEnum.IS_04.getKey().equals(inspectionSituation)){
						msg=msg +"自行检测合格、";
					}
				}else{
					resultList.addAll(list);
				}
			}
		}
		boolean pass = false;
		if("".equals(msg)){
			pass = true;
		}else{
			msg = msg.substring(0,msg.length()-1);
			msg = "请填写"+msg +"依据";
		}
		resultMap.put("pass",pass);
		resultMap.put("msg",msg);
		resultMap.put("resultList",resultList);

		return resultMap;
	}

}
