package com.jkr.project.argi.service.impl;

import cn.hutool.core.map.MapUtil;
import com.jkr.project.argi.domain.AnalysisInfo;
import com.jkr.project.argi.mapper.AnalysisMapper;
import com.jkr.project.argi.service.IAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 首页统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
@Transactional
public class AnalysisServiceImpl implements IAnalysisService {

    @Autowired
    private AnalysisMapper analysisMapper;

    @Override
    public Map<String, Object> findCount() {
        int entCount = analysisMapper.findEntCount();
        int certificateCount = analysisMapper.findCertificateCount();
        int productCount = analysisMapper.findProductCount();
        int scanCount = analysisMapper.findScanCount();
        Map<String, Object> map = MapUtil.newHashMap();
        map.put("entCount", entCount);
        map.put("certificateCount", certificateCount);
        map.put("productCount", productCount);
        map.put("scanCount", scanCount);
        return map;
    }

    @Override
    public int findEntCount() {
        return analysisMapper.findEntCount();
    }

    @Override
    public int findCertificateCount() {
        return analysisMapper.findCertificateCount();
    }

    @Override
    public int findProductCount() {
        return analysisMapper.findProductCount();
    }

    @Override
    public int findScanCount() {
        return analysisMapper.findScanCount();
    }

    @Override
    public List<AnalysisInfo> findEntMainTypeCount(AnalysisInfo analysisInfo) {
        return analysisMapper.findEntMainTypeCount(analysisInfo);
    }

    @Override
    public List<AnalysisInfo> findEntTypeCount(AnalysisInfo analysisInfo) {
        return analysisMapper.findEntTypeCount(analysisInfo);
    }

    @Override
    public List<AnalysisInfo> findCertificateByMainType(AnalysisInfo analysisInfo) {
        return analysisMapper.findCertificateByMainType(analysisInfo);
    }

    @Override
    public List<AnalysisInfo> findCertificateByEntType(AnalysisInfo analysisInfo) {
        return analysisMapper.findCertificateByEntType(analysisInfo);
    }

    @Override
    public List<AnalysisInfo> findCertificateByProductSortCode(AnalysisInfo analysisInfo) {
        return analysisMapper.findCertificateByProductSortCode(analysisInfo);
    }

    @Override
    public List<AnalysisInfo> findProductByProductSortCode(AnalysisInfo analysisInfo) {
        return analysisMapper.findProductByProductSortCode(analysisInfo);
    }

    @Override
    public List<AnalysisInfo> findScanByProductSortCode(AnalysisInfo analysisInfo) {
        return analysisMapper.findScanByProductSortCode(analysisInfo);
    }
}
