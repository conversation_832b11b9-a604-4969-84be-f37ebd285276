package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateProductItemMapper;
import com.jkr.project.argi.domain.CertificateProductItem;
import com.jkr.project.argi.service.ICertificateProductItemService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合格证产品子表(组合品使用)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateProductItemServiceImpl implements ICertificateProductItemService {
	@Autowired
	private CertificateProductItemMapper certificateProductItemMapper;

	/**
	 * 查询合格证产品子表(组合品使用)
	 *
	 * @param id 合格证产品子表(组合品使用)主键
	 * @return 合格证产品子表(组合品使用)
	 */
	@Override
	public CertificateProductItem selectCertificateProductItemById(Long id) {
		return certificateProductItemMapper.selectCertificateProductItemById(id);
	}

	/**
	 * 查询合格证产品子表(组合品使用)列表
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 合格证产品子表(组合品使用)
	 */
	@Override
	public List<CertificateProductItem> selectCertificateProductItemList(CertificateProductItem certificateProductItem) {
		return certificateProductItemMapper.selectCertificateProductItemList(certificateProductItem);
	}

	/**
	 * 新增合格证产品子表(组合品使用)
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateProductItem(CertificateProductItem certificateProductItem) {
		certificateProductItem.insertInit(SecurityUtils.getLoginUser().getUsername());
        
			return certificateProductItemMapper.insertCertificateProductItem(certificateProductItem);
	}

	/**
	 * 修改合格证产品子表(组合品使用)
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateProductItem(CertificateProductItem certificateProductItem) {
		certificateProductItem.updateInit(SecurityUtils.getLoginUser().getUsername());
        
		return certificateProductItemMapper.updateCertificateProductItem(certificateProductItem);
	}

	/**
	 * 批量删除合格证产品子表(组合品使用)
	 *
	 * @param ids 需要删除的合格证产品子表(组合品使用)主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateProductItemByIds(List<Long> ids) {
		return certificateProductItemMapper.logicRemoveByIds(ids);
		//return certificateProductItemMapper.deleteCertificateProductItemByIds(ids);
	}

	/**
	 * 删除合格证产品子表(组合品使用)信息
	 *
	 * @param id 合格证产品子表(组合品使用)主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateProductItemById(Long id) {
		return certificateProductItemMapper.logicRemoveById(id);
		//return certificateProductItemMapper.deleteCertificateProductItemById(id);
	}

}
