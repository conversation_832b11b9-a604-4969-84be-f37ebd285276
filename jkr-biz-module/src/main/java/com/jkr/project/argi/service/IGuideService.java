package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .Guide;

/**
 * 农业技术指导Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IGuideService {
	/**
	 * 查询农业技术指导
	 *
	 * @param id 农业技术指导主键
	 * @return 农业技术指导
	 */
	public Guide selectGuideById(Long id);

	/**
	 * 查询农业技术指导列表
	 *
	 * @param guide 农业技术指导
	 * @return 农业技术指导集合
	 */
	public List<Guide> selectGuideList(Guide guide);

	/**
	 * 新增农业技术指导
	 *
	 * @param guide 农业技术指导
	 * @return 结果
	 */
	public int insertGuide(Guide guide);

	/**
	 * 修改农业技术指导
	 *
	 * @param guide 农业技术指导
	 * @return 结果
	 */
	public int updateGuide(Guide guide);

	/**
	 * 批量删除农业技术指导
	 *
	 * @param ids 需要删除的农业技术指导主键集合
	 * @return 结果
	 */
	public int deleteGuideByIds(List<Long> ids);

	/**
	 * 删除农业技术指导信息
	 *
	 * @param id 农业技术指导主键
	 * @return 结果
	 */
	public int deleteGuideById(Long id);

}
