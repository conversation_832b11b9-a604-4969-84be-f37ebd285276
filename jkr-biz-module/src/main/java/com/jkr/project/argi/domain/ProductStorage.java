package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 产品保质方式对象 bas_product_storage
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_product_storage")
public class ProductStorage extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Excel(name = "主体id")
    private String entId;

    /**
     * 产品id
     */
    @Excel(name = "产品id")
    private String productId;

    /**
     * 保存条件code
     */
    @Excel(name = "保存条件code")
    private String storageTypeCode;

    /**
     * 保存条件name
     */
    @Excel(name = "保存条件name")
    private String storageTypeName;

    /**
     * 保存时间
     */
    @Excel(name = "保存时间")
    private String storageDays;

    /**
     * 保存单位
     */
    @Excel(name = "保存单位")
    private String storageUnit;

    /**
     * 补充说明
     */
    @Excel(name = "补充说明")
    private String instruction;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;
}
