package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .CertificateConfigDevice;

/**
 * 电子合格证配置-设备Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateConfigDeviceService {
	/**
	 * 查询电子合格证配置-设备
	 *
	 * @param id 电子合格证配置-设备主键
	 * @return 电子合格证配置-设备
	 */
	public CertificateConfigDevice selectCertificateConfigDeviceById(Long id);

	/**
	 * 查询电子合格证配置-设备列表
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 电子合格证配置-设备集合
	 */
	public List<CertificateConfigDevice> selectCertificateConfigDeviceList(CertificateConfigDevice certificateConfigDevice);

	/**
	 * 新增电子合格证配置-设备
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 结果
	 */
	public int insertCertificateConfigDevice(CertificateConfigDevice certificateConfigDevice);

	/**
	 * 修改电子合格证配置-设备
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 结果
	 */
	public int updateCertificateConfigDevice(CertificateConfigDevice certificateConfigDevice);

	/**
	 * 批量删除电子合格证配置-设备
	 *
	 * @param ids 需要删除的电子合格证配置-设备主键集合
	 * @return 结果
	 */
	public int deleteCertificateConfigDeviceByIds(List<Long> ids);

	/**
	 * 删除电子合格证配置-设备信息
	 *
	 * @param id 电子合格证配置-设备主键
	 * @return 结果
	 */
	public int deleteCertificateConfigDeviceById(Long id);

	public void saveBatch(String certificateConfigId,List<String> deviceIdList);

	public int deleteByCertificateConfigId(String certificateConfigId);

}
