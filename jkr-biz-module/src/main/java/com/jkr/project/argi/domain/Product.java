package com.jkr.project.argi.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 产品对象 bas_product
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_product")
public class Product extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Excel(name = "产品名称", sort = 1)
    private String name;

    /**
     * 组合品标识：0-单品;1-组合品
     */
    @Excel(name = "产品类型", sort = 4)
    private String mixFlag;

    /**
     * 用户id（乡村版合格证用）
     */
    private String userId;

    /**
     * 企业id
     */
    private String entId;

    @Excel(name = "生产经营主体名称", sort = 8)
    private String entName;

    /**
     * 主体类型(0:种植；1:养殖；2.检测机构)
     */
    @Excel(name = "主体类型", sort = 9, dictType = "ent_business_type")
    private String businessType;

    /**
     * 主体性质(0:企业；1:个人；2.检测机构)
     */
    @Excel(name = "主体性质", sort = 10, dictType = "ent_type")
    private String entType;

    /**
     * 数据来源
     */
    @Excel(name = "产品来源", sort = 5)
    private String dataScope;

    /**
     * 分类code
     */
    private String productSortCode;

    /**
     * 分类名称
     */
    @Excel(name = "产品分类", sort = 2)
    private String productSortName;

    /**
     * 产品认证（有机农产品，绿色食品，无公害农产品，农产品地理标志）
     */
    private String productCertificationCode;

    /**
     * 产品认证名称
     */
    @Excel(name = "产品认证", sort = 6)
    private String productCertificationName;

    /**
     * 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "添加时间", width = 30, dateFormat = "yyyy-MM-dd", sort = 11)
    private Date addDate;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县区
     */
    private String county;

    /**
     * 地址(省市县)
     */
    private String address;

    /**
     * 详细地址
     */
    @Excel(name = "生产地址", sort = 7)
    private String detail;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 产品介绍
     */
    private String productIntroduction;

    /**
     * 最新样品编号
     */
    private String currentSampleNo;

    /**
     * 二维码url
     */
    private String queryCodeUrl;

    /**
     * 电商链接
     */
    private String electricityLink;

    /**
     * 种养殖规模
     */
    private String scaleAmount;

    /**
     * 规模单位code
     */
    private String scaleUnitCode;

    /**
     * 规模单位name
     */
    private String scaleUnitName;

    /**
     * 年产值
     */
    private String productionValue;

    /**
     * 年产量
     */
    private String productionAmount;

    /**
     * 年产量单位code
     */
    private String productionUnitCode;

    /**
     * 年产量单位name
     */
    private String productionUnitName;

    /**
     * 开具数量
     */
    @Excel(name = "开具数量", sort = 3)
    private Long printAmount;

    /**
     * 开具时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最近一次开具日期", width = 30, dateFormat = "yyyy-MM-dd", sort = 12)
    private Date printDate;

    /**
     * 复购商品id
     */
    private String reBuyProductId;

    /**
     * 复购按钮是否显示1显示,其他不显示
     */
    private String reBuyVisible;

    /**
     * $column.columnComment
     */
    private String reBuyProductName;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;
	@TableField(exist = false)
	private Date beginAddDate;		// 开始 添加时间
	@TableField(exist = false)
	private Date endAddDate;		// 结束 添加时间
	@TableField(exist = false)
	private String addDateOrder;
	@TableField(exist = false)
	private String printCountOrder;
	@TableField(exist = false)
	private String count; //打印数量
	@TableField(exist = false)
	private Date beginPrintDate;		// 开始 开具时间
	@TableField(exist = false)
	private Date endPrintDate;		// 结束 开具时间
	@TableField(exist = false)
	private List<ProductStorage> productStorageList;
	@TableField(exist = false)
	private List<String> productStorageDelIdList;
	@TableField(exist = false)
	private List<ProductItem> productItemList;
	@TableField(exist = false)
	private List<SysFile> fileList = Lists.newArrayList();
	@TableField(exist = false)
	private List<String> idList = Lists.newArrayList();
    @TableField(exist = false)
    private String shopId;//电话号密文(查询商品列表参数)
    @TableField(exist = false)
    private Integer beginPrintAmount;
    @TableField(exist = false)
    private Integer endPrintAmount;
}
