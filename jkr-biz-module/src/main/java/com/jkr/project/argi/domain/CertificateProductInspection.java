package com.jkr.project.argi.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 合格证-产品检测关系对象 bas_certificate_product_inspection
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_certificate_product_inspection")
public class CertificateProductInspection extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 合格证id
     */
    @Excel(name = "合格证id")
    private String certificateId;

    /**
     * 检测id
     */
    @Excel(name = "检测id")
    private String productInspectionId;

    /**
     * 主键集合
     */
    private List<Long> ids;
}
