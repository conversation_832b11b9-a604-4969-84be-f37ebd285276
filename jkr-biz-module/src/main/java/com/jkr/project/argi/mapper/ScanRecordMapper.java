package com.jkr.project.argi.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.ScanRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 扫描日志Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ScanRecordMapper extends BaseMapper<ScanRecord>{
	/**
	 * 查询扫描日志
	 *
	 * @param id 扫描日志主键
	 * @return 扫描日志
	 */
	public ScanRecord selectScanRecordById(Long id);

	/**
	 * 查询扫描日志列表
	 *
	 * @param scanRecord 扫描日志
	 * @return 扫描日志集合
	 */
	public List<ScanRecord> selectScanRecordList(ScanRecord scanRecord);

	/**
	 * 新增扫描日志
	 *
	 * @param scanRecord 扫描日志
	 * @return 结果
	 */
	public int insertScanRecord(ScanRecord scanRecord);

	/**
	 * 修改扫描日志
	 *
	 * @param scanRecord 扫描日志
	 * @return 结果
	 */
	public int updateScanRecord(ScanRecord scanRecord);

	/**
	 * 删除扫描日志
	 *
	 * @param id 扫描日志主键
	 * @return 结果
	 */
	public int deleteScanRecordById(Long id);

	/**
	 * 批量删除扫描日志
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteScanRecordByIds(Long[] ids);

	/**
	 * 批量逻辑删除扫描日志
	 *
	 * @param  ids 扫描日志主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过扫描日志主键id逻辑删除信息
	 *
	 * @param  id 扫描日志主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public List<ScanRecord> findEntScanList(ScanRecord scanRecord);

	public List<ScanRecord> findByCertificateId(@Param("certificateId") String certificateId);

	public Map<String,Object> getCountAmountByCertificateId(@Param("certificateId")String certificateId);

	public List<ScanRecord> findByFullNumber(@Param("fullNumber")String fullNumber);

	public List<ScanRecord> findTraceStatisticList(ScanRecord scanRecord);
}
