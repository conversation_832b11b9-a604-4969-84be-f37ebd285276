package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateProductInspection;
import com.jkr.project.argi.service.ICertificateProductInspectionService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 合格证-产品检测关系Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateProductInspection")
public class CertificateProductInspectionController extends BaseController {
	@Autowired
	private ICertificateProductInspectionService certificateProductInspectionService;

/**
 * 查询合格证-产品检测关系列表
 */
@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:list')")
@GetMapping("/list")
	public TableDataInfo list(CertificateProductInspection certificateProductInspection) {
		startPage();
		List<CertificateProductInspection> list = certificateProductInspectionService.selectCertificateProductInspectionList(certificateProductInspection);
		return getDataTable(list);
	}

	/**
	 * 导出合格证-产品检测关系列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:export')")
	@Log(title = "导出合格证-产品检测关系列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, CertificateProductInspection certificateProductInspection) {
		List<CertificateProductInspection> list = certificateProductInspectionService.selectCertificateProductInspectionList(certificateProductInspection);
		ExcelUtil<CertificateProductInspection> util = new ExcelUtil<CertificateProductInspection>(CertificateProductInspection. class);
		util.exportExcel(response, list, "合格证-产品检测关系数据");
	}

	/**
	 * 获取合格证-产品检测关系详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(certificateProductInspectionService.selectCertificateProductInspectionById(id));
	}

	/**
	 * 新增合格证-产品检测关系
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:add')")
	@Log(title = "新增合格证-产品检测关系", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody CertificateProductInspection certificateProductInspection) {
		return toAjax(certificateProductInspectionService.insertCertificateProductInspection(certificateProductInspection));
	}

	/**
	 * 修改合格证-产品检测关系
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:edit')")
	@Log(title = "修改合格证-产品检测关系", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody CertificateProductInspection certificateProductInspection) {
		return toAjax(certificateProductInspectionService.updateCertificateProductInspection(certificateProductInspection));
	}

	/**
	 * 删除合格证-产品检测关系
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:remove')")
	@Log(title = "删除合格证-产品检测关系", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(certificateProductInspectionService.deleteCertificateProductInspectionById(id));
	}

	/**
	 * 批量删除合格证-产品检测关系
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductInspection:batchRemove')")
	@Log(title = "批量删除合格证-产品检测关系", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody CertificateProductInspection certificateProductInspection) {
		return toAjax(certificateProductInspectionService.deleteCertificateProductInspectionByIds(certificateProductInspection.getIds()));
	}
}
