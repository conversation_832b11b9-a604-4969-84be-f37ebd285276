package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Detection;
import com.jkr.project.argi.service.IDetectionService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 与快检系统对接Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/detection")
public class DetectionController extends BaseController {
	@Autowired
	private IDetectionService detectionService;

/**
 * 查询与快检系统对接列表
 */
@PreAuthorize("@ss.hasPermi('argi:detection:list')")
@GetMapping("/list")
	public TableDataInfo list(Detection detection) {
		startPage();
		List<Detection> list = detectionService.selectDetectionList(detection);
		return getDataTable(list);
	}

	/**
	 * 导出与快检系统对接列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:detection:export')")
	@Log(title = "导出与快检系统对接列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, Detection detection) {
		List<Detection> list = detectionService.selectDetectionList(detection);
		ExcelUtil<Detection> util = new ExcelUtil<Detection>(Detection. class);
		util.exportExcel(response, list, "与快检系统对接数据");
	}

	/**
	 * 获取与快检系统对接详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:detection:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(detectionService.selectDetectionById(id));
	}

	/**
	 * 新增与快检系统对接
	 */
	@PreAuthorize("@ss.hasPermi('argi:detection:add')")
	@Log(title = "新增与快检系统对接", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody Detection detection) {
		return toAjax(detectionService.insertDetection(detection));
	}

	/**
	 * 修改与快检系统对接
	 */
	@PreAuthorize("@ss.hasPermi('argi:detection:edit')")
	@Log(title = "修改与快检系统对接", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody Detection detection) {
		return toAjax(detectionService.updateDetection(detection));
	}

	/**
	 * 删除与快检系统对接
	 */
	@PreAuthorize("@ss.hasPermi('argi:detection:remove')")
	@Log(title = "删除与快检系统对接", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(detectionService.deleteDetectionById(id));
	}

	/**
	 * 批量删除与快检系统对接
	 */
	@PreAuthorize("@ss.hasPermi('argi:detection:batchRemove')")
	@Log(title = "批量删除与快检系统对接", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody Detection detection) {
		return toAjax(detectionService.deleteDetectionByIds(detection.getIds()));
	}
}
