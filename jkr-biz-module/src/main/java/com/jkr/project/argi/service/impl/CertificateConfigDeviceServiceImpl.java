package com.jkr.project.argi.service.impl;

import java.util.ArrayList;
import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateConfigDeviceMapper;
import com.jkr.project.argi.domain.CertificateConfigDevice;
import com.jkr.project.argi.service.ICertificateConfigDeviceService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 电子合格证配置-设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateConfigDeviceServiceImpl implements ICertificateConfigDeviceService {
	@Autowired
	private CertificateConfigDeviceMapper certificateConfigDeviceMapper;

	/**
	 * 查询电子合格证配置-设备
	 *
	 * @param id 电子合格证配置-设备主键
	 * @return 电子合格证配置-设备
	 */
	@Override
	public CertificateConfigDevice selectCertificateConfigDeviceById(Long id) {
		return certificateConfigDeviceMapper.selectCertificateConfigDeviceById(id);
	}

	/**
	 * 查询电子合格证配置-设备列表
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 电子合格证配置-设备
	 */
	@Override
	public List<CertificateConfigDevice> selectCertificateConfigDeviceList(CertificateConfigDevice certificateConfigDevice) {
		return certificateConfigDeviceMapper.selectCertificateConfigDeviceList(certificateConfigDevice);
	}

	/**
	 * 新增电子合格证配置-设备
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateConfigDevice(CertificateConfigDevice certificateConfigDevice) {
		certificateConfigDevice.insertInit(SecurityUtils.getLoginUser().getUsername());
		return certificateConfigDeviceMapper.insertCertificateConfigDevice(certificateConfigDevice);
	}

	/**
	 * 修改电子合格证配置-设备
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateConfigDevice(CertificateConfigDevice certificateConfigDevice) {
		certificateConfigDevice.updateInit(SecurityUtils.getLoginUser().getUsername());
		return certificateConfigDeviceMapper.updateCertificateConfigDevice(certificateConfigDevice);
	}

	/**
	 * 批量删除电子合格证配置-设备
	 *
	 * @param ids 需要删除的电子合格证配置-设备主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateConfigDeviceByIds(List<Long> ids) {
		return certificateConfigDeviceMapper.logicRemoveByIds(ids);
		//return certificateConfigDeviceMapper.deleteCertificateConfigDeviceByIds(ids);
	}

	/**
	 * 删除电子合格证配置-设备信息
	 *
	 * @param id 电子合格证配置-设备主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateConfigDeviceById(Long id) {
		return certificateConfigDeviceMapper.logicRemoveById(id);
		//return certificateConfigDeviceMapper.deleteCertificateConfigDeviceById(id);
	}

	/**
	 *
	 * @title: saveBatch
	 * @author: lxy
	 * @date: 2021年1月6日 下午2:14:49
	 * @description: 批量保存
	 * @param: @param certificateConfigId
	 * @param: @param deviceIdList
	 * @return: void
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveBatch(String certificateConfigId, List<String> deviceIdList) {
		if(StringUtils.isNotBlank(certificateConfigId) && !deviceIdList.isEmpty()) {
			List<CertificateConfigDevice> list=new ArrayList<CertificateConfigDevice>();
			for(String deviceId:deviceIdList) {
				CertificateConfigDevice certificateConfigDevice=new CertificateConfigDevice();
				certificateConfigDevice.setCertificateConfigId(certificateConfigId);
				certificateConfigDevice.setDeviceId(deviceId);
				certificateConfigDevice.insertInit(SecurityUtils.getLoginUser().getUsername());
				list.add(certificateConfigDevice);
			}
			//批量保存
			certificateConfigDeviceMapper.insertBatch(list);
		}
	}

	/**
	 *
	 * @title: deleteByCertificateConfigId
	 * @author: lxy
	 * @date: 2021年1月6日 下午3:07:37
	 * @description: 删除
	 * @param: certificateConfigId
	 * @return: int
	 */
	@Override
	public int deleteByCertificateConfigId(String certificateConfigId) {
		CertificateConfigDevice certificateConfigDevice=new CertificateConfigDevice();
		certificateConfigDevice.setCertificateConfigId(certificateConfigId);
		return certificateConfigDeviceMapper.deleteByCertificateConfigId(certificateConfigDevice);
	}

}
