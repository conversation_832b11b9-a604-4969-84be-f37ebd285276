package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .CertificateProductInspection;

/**
 * 合格证-产品检测关系Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateProductInspectionService {
	/**
	 * 查询合格证-产品检测关系
	 *
	 * @param id 合格证-产品检测关系主键
	 * @return 合格证-产品检测关系
	 */
	public CertificateProductInspection selectCertificateProductInspectionById(Long id);

	/**
	 * 查询合格证-产品检测关系列表
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 合格证-产品检测关系集合
	 */
	public List<CertificateProductInspection> selectCertificateProductInspectionList(CertificateProductInspection certificateProductInspection);

	/**
	 * 新增合格证-产品检测关系
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 结果
	 */
	public int insertCertificateProductInspection(CertificateProductInspection certificateProductInspection);

	/**
	 * 修改合格证-产品检测关系
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 结果
	 */
	public int updateCertificateProductInspection(CertificateProductInspection certificateProductInspection);

	/**
	 * 批量删除合格证-产品检测关系
	 *
	 * @param ids 需要删除的合格证-产品检测关系主键集合
	 * @return 结果
	 */
	public int deleteCertificateProductInspectionByIds(List<Long> ids);

	/**
	 * 删除合格证-产品检测关系信息
	 *
	 * @param id 合格证-产品检测关系主键
	 * @return 结果
	 */
	public int deleteCertificateProductInspectionById(Long id);

}
