/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.jkr.project.argi.util;

import com.google.common.collect.Maps;
import com.jkr.common.utils.StringUtils;
import com.jkr.common.utils.spring.SpringUtils;
import com.jkr.project.argi.mapper.CertificateNoMapper;

import java.util.Map;

/**
 * 流水号工具类
 *
 * <AUTHOR>
 * @version 2020年8月4日11:26:05
 */
public class SerialNumberUtils {

	private static CertificateNoMapper certificateNoDao = SpringUtils.getBean(CertificateNoMapper.class);

	// 类初始化时，不初始化这个对象(延时加载，真正用的时候再创建)
	private static SerialNumberUtils instance;

	// 构造器私有化
	private SerialNumberUtils() {
	}

	// 方法同步，调用效率低
	public static synchronized SerialNumberUtils getInstance() {
		if (instance == null) {
			instance = new SerialNumberUtils();
		}
		return instance;
	}

	private Map<String, Object> cache = Maps.newConcurrentMap();
	//根据 key 获取缓存对象
	public Object get(String key) {
		return cache.get(key);
	}
	//添加、更新缓存对象
	public void addOrUpdateCache(String key, Object value) {
		cache.put(key, value);
	}

	// 根据 key 来删除缓存中的一条记录
	public void evictCache(String key) {
		if (cache.containsKey(key)) {
			cache.remove(key);
		}
	}

	// 清空缓存中的所有记录
	public void evictCache() {
		cache.clear();
	}

	public synchronized Integer geneterNextNumber(String name, String batchNo) {
		Integer sn = this.geneterNextNumber(batchNo);
		System.out.println(name + ":" + sn);
		return sn;
	}

	/**
	 *
	 * @title: geneterNextNumber
	 * @author: lxy
	 * @date: 2020年8月4日 下午1:54:25
	 * @description: 获取下一个流水号
	 * @param: batchNo
	 * @return: Integer
	 */
	public synchronized Integer geneterNextNumber(String batchNo) {
		if (StringUtils.isBlank(batchNo)) {
			return null;
		}
		Integer no = SerialNumberUtils.getInstance().getSerialNumber(batchNo);
		if (no == null) {
			no = 1;
		} else {
			no++;
		}
		SerialNumberUtils.getInstance().addOrUpdateCache(batchNo, no);
		return no;

	}

	/**
	 *
	 * @title: getSerialNumber
	 * @author: lxy
	 * @date: 2020年8月4日 下午1:54:55
	 * @description: 根据批次号获取最大流水号
	 * @param: batchNo
	 * @param:
	 * @return: Integer
	 */
	public Integer getSerialNumber(String batchNo) {
		if(StringUtils.isBlank(batchNo)) {
			return null;
		}
		Integer serialNumber=null;
		if(null==SerialNumberUtils.getInstance().get(batchNo)) {
			serialNumber = certificateNoDao.getMaxSerialNumber(batchNo);
			if (null == serialNumber) {
				return null;
			}
			SerialNumberUtils.getInstance().addOrUpdateCache(batchNo, serialNumber);
		}else {
			serialNumber = Integer.parseInt(SerialNumberUtils.getInstance().get(batchNo).toString());
		}
		return serialNumber;
	}

	public static void main(String[] args) {
		/*
		 * String s = String.format("%06d", 123);// 其中0表示补零而不是补空格，6表示至少6位
		 * System.out.println(s);
		 */

		Thread t1 = new Thread() {
			@Override
			public void run() {
				for (int i = 0; i < 100; i++) {
					try {
						sleep(200);
						SerialNumberUtils.getInstance().geneterNextNumber("张三", "test1-");
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			}
		};
		Thread t2 = new Thread() {
			@Override
			public void run() {
				for (int i = 0; i < 50; i++) {
					try {
						sleep(100);
						SerialNumberUtils.getInstance().geneterNextNumber("李四", "test1-");
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			}
		};
		Thread t3 = new Thread() {
			@Override
			public void run() {
				for (int i = 0; i < 50; i++) {
					try {
						sleep(50);
						SerialNumberUtils.getInstance().geneterNextNumber("王五", "test2-");
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				}
			}
		};
		t1.run();
		t2.run();
		t3.run();
	}

}
