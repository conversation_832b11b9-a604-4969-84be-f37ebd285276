package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.ProductRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 产品检测记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ProductRecordMapper extends BaseMapper<ProductRecord>{
	/**
	 * 查询产品检测记录
	 *
	 * @param id 产品检测记录主键
	 * @return 产品检测记录
	 */
	public ProductRecord selectProductRecordById(Long id);

	/**
	 * 查询产品检测记录列表
	 *
	 * @param productRecord 产品检测记录
	 * @return 产品检测记录集合
	 */
	public List<ProductRecord> selectProductRecordList(ProductRecord productRecord);

	/**
	 * 新增产品检测记录
	 *
	 * @param productRecord 产品检测记录
	 * @return 结果
	 */
	public int insertProductRecord(ProductRecord productRecord);

	/**
	 * 修改产品检测记录
	 *
	 * @param productRecord 产品检测记录
	 * @return 结果
	 */
	public int updateProductRecord(ProductRecord productRecord);

	/**
	 * 删除产品检测记录
	 *
	 * @param id 产品检测记录主键
	 * @return 结果
	 */
	public int deleteProductRecordById(Long id);

	/**
	 * 批量删除产品检测记录
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteProductRecordByIds(Long[] ids);

	/**
	 * 批量逻辑删除产品检测记录
	 *
	 * @param  ids 产品检测记录主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过产品检测记录主键id逻辑删除信息
	 *
	 * @param  id 产品检测记录主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public ProductRecord getBySampleNo(@Param("sampleNo")String sampleNo);

	public List<ProductRecord> findListByProductId(ProductRecord productRecord);

	public List<ProductRecord> findListValidDetection(ProductRecord productRecord);
}
