package com.jkr.project.argi.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 签名对象 bas_autograph
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_autograph")
public class Autograph extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Excel(name = "主体id")
    private String entId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String autograph;

    /**
     * 主键集合
     */
    private List<Long> ids;
}
