package com.jkr.project.argi.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jkr.common.constant.Constants;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.service.IEntService;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.EntChangeMapper;
import com.jkr.project.argi.domain.EntChange;
import com.jkr.project.argi.service.IEntChangeService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主体信息变更Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class EntChangeServiceImpl extends ServiceImpl<EntChangeMapper, EntChange> implements IEntChangeService {
	@Autowired
	private EntChangeMapper entChangeMapper;
	@Autowired
	private ISysFileService fileService;
	@Lazy
	@Autowired
	private IEntService entService;

	/**
	 * 查询主体信息变更
	 *
	 * @param id 主体信息变更主键
	 * @return 主体信息变更
	 */
	@Override
	public EntChange selectEntChangeById(Long id) {
		return entChangeMapper.selectEntChangeById(id);
	}

	/**
	 * 查询主体信息变更列表
	 *
	 * @param entChange 主体信息变更
	 * @return 主体信息变更
	 */
	@Override
	public List<EntChange> selectEntChangeList(EntChange entChange) {
		if(!Constants.BUSINESS_SYSTEM_FLAG.equals(SecurityUtils.getLoginUser().getUsername())){
			entChange.setLoginAreaCode(SecurityUtils.getLoginUser().getUser().getDept().getAreaCode());
		}
		return entChangeMapper.findList(entChange);
	}

	/**
	 * 新增主体信息变更
	 *
	 * @param entChange 主体信息变更
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertEntChange(EntChange entChange) {
		entChange.insertInit(SecurityUtils.getLoginUser().getUsername());

			return entChangeMapper.insertEntChange(entChange);
	}

	/**
	 * 修改主体信息变更
	 *
	 * @param entChange 主体信息变更
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateEntChange(EntChange entChange) {
		entChange.updateInit(SecurityUtils.getLoginUser().getUsername());

		return entChangeMapper.updateEntChange(entChange);
	}

	/**
	 * 批量删除主体信息变更
	 *
	 * @param ids 需要删除的主体信息变更主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteEntChangeByIds(List<Long> ids) {
		return entChangeMapper.logicRemoveByIds(ids);
		//return entChangeMapper.deleteEntChangeByIds(ids);
	}

	/**
	 * 删除主体信息变更信息
	 *
	 * @param id 主体信息变更主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteEntChangeById(Long id) {
		return entChangeMapper.logicRemoveById(id);
		//return entChangeMapper.deleteEntChangeById(id);
	}

	/**
	 *
	 * @title: findPassList
	 * @author: lxy
	 * @date: 2021年2月25日 下午6:14:29
	 * @description: 获取通过集合
	 * @param: entId
	 * @return: List<EntChange>
	 */
	@Override
	public List<EntChange> findPassList(Long entId){
		if(ObjectUtil.isNotNull(entId)) {
			return null;
		}
		EntChange entChange=new EntChange();
		entChange.setEntId(String.valueOf(entId));
		entChange.setExamineStatus(EnumProperty.ExamineStatusEnum.PASS.getKey());
		return entChangeMapper.selectEntChangeList(entChange);
	}

	/**
	 *
	 * @title: updateBasicEnterFinish
	 * @author: lxy
	 * @date: 2021年2月24日19:32:46
	 * @description: 更新基础信息录入标示完成标识
	 * @param: id
	 * @return: void
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void updateBasicEnterFinish(String id) {
		if(StringUtils.isBlank(id)) {
			return ;
		}
		EntChange entChange=getById(id);
		entChange.setExamineStatus(EnumProperty.ExamineStatusEnum.TODO.getKey());
		entChange.setBasicEnterFlag(Constants.YES);
		this.save(entChange);
	}
	/**
	 *
	 * @title: getUnfinished
	 * @author: lxy
	 * @date: 2021年2月25日 下午7:34:11
	 * @description: 获取未完成的状态的变更记录
	 * @param: @param entChange
	 * @param: @return
	 * @return: EntChange
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public EntChange getUnfinished(EntChange entChange) {
		if(null==entChange || StringUtil.isBlank(entChange.getEntId())) {
			return null;
		}
		return entChangeMapper.getUnfinished(entChange);
	}
	/**
	 *
	 * @title: getChange
	 * @author: lxy
	 * @date: 2021年2月25日19:10:52
	 * @description: 获取主体变更信息
	 * @param: entChange
	 * @return: EntChange
	 */
	@Override
	public EntChange getChange(EntChange entChange){
		EntChange change = this.getUnfinished(entChange);
		if(null==change) {
			return null;
		}
		SysFile attachment = new SysFile();
		attachment.setTableId(change.getId());
		attachment.setTableName(EnumProperty.TableNameEnum.ENT_CHANGE.getKey());
		attachment.setTableId(change.getId());
		List<SysFile> attachmentList = fileService.findUrlsFileList(attachment);
		change.setFileList(attachmentList);
		return change;
	}

	/**
	 *
	 * @title: saveEnt
	 * @author: lxy
	 * @date: 2021年2月24日 下午6:43:37
	 * @description: 主体变更保存方法
	 * @param: entChange
	 * @param: @throws Exception
	 * @return: EntChange
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public EntChange saveEntChange(EntChange entChange){
		entChange.setSubmitDate(new Date());

		this.save(entChange);
		//保存新附件
		List<SysFile> finalList = entChange.getFileList();
		if (!finalList.isEmpty()) {
			for (SysFile att : finalList) {
				att.setTableId(entChange.getId());
				att.setTableName(EnumProperty.TableNameEnum.ENT_CHANGE.getKey());
				att.setFieldType("fieldType");
			}
			fileService.insertSysFileList(finalList);
		}
		//TODO 同步保存机构表数据---审核通过时，将主体变更表的信息，同步修改机构表数据
		/**
		 * 主体变更业务:主体信息状态变为"变更暂存"
		 */
		entService.updateExamineStatusChangeTemp(entChange.getEntId());

//		//2023-12-06 自动审核
//		entChange.setExamineStatus(EnumProperty.ExamineStatusEnum.PASS.getKey());
//		entChange.setExamineDate(new Date());
//		entChange.setExamineMan("平台管理员");
//
//		this.examineSave(entChange);
//
		return entChange;
	}
	/**
	 * @throws Exception
	 * @Title: examineSave
	 * @author: lxy
	 * @date: 2021年2月24日19:27:06
	 * @Description: 审批保存
	 * @param:  entChange
	 * @throws
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void examineSave(EntChange entChange){
		entChange.updateInit(SecurityUtils.getUsername());
		entChange.setExamineDate(new Date());
		entChange.setExamineMan(SecurityUtils.getLoginUser().getUser().getNickName());
		entChangeMapper.examineSave(entChange);
		//更新主体信息状态
		entService.updateEntChange(entChange);
	}

}
