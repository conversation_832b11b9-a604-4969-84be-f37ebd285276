package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateProductInspectionMapper;
import com.jkr.project.argi.domain.CertificateProductInspection;
import com.jkr.project.argi.service.ICertificateProductInspectionService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合格证-产品检测关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateProductInspectionServiceImpl implements ICertificateProductInspectionService {
	@Autowired
	private CertificateProductInspectionMapper certificateProductInspectionMapper;

	/**
	 * 查询合格证-产品检测关系
	 *
	 * @param id 合格证-产品检测关系主键
	 * @return 合格证-产品检测关系
	 */
	@Override
	public CertificateProductInspection selectCertificateProductInspectionById(Long id) {
		return certificateProductInspectionMapper.selectCertificateProductInspectionById(id);
	}

	/**
	 * 查询合格证-产品检测关系列表
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 合格证-产品检测关系
	 */
	@Override
	public List<CertificateProductInspection> selectCertificateProductInspectionList(CertificateProductInspection certificateProductInspection) {
		return certificateProductInspectionMapper.selectCertificateProductInspectionList(certificateProductInspection);
	}

	/**
	 * 新增合格证-产品检测关系
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateProductInspection(CertificateProductInspection certificateProductInspection) {
		certificateProductInspection.insertInit(SecurityUtils.getLoginUser().getUsername());
        
			return certificateProductInspectionMapper.insertCertificateProductInspection(certificateProductInspection);
	}

	/**
	 * 修改合格证-产品检测关系
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateProductInspection(CertificateProductInspection certificateProductInspection) {
		certificateProductInspection.updateInit(SecurityUtils.getLoginUser().getUsername());
        
		return certificateProductInspectionMapper.updateCertificateProductInspection(certificateProductInspection);
	}

	/**
	 * 批量删除合格证-产品检测关系
	 *
	 * @param ids 需要删除的合格证-产品检测关系主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateProductInspectionByIds(List<Long> ids) {
		return certificateProductInspectionMapper.logicRemoveByIds(ids);
		//return certificateProductInspectionMapper.deleteCertificateProductInspectionByIds(ids);
	}

	/**
	 * 删除合格证-产品检测关系信息
	 *
	 * @param id 合格证-产品检测关系主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateProductInspectionById(Long id) {
		return certificateProductInspectionMapper.logicRemoveById(id);
		//return certificateProductInspectionMapper.deleteCertificateProductInspectionById(id);
	}

}
