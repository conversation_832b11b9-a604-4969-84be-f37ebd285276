package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .Feedback;

/**
 * 反馈信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IFeedbackService {
	/**
	 * 查询反馈信息
	 *
	 * @param id 反馈信息主键
	 * @return 反馈信息
	 */
	public Feedback selectFeedbackById(Long id);

	/**
	 * 查询反馈信息列表
	 *
	 * @param feedback 反馈信息
	 * @return 反馈信息集合
	 */
	public List<Feedback> selectFeedbackList(Feedback feedback);

	/**
	 * 新增反馈信息
	 *
	 * @param feedback 反馈信息
	 * @return 结果
	 */
	public int insertFeedback(Feedback feedback);

	/**
	 * 修改反馈信息
	 *
	 * @param feedback 反馈信息
	 * @return 结果
	 */
	public int updateFeedback(Feedback feedback);

	/**
	 * 批量删除反馈信息
	 *
	 * @param ids 需要删除的反馈信息主键集合
	 * @return 结果
	 */
	public int deleteFeedbackByIds(List<Long> ids);

	/**
	 * 删除反馈信息信息
	 *
	 * @param id 反馈信息主键
	 * @return 结果
	 */
	public int deleteFeedbackById(Long id);

}
