package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 电子合格证配置-模板对象 bas_certificate_config_template
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_certificate_config_template")
public class CertificateConfigTemplate extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 合格证配置id
     */
    @Excel(name = "合格证配置id")
    private String certificateConfigId;

    /**
     * 设备id
     */
    @Excel(name = "设备id")
    private String deviceId;

    /**
     * 合格证模板id
     */
    @Excel(name = "合格证模板id")
    private String templateId;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private DeviceParameter deviceParameter;//蓝牙设备参数
    @TableField(exist = false)
    private CertificateTemplate certificateTemplate;//合格证模板
}
