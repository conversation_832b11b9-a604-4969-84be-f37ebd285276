package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ProductStorageMapper;
import com.jkr.project.argi.domain.ProductStorage;
import com.jkr.project.argi.service.IProductStorageService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品保质方式Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ProductStorageServiceImpl implements IProductStorageService {
	@Autowired
	private ProductStorageMapper productStorageMapper;

	/**
	 * 查询产品保质方式
	 *
	 * @param id 产品保质方式主键
	 * @return 产品保质方式
	 */
	@Override
	public ProductStorage selectProductStorageById(Long id) {
		return productStorageMapper.selectProductStorageById(id);
	}

	/**
	 * 查询产品保质方式列表
	 *
	 * @param productStorage 产品保质方式
	 * @return 产品保质方式
	 */
	@Override
	public List<ProductStorage> selectProductStorageList(ProductStorage productStorage) {
		return productStorageMapper.selectProductStorageList(productStorage);
	}

	/**
	 * 新增产品保质方式
	 *
	 * @param productStorage 产品保质方式
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertProductStorage(ProductStorage productStorage) {
		productStorage.insertInit(SecurityUtils.getLoginUser().getUsername());
		return productStorageMapper.insertProductStorage(productStorage);
	}

	/**
	 * 修改产品保质方式
	 *
	 * @param productStorage 产品保质方式
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateProductStorage(ProductStorage productStorage) {
		productStorage.updateInit(SecurityUtils.getLoginUser().getUsername());
		return productStorageMapper.updateProductStorage(productStorage);
	}

	/**
	 * 批量删除产品保质方式
	 *
	 * @param ids 需要删除的产品保质方式主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductStorageByIds(List<Long> ids) {
		return productStorageMapper.logicRemoveByIds(ids);
		//return productStorageMapper.deleteProductStorageByIds(ids);
	}

	/**
	 * 删除产品保质方式信息
	 *
	 * @param id 产品保质方式主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductStorageById(Long id) {
		return productStorageMapper.logicRemoveById(id);
		//return productStorageMapper.deleteProductStorageById(id);
	}

	@Override
	public List<ProductStorage> findListByProductId(String productId) {
		if(StringUtils.isBlank(productId)) {
			return null;
		}
		ProductStorage productStorageParam=new ProductStorage();
		productStorageParam.setProductId(productId);
		return selectProductStorageList(productStorageParam);
	}

}
