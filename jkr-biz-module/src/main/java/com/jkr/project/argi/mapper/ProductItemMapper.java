package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.ProductItem;
import org.apache.ibatis.annotations.Param;

/**
 * 产品子表(组合品使用)Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ProductItemMapper extends BaseMapper<ProductItem>{
	/**
	 * 查询产品子表(组合品使用)
	 *
	 * @param id 产品子表(组合品使用)主键
	 * @return 产品子表(组合品使用)
	 */
	public ProductItem selectProductItemById(Long id);

	/**
	 * 查询产品子表(组合品使用)列表
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 产品子表(组合品使用)集合
	 */
	public List<ProductItem> selectProductItemList(ProductItem productItem);

	/**
	 * 新增产品子表(组合品使用)
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 结果
	 */
	public int insertProductItem(ProductItem productItem);

	/**
	 * 修改产品子表(组合品使用)
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 结果
	 */
	public int updateProductItem(ProductItem productItem);

	/**
	 * 删除产品子表(组合品使用)
	 *
	 * @param id 产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int deleteProductItemById(Long id);

	/**
	 * 批量删除产品子表(组合品使用)
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteProductItemByIds(Long[] ids);

	/**
	 * 批量逻辑删除产品子表(组合品使用)
	 *
	 * @param  ids 产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过产品子表(组合品使用)主键id逻辑删除信息
	 *
	 * @param  id 产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public void batchInsert(@Param("productItemList") List<ProductItem> productItemList);

	public int deleteByProductId(@Param("productId") String productId);

	public int deleteBatchByProductId(@Param("list") List<String> list);
}
