package com.jkr.project.argi.service;

import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jkr.project.argi.domain.Product;
import com.jkr.project.argi.domain .ProductRecord;

/**
 * 产品检测记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IProductRecordService {
	/**
	 * 查询产品检测记录
	 *
	 * @param id 产品检测记录主键
	 * @return 产品检测记录
	 */
	public ProductRecord selectProductRecordById(Long id);

	/**
	 * 查询产品检测记录列表
	 *
	 * @param productRecord 产品检测记录
	 * @return 产品检测记录集合
	 */
	public List<ProductRecord> selectProductRecordList(ProductRecord productRecord);

	/**
	 * 新增产品检测记录
	 *
	 * @param productRecord 产品检测记录
	 * @return 结果
	 */
	public int insertProductRecord(ProductRecord productRecord);

	/**
	 * 修改产品检测记录
	 *
	 * @param productRecord 产品检测记录
	 * @return 结果
	 */
	public int updateProductRecord(ProductRecord productRecord);

	/**
	 * 批量删除产品检测记录
	 *
	 * @param ids 需要删除的产品检测记录主键集合
	 * @return 结果
	 */
	public int deleteProductRecordByIds(List<Long> ids);

	/**
	 * 删除产品检测记录信息
	 *
	 * @param id 产品检测记录主键
	 * @return 结果
	 */
	public int deleteProductRecordById(Long id);

	public ProductRecord getBySampleNo(String sampleNo);

	public String saveRecord(List<Product> productList);

	public JSONObject findInspectionResultListBySampleNo(String sampleNo);

	public JSONArray findInspectionQualifiedResultListBySampleNo(String sampleNo);

	public List<ProductRecord> findPageByProductId(ProductRecord productRecord);

	public List<ProductRecord> findListValidDetection(ProductRecord productRecord);

}
