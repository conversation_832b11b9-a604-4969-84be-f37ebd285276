package com.jkr.project.argi.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import com.jkr.common.utils.*;
import com.jkr.common.utils.ip.IpUtils;
import com.jkr.project.argi.domain.CertificateNo;
import com.jkr.project.argi.service.ICertificateNoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ScanRecordMapper;
import com.jkr.project.argi.domain.ScanRecord;
import com.jkr.project.argi.service.IScanRecordService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 扫描日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ScanRecordServiceImpl implements IScanRecordService {

	@Autowired
	private ScanRecordMapper scanRecordMapper;
	@Autowired
	private ICertificateNoService certificateNoService;

	/**
	 * 查询扫描日志
	 *
	 * @param id 扫描日志主键
	 * @return 扫描日志
	 */
	@Override
	public ScanRecord selectScanRecordById(Long id) {
		return scanRecordMapper.selectScanRecordById(id);
	}

	/**
	 * 查询扫描日志列表
	 *
	 * @param scanRecord 扫描日志
	 * @return 扫描日志
	 */
	@Override
	public List<ScanRecord> selectScanRecordList(ScanRecord scanRecord) {
		return scanRecordMapper.selectScanRecordList(scanRecord);
	}

	/**
	 * 新增扫描日志
	 *
	 * @param scanRecord 扫描日志
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertScanRecord(ScanRecord scanRecord) {
		scanRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		return scanRecordMapper.insertScanRecord(scanRecord);
	}

	/**
	 * 修改扫描日志
	 *
	 * @param scanRecord 扫描日志
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateScanRecord(ScanRecord scanRecord) {
		scanRecord.updateInit(SecurityUtils.getLoginUser().getUsername());
		return scanRecordMapper.updateScanRecord(scanRecord);
	}

	/**
	 * 批量删除扫描日志
	 *
	 * @param ids 需要删除的扫描日志主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteScanRecordByIds(List<Long> ids) {
		return scanRecordMapper.logicRemoveByIds(ids);
		//return scanRecordMapper.deleteScanRecordByIds(ids);
	}

	/**
	 * 删除扫描日志信息
	 *
	 * @param id 扫描日志主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteScanRecordById(Long id) {
		return scanRecordMapper.logicRemoveById(id);
		//return scanRecordMapper.deleteScanRecordById(id);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveScanRecord(String certificateId) {
		ScanRecord scanRecord = new ScanRecord();
		scanRecord.setCertificateId(certificateId);
		scanRecord.setScanDate(new Date());
		//获取ip
		String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
		//通过高德获取相关ip信息
		JSONObject jsonObject= GaoDeUtils.getIpV5(ip);
		if(null!=jsonObject && Global.YES.equals(jsonObject.getStr("status"))) {
			//省
			String province=jsonObject.getStr("province");
			scanRecord.setProvince(province);
			//市
			String city=jsonObject.getStr("city");
			scanRecord.setCity(city);
			//区
			String county=jsonObject.getStr("district");
			scanRecord.setCounty(county);
			//ip
			scanRecord.setIp(ip);
		}
		scanRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		scanRecordMapper.insertScanRecord(scanRecord);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public Map<String, Object> saveScanRecord(String certificateId, String fullNumber, String entId) {
		Map<String, Object> resMap = MapUtil.newHashMap();

		ScanRecord scanRecord = new ScanRecord();
		scanRecord.setCertificateId(certificateId);
		scanRecord.setScanDate(new Date());
		scanRecord.setEntId(entId);
		//获取ip
		String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
		//通过高德获取相关ip信息
		JSONObject jsonObject=GaoDeUtils.getIpV5(ip);
		if(null!=jsonObject && Global.YES.equals(jsonObject.getStr("status"))) {
			//省
			String province=jsonObject.getStr("province");
			scanRecord.setProvince(province);
			//市
			String city=jsonObject.getStr("city");
			scanRecord.setCity(city);
			//区
			String county=jsonObject.getStr("district");
			scanRecord.setCounty(county);
			//ip
			scanRecord.setIp(ip);
		}
		resMap.put("fullNumber", fullNumber);
		scanRecord.setFullNumber(fullNumber);
		scanRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		scanRecordMapper.insertScanRecord(scanRecord);

		resMap.put("scanRecordId", scanRecord.getId());
		resMap.put("certificateId", certificateId);
		resMap.put("scanDate", scanRecord.getScanDate());
		return resMap;
	}

	@Override
	public List<ScanRecord> findByCertificateId(String certificateId) {
		return scanRecordMapper.findByCertificateId(certificateId);
	}

	@Override
	public Map<String, Object> getCountAmountByCertificateId(String certificateId) {
		return scanRecordMapper.getCountAmountByCertificateId(certificateId);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveFullNumber(String fullNumber) {
		ScanRecord scanRecord = new ScanRecord();
		scanRecord.setFullNumber(fullNumber);
		scanRecord.setScanDate(new Date());
		//获取ip
		String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
		//通过高德获取相关ip信息
		JSONObject jsonObject=GaoDeUtils.getIpV5(ip);
		if(null!=jsonObject && Global.YES.equals(jsonObject.getStr("status"))) {
			//省
			String province=jsonObject.getStr("province");
			scanRecord.setProvince(province);
			//市
			String city=jsonObject.getStr("city");
			scanRecord.setCity(city);
			//区
			String county=jsonObject.getStr("district");
			scanRecord.setCounty(county);
			//ip
			scanRecord.setIp(ip);
		}
		scanRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		scanRecordMapper.insertScanRecord(scanRecord);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public Map<String, Object> saveFullNumber(String fullNumber, String endId) {
		Map<String, Object> resMap = MapUtil.newHashMap();

		ScanRecord scanRecord = new ScanRecord();
		scanRecord.setFullNumber(fullNumber);
		scanRecord.setScanDate(new Date());
		scanRecord.setEntId(endId);
		//获取ip
		String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
		//通过高德获取相关ip信息
		JSONObject jsonObject=GaoDeUtils.getIpV5(ip);
		if(null!=jsonObject && Global.YES.equals(jsonObject.getStr("status"))) {
			//省
			String province=jsonObject.getStr("province");
			scanRecord.setProvince(province);
			//市
			String city=jsonObject.getStr("city");
			scanRecord.setCity(city);
			//区
			String county=jsonObject.getStr("district");
			scanRecord.setCounty(county);
			//ip
			scanRecord.setIp(ip);
		}
		CertificateNo certificateNo = certificateNoService.getByFullNumber(fullNumber);
		if (null != certificateNo) {
			resMap.put("certificateId", certificateNo.getCertificateId());
			scanRecord.setCertificateId(certificateNo.getCertificateId());
		} else {
			resMap.put("certificateId", "");
		}
		scanRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		scanRecordMapper.insertScanRecord(scanRecord);

		resMap.put("scanRecordId", scanRecord.getId());
		resMap.put("fullNumber", fullNumber);
		resMap.put("scanDate", scanRecord.getScanDate());
		return resMap;
	}

	@Override
	public List<ScanRecord> findByFullNumber(String fullNumber) {
		return scanRecordMapper.findByFullNumber(fullNumber);
	}

	@Override
	public List<ScanRecord> findTraceStatisticList(ScanRecord scanRecord) {
		return scanRecordMapper.findTraceStatisticList(scanRecord);
	}

	@Override
	public List<ScanRecord> findEntScanPage(ScanRecord scanRecord) {
		return scanRecordMapper.findEntScanList(scanRecord);
	}

}
