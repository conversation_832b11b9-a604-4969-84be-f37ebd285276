package com.jkr.project.argi.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.jkr.project.argi.domain .Certificate;
import com.jkr.project.argi.domain.CertificateNo;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.domain.Product;

/**
 * 合格证Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateService {
	/**
	 * 查询合格证
	 *
	 * @param id 合格证主键
	 * @return 合格证
	 */
	public Certificate selectCertificateById(Long id);

	/**
	 * 查询合格证列表
	 *
	 * @param certificate 合格证
	 * @return 合格证集合
	 */
	public List<Certificate> selectCertificateList(Certificate certificate);

	/**
	 * 新增合格证
	 *
	 * @param certificate 合格证
	 * @return 结果
	 */
	public int insertCertificate(Certificate certificate);

	/**
	 * 修改合格证
	 *
	 * @param certificate 合格证
	 * @return 结果
	 */
	public int updateCertificate(Certificate certificate);

	/**
	 * 批量删除合格证
	 *
	 * @param ids 需要删除的合格证主键集合
	 * @return 结果
	 */
	public int deleteCertificateByIds(List<Long> ids);

	/**
	 * 删除合格证信息
	 *
	 * @param id 合格证主键
	 * @return 结果
	 */
	public int deleteCertificateById(Long id);

	public Certificate getForElectronicShow(String id);

	public List<Certificate> findPageForWechat(Certificate certificate);

	public void updatePrintCount(String id, String printCountNew);

	public void joinBlockChain(String id);

	public void updateBlockChainId(String id,String blockChainId);

	public void insertAll(Certificate certificate);

	public void batchGenerationSerialNumber(Certificate certificate, Ent ent, Product product);

	public void batchGenerationSerialNumber(Certificate certificate);

	public void joinBlockChainCertificateNo(String id,List<CertificateNo> list);

	public void dataCentrePush(List<CertificateNo> certificateNoList,Certificate certificate);

	public void dataCentrePushDo(List<CertificateNo> certificateNoList, Certificate certificate);

	public Integer findPrintTotalNum(Certificate certificate);

	public Map<String, Object> getSummaryInfo(String entId);

	List<Certificate> incrementalQuery(Date date);
}
