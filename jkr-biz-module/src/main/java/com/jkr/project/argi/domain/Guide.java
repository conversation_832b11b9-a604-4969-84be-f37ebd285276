package com.jkr.project.argi.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 农业技术指导对象 bas_guide
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_guide")
public class Guide extends BaseModel {
    private static final long serialVersionUID = 1L;



    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String label;

    /**
     * 技术类型
     */
    @Excel(name = "技术类型")
    private String businessType;

    /**
     * 技术类型字典code
     */
    private String businessTypeCode;

    /**
     * 来源
     */
    @Excel(name = "来源")
    private String source;

    /**
     * 内容
     */
    @Excel(name = "内容")
    private String content;

    /**
     * 编辑时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "编辑时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date editDate;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /**
     * 状态值 0-未发布 1-已发布 2-已下架
     */
    private String status;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;

    /**
     * 开始时间
     */
    @TableField(exist = false)
    private String beginTime;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    private String endTime;

    /**
     * 上传图片集合
     */
    @TableField(exist = false)
    private List<SysFile> coverImageFiles;
}
