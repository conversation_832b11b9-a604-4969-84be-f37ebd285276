package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .ProductStorage;

/**
 * 产品保质方式Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IProductStorageService {
	/**
	 * 查询产品保质方式
	 *
	 * @param id 产品保质方式主键
	 * @return 产品保质方式
	 */
	public ProductStorage selectProductStorageById(Long id);

	/**
	 * 查询产品保质方式列表
	 *
	 * @param productStorage 产品保质方式
	 * @return 产品保质方式集合
	 */
	public List<ProductStorage> selectProductStorageList(ProductStorage productStorage);

	/**
	 * 新增产品保质方式
	 *
	 * @param productStorage 产品保质方式
	 * @return 结果
	 */
	public int insertProductStorage(ProductStorage productStorage);

	/**
	 * 修改产品保质方式
	 *
	 * @param productStorage 产品保质方式
	 * @return 结果
	 */
	public int updateProductStorage(ProductStorage productStorage);

	/**
	 * 批量删除产品保质方式
	 *
	 * @param ids 需要删除的产品保质方式主键集合
	 * @return 结果
	 */
	public int deleteProductStorageByIds(List<Long> ids);

	/**
	 * 删除产品保质方式信息
	 *
	 * @param id 产品保质方式主键
	 * @return 结果
	 */
	public int deleteProductStorageById(Long id);

	public List<ProductStorage> findListByProductId(String productId);

}
