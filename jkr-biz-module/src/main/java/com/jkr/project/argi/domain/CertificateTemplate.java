package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 合格证模板对象 bas_certificate_template
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_certificate_template")
public class CertificateTemplate extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 模板编号
     */
    @Excel(name = "模板编号")
    private Integer code;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 图片url
     */
    @Excel(name = "图片url")
    private String imageUrl;

    /**
     * 是否用于乡村版(1是 0否)
     */
    @Excel(name = "是否用于乡村版(1是 0否)")
    private String showVillage;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;

    /**
     * 文件
     */
    @TableField(exist = false)
    private List<SysFile> imageFiles;
}
