package com.jkr.project.argi.task;

import com.jkr.project.argi.constant.PublishStatusConstants;
import com.jkr.project.argi.domain.BasNotice;
import com.jkr.project.argi.mapper.BasNoticeMapper;
import com.jkr.project.argi.service.DelayedPublishService;
import com.jkr.project.argi.service.IBasNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Notice发布状态处理器
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Component
@Slf4j
public class NoticePublishHandler {

    private static final String NOTICE_PUBLISH_QUEUE = "notice:publish:queue";

    @Autowired
    private DelayedPublishService delayedPublishService;

    @Lazy
    @Autowired
    private IBasNoticeService basNoticeService;

    @Autowired
    private BasNoticeMapper basNoticeMapper;

    /**
     * 添加Notice到发布队列
     *
     * @param notice Notice对象
     */
    public void addToPublishQueue(BasNotice notice) {
        if (notice != null && notice.getPublishTime() != null) {
            delayedPublishService.addToQueue(NOTICE_PUBLISH_QUEUE, notice.getId(), notice.getPublishTime());
        }
    }

    /**
     * 从发布队列中移除Notice
     *
     * @param noticeId Notice ID
     */
    public void removeFromPublishQueue(Long noticeId) {
        delayedPublishService.removeFromQueue(NOTICE_PUBLISH_QUEUE, noticeId);
    }

    /**
     * 定时检查并处理待发布的Notice
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void handlePublishQueue() {
        delayedPublishService.processQueue(NOTICE_PUBLISH_QUEUE, this::publishNotice);
    }

    /**
     * 发布Notice的具体逻辑
     *
     * @param noticeId Notice ID
     */
    private void publishNotice(Long noticeId) {
        BasNotice notice = new BasNotice();
        notice.setId(noticeId);
        notice.setStatus(PublishStatusConstants.STATUS_PUBLISHED);
        basNoticeMapper.updateById(notice);
    }
}
