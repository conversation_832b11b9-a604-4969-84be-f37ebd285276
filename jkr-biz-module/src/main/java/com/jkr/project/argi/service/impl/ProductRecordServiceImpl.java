package com.jkr.project.argi.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.Md5Util;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.domain.Product;
import com.jkr.project.argi.service.IEntService;
import com.jkr.project.argi.service.IProductSampleService;
import com.jkr.project.argi.service.IProductService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ProductRecordMapper;
import com.jkr.project.argi.domain.ProductRecord;
import com.jkr.project.argi.service.IProductRecordService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

/**
 * 产品检测记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ProductRecordServiceImpl implements IProductRecordService {

	private static final String successCode = "0";
	@Autowired
	private ProductRecordMapper productRecordMapper;
	@Autowired
	private IProductService productService;
	@Autowired
	private IEntService entService;
	@Autowired
	private IProductSampleService productSampleService;
	@Value("${inspectionEnabled}")
	private Boolean inspectionEnabled;
	@Value("${inspection.url}")
	private String inspectionUrl;
	@Value("${inspection.saveSampleUrl}")
	private String inspectionSaveSampleUrl;
	@Value("${inspection.AccessKey}")
	private static String inspectionAccessKey;
	@Value("${inspection.SecretKey}")
	private static String inspectionSecretKey;
	@Value("${inspection.queryBySampleNoUrl}")
	private String inspectionQueryBySampleNoUrl;
	@Value("${inspection.queryQualifiedResultBySampleUrl}")
	private String inspectionQueryQualifiedResultBySampleUrl;

	/**
	 * 查询产品检测记录
	 *
	 * @param id 产品检测记录主键
	 * @return 产品检测记录
	 */
	@Override
	public ProductRecord selectProductRecordById(Long id) {
		return productRecordMapper.selectProductRecordById(id);
	}

	/**
	 * 查询产品检测记录列表
	 *
	 * @param productRecord 产品检测记录
	 * @return 产品检测记录
	 */
	@Override
	public List<ProductRecord> selectProductRecordList(ProductRecord productRecord) {
		return productRecordMapper.selectProductRecordList(productRecord);
	}

	/**
	 * 新增产品检测记录
	 *
	 * @param productRecord 产品检测记录
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertProductRecord(ProductRecord productRecord) {
		productRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		return productRecordMapper.insertProductRecord(productRecord);
	}

	/**
	 * 修改产品检测记录
	 *
	 * @param productRecord 产品检测记录
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateProductRecord(ProductRecord productRecord) {
		productRecord.updateInit(SecurityUtils.getLoginUser().getUsername());
		return productRecordMapper.updateProductRecord(productRecord);
	}

	/**
	 * 批量删除产品检测记录
	 *
	 * @param ids 需要删除的产品检测记录主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductRecordByIds(List<Long> ids) {
		return productRecordMapper.logicRemoveByIds(ids);
		//return productRecordMapper.deleteProductRecordByIds(ids);
	}

	/**
	 * 删除产品检测记录信息
	 *
	 * @param id 产品检测记录主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductRecordById(Long id) {
		return productRecordMapper.logicRemoveById(id);
		//return productRecordMapper.deleteProductRecordById(id);
	}

	@Override
	public ProductRecord getBySampleNo(String sampleNo) {
		if(StringUtils.isBlank(sampleNo)) {
			return null;
		}
		return productRecordMapper.getBySampleNo(sampleNo);
	}

	/**
	 *
	 * @Title: saveRecord
	 * @author: LJX
	 * @date: 2020年9月10日 下午1:16:33
	 * @Description: 保存样品检测记录
	 * @param:  productList
	 * @throws
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public String saveRecord(List<Product> productList) {
		if (!inspectionEnabled) {
			return "保存失败：接口开关设置为false";
		}
		try {
			RestTemplate restTemplate =new RestTemplate();
			HttpHeaders requestHeaders = getRequestHeader();
			JSONArray jsonArray = new JSONArray();
			Ent ent=entService.selectEntById(SecurityUtils.getDeptId());
			//个人使用姓名;企业使用法人;
			String testMan=ent.getEntType().equals(EnumProperty.EntTypeEnum.ENTERPRISE.getKey())?ent.getLegalPerson():ent.getName();
			for(Product product: productList) {
				JSONObject temp = new JSONObject();
				temp.put("sampleId", product.getId());
				temp.put("sampleName", product.getName());
				temp.put("testMan", testMan);
				/**
				 * 接口变动，新增接口内容
				 * 2021年3月8日13:22:39
				 * lxy
				 */
				temp.put("entId", ent.getId());
				temp.put("sampleOneCode", product.getProductSortCode());
				temp.put("sampleOneLabel", product.getProductSortName());
				jsonArray.add(temp);
			};

			Map<String, Object> requestBody = new HashMap<>();
			requestBody.put("data",jsonArray);

			/**
			 * 接口变动，新增接口内容:企业信息
			 * 2021年3月8日13:22:39
			 * lxy
			 */
			JSONObject entJson = new JSONObject();
			entJson.put("id", ent.getId());
			entJson.put("name", ent.getName());
			entJson.put("legalPerson", ent.getLegalPerson());
			entJson.put("telephone", ent.getContactsPhone());
			entJson.put("province", ent.getProvince());
			entJson.put("city", ent.getCity());
			entJson.put("district", ent.getCounty());
			entJson.put("address", ent.getAddress());
			entJson.put("detail", ent.getDetail());
			entJson.put("lat", ent.getLat());
			entJson.put("lng", ent.getLng());
			entJson.put("code", ent.getSocialCode());
			entJson.put("idCard", ent.getCardNo());
			entJson.put("entTypeCode", "");
			entJson.put("entTypeLabel", "");
			entJson.put("updateDate", DateUtils.formatDate(ent.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
			requestBody.put("ent",entJson);

			/**
			 * 接口变动，新增接口内容:用户信息
			 * 2021年3月8日13:22:39
			 * lxy
			 * 2021年4月25日17:08:00
			 * 因本业务系统没有用户数据，但对接系统需要，和对接开发人员沟通后填写其他数据补充
			 */
			JSONObject userJson = new JSONObject();
			userJson.put("id", ent.getId());
			userJson.put("officeId", ent.getId());
			userJson.put("name", ent.getName());
			userJson.put("loginName", ent.getContactsPhone());
			userJson.put("mobile", ent.getContactsPhone());
			userJson.put("updateDate", DateUtils.formatDate(ent.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
			requestBody.put("user",userJson);

			HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, requestHeaders);
			JSONObject result = restTemplate.postForObject(inspectionUrl+inspectionSaveSampleUrl, requestEntity, JSONObject.class);
			if(successCode.equals(result.getString("code"))){
				JSONArray data=result.getJSONArray("data");
				if( null != data && data.size()>0) {
					for(int i = 0; i < data.size(); i++){
						JSONObject sample = data.getJSONObject(i);
						ProductRecord productRecord= new ProductRecord();
						productRecord.setProductId(sample.getString("sampleId"));
						productRecord.setSampleName(sample.getString("sampleName"));
						productRecord.setSampleNo(sample.getString("sampleNo"));
						productRecord.setQueryCodeUrl(sample.getString("qrcodeUrl"));
						productRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
						productRecordMapper.insertProductRecord(productRecord);
						/**
						 * 更新检测样品信息
						 * 2021年1月18日15:20:37
						 * lxy
						 */
						productSampleService.updateNewestQuickSample(ent.getId()+"", productRecord.getProductId(), productRecord.getSampleNo());
					}
					productService.updateInspectionResultById(data);
				}
				return successCode;
			}else {
				return result.getString("message");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return "服务连接失败";
		}
	}

	/**
	 *
	 * @Title: findInspectionResultListBySampleNo
	 * @author: LJX
	 * @date: 2020年9月10日 下午3:52:18
	 * @Description: 根据样品编号获取检测结果
	 * @param:  sampleNo
	 * @return: void
	 * @throws
	 */
	@Override
	public JSONObject findInspectionResultListBySampleNo(String sampleNo) {
		if (!inspectionEnabled) {
			return new JSONObject();
		}
		try {
			RestTemplate restTemplate =new RestTemplate();
			HttpHeaders requestHeaders = getRequestHeader();
			JSONArray jsonArray = new JSONArray();
			JSONObject temp = new JSONObject();
			temp.put("sampleNo",sampleNo);
			jsonArray.add(temp);
			Map<String, Object> requestBody = new HashMap<>();
			requestBody.put("data",jsonArray);
			HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, requestHeaders);
			JSONObject result = restTemplate.postForObject(inspectionUrl+inspectionQueryBySampleNoUrl, requestEntity, JSONObject.class);
			return result;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new JSONObject();
	}

	/**
	 *
	 * @Title: findInspectionQualifiedResultListBySampleNo
	 * @author: LJX
	 * @date: 2020年9月17日 下午3:07:06
	 * @Description: 根据样品标号获取检测合格结果
	 * @param:  sampleNo
	 * @return: JSONArray
	 * @throws
	 */
	@Override
	public JSONArray findInspectionQualifiedResultListBySampleNo(String sampleNo) {
		if (!inspectionEnabled || StringUtils.isEmpty(sampleNo)) {
			return new JSONArray();
		}
		try {
			RestTemplate restTemplate =new RestTemplate();
			HttpHeaders requestHeaders = getRequestHeader();
			JSONArray jsonArray = new JSONArray();
			JSONObject temp = new JSONObject();
			temp.put("sampleNo",sampleNo);
			jsonArray.add(temp);
			Map<String, Object> requestBody = new HashMap<>();
			requestBody.put("data",jsonArray);
			HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, requestHeaders);
			JSONObject result = restTemplate.postForObject(inspectionUrl+inspectionQueryQualifiedResultBySampleUrl, requestEntity, JSONObject.class);
			if(successCode.equals(result.getString("code"))){
				return result.getJSONArray("data");
			}
			return new JSONArray();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return new JSONArray();
	}

	@Override
	public List<ProductRecord> findPageByProductId(ProductRecord productRecord) {
		return productRecordMapper.findListByProductId(productRecord);
	}

	@Override
	public List<ProductRecord> findListValidDetection(ProductRecord productRecord) {
		return productRecordMapper.findListValidDetection(productRecord);
	}

	/**
	 *
	 * @Title: getRequestHeader
	 * @author: LJX
	 * @date: 2020年9月10日 下午1:54:21
	 * @Description: 获取请求头
	 * @return: HttpHeaders
	 * @throws
	 */
	private static HttpHeaders getRequestHeader() {
		String AccessKey = inspectionAccessKey;
		String SecretKey = inspectionSecretKey;
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("Content-Type", "multipart/form-data;charset=utf8");
		headers.add("Authorization", Md5Util.md5(SecretKey+ DateFormatUtils.format(new Date(), "yyyy-MM-dd")));
		headers.add("Distinguish-Id", AccessKey);
		return headers;
	}

}
