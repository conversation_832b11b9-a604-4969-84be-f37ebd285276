package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .ProductItem;

/**
 * 产品子表(组合品使用)Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IProductItemService {
	/**
	 * 查询产品子表(组合品使用)
	 *
	 * @param id 产品子表(组合品使用)主键
	 * @return 产品子表(组合品使用)
	 */
	public ProductItem selectProductItemById(Long id);

	/**
	 * 查询产品子表(组合品使用)列表
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 产品子表(组合品使用)集合
	 */
	public List<ProductItem> selectProductItemList(ProductItem productItem);

	/**
	 * 新增产品子表(组合品使用)
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 结果
	 */
	public int insertProductItem(ProductItem productItem);

	/**
	 * 修改产品子表(组合品使用)
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 结果
	 */
	public int updateProductItem(ProductItem productItem);

	/**
	 * 批量删除产品子表(组合品使用)
	 *
	 * @param ids 需要删除的产品子表(组合品使用)主键集合
	 * @return 结果
	 */
	public int deleteProductItemByIds(List<Long> ids);

	/**
	 * 删除产品子表(组合品使用)信息
	 *
	 * @param id 产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int deleteProductItemById(Long id);

	public List<ProductItem> findListByProductId(String productId);

	public void batchInsert(List<ProductItem> productItemList);

	public void deleteByProductId(String id);

	public void deleteBatchByProductId(List<String> list);

}
