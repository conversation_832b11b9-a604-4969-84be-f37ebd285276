package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Feedback;
import com.jkr.project.argi.service.IFeedbackService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 反馈信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/feedback")
public class FeedbackController extends BaseController {
	@Autowired
	private IFeedbackService feedbackService;

/**
 * 查询反馈信息列表
 */
@PreAuthorize("@ss.hasPermi('argi:feedback:list')")
@GetMapping("/list")
	public TableDataInfo list(Feedback feedback) {
		startPage();
		List<Feedback> list = feedbackService.selectFeedbackList(feedback);
		return getDataTable(list);
	}

	/**
	 * 导出反馈信息列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:feedback:export')")
	@Log(title = "导出反馈信息列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, Feedback feedback) {
		List<Feedback> list = feedbackService.selectFeedbackList(feedback);
		ExcelUtil<Feedback> util = new ExcelUtil<Feedback>(Feedback. class);
		util.exportExcel(response, list, "反馈信息数据");
	}

	/**
	 * 获取反馈信息详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:feedback:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(feedbackService.selectFeedbackById(id));
	}

	/**
	 * 新增反馈信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:feedback:add')")
	@Log(title = "新增反馈信息", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody Feedback feedback) {
		return toAjax(feedbackService.insertFeedback(feedback));
	}

	/**
	 * 修改反馈信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:feedback:edit')")
	@Log(title = "修改反馈信息", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody Feedback feedback) {
		return toAjax(feedbackService.updateFeedback(feedback));
	}

	/**
	 * 删除反馈信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:feedback:remove')")
	@Log(title = "删除反馈信息", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(feedbackService.deleteFeedbackById(id));
	}

	/**
	 * 批量删除反馈信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:feedback:batchRemove')")
	@Log(title = "批量删除反馈信息", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody Feedback feedback) {
		return toAjax(feedbackService.deleteFeedbackByIds(feedback.getIds()));
	}
}
