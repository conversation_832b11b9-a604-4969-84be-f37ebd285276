package com.jkr.project.argi.controller;

import java.util.Date;
import java.util.List;

import com.jkr.common.utils.PageUtils;
import com.jkr.project.argi.service.IEntService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Product;
import com.jkr.project.argi.service.IProductService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 产品Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/product")
public class ProductController extends BaseController {

    @Autowired
    private IProductService productService;
    @Autowired
    private IEntService entService;

    /**
     * 查询产品列表
     */
    @PreAuthorize("@ss.hasPermi('argi:product:list')")
    @GetMapping("/list")
    public TableDataInfo list(Product product) {
        startPage();
        List<Product> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    @PostMapping("/findProductPage")
    public TableDataInfo findProductPage(@RequestBody Product product){
        PageUtils.startPage(product.getPageNum(), product.getPageSize());
        List<Product> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    @PostMapping("/findProductAttachment")
    public AjaxResult findProductAttachment(@RequestBody Product product){
        product = productService.findProductAttachment(product);
        return AjaxResult.success(product);
    }

    /**
     * 导出产品列表
     */
    @PreAuthorize("@ss.hasPermi('argi:product:export')")
    @Log(title = "导出产品列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Product product) {
        List<Product> list = productService.selectProductList(product);
        ExcelUtil<Product> util = new ExcelUtil<Product>(Product.class);
        util.exportExcel(response, list, "产品数据");
    }

    /**
     * 获取产品详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:product:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productService.selectProductById(id));
    }

    /**
     * 新增产品
     */
    @PreAuthorize("@ss.hasPermi('argi:product:add')")
    @Log(title = "新增产品", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody Product product) {
        return toAjax(productService.insertProduct(product));
    }

    /**
     * 修改产品
     */
    @PreAuthorize("@ss.hasPermi('argi:product:edit')")
    @Log(title = "修改产品", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody Product product) {
        return toAjax(productService.updateProduct(product));
    }

    /**
     * 删除产品
     */
    @PreAuthorize("@ss.hasPermi('argi:product:remove')")
    @Log(title = "删除产品", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(productService.deleteProductById(id));
    }

    /**
     * 批量删除产品
     */
    @PreAuthorize("@ss.hasPermi('argi:product:batchRemove')")
    @Log(title = "批量删除产品", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody Product product) {
        return toAjax(productService.deleteProductByIds(product.getIds()));
    }


    //获取同步数据
    @GetMapping(value = "/getSyncData")
    public AjaxResult getSyncData(Date date) {
        return success(productService.getSyncData(date));
    }
}
