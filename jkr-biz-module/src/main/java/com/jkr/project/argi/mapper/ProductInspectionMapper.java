package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.ProductInspection;
import org.apache.ibatis.annotations.Param;

/**
 * 产品检测Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ProductInspectionMapper extends BaseMapper<ProductInspection>{
	/**
	 * 查询产品检测
	 *
	 * @param id 产品检测主键
	 * @return 产品检测
	 */
	public ProductInspection selectProductInspectionById(Long id);

	/**
	 * 查询产品检测列表
	 *
	 * @param productInspection 产品检测
	 * @return 产品检测集合
	 */
	public List<ProductInspection> selectProductInspectionList(ProductInspection productInspection);

	/**
	 * 新增产品检测
	 *
	 * @param productInspection 产品检测
	 * @return 结果
	 */
	public int insertProductInspection(ProductInspection productInspection);

	/**
	 * 修改产品检测
	 *
	 * @param productInspection 产品检测
	 * @return 结果
	 */
	public int updateProductInspection(ProductInspection productInspection);

	/**
	 * 删除产品检测
	 *
	 * @param id 产品检测主键
	 * @return 结果
	 */
	public int deleteProductInspectionById(Long id);

	/**
	 * 批量删除产品检测
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteProductInspectionByIds(Long[] ids);

	/**
	 * 批量逻辑删除产品检测
	 *
	 * @param  ids 产品检测主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过产品检测主键id逻辑删除信息
	 *
	 * @param  id 产品检测主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public ProductInspection getNewest(ProductInspection productInspection);

	public int insertBatch(@Param("list")List<ProductInspection> list);
}
