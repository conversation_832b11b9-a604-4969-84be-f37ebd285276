package com.jkr.project.argi.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.EntDetailMapper;
import com.jkr.project.argi.domain.EntDetail;
import com.jkr.project.argi.service.IEntDetailService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主体信息子表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class EntDetailServiceImpl extends ServiceImpl<EntDetailMapper, EntDetail>  implements IEntDetailService {
	@Autowired
	private EntDetailMapper entDetailMapper;

	/**
	 * 查询主体信息子表
	 *
	 * @param id 主体信息子表主键
	 * @return 主体信息子表
	 */
	@Override
	public EntDetail selectEntDetailById(Long id) {
		return entDetailMapper.selectEntDetailById(id);
	}

	/**
	 * 查询主体信息子表列表
	 *
	 * @param entDetail 主体信息子表
	 * @return 主体信息子表
	 */
	@Override
	public List<EntDetail> selectEntDetailList(EntDetail entDetail) {
		return entDetailMapper.selectEntDetailList(entDetail);
	}

	/**
	 * 新增主体信息子表
	 *
	 * @param entDetail 主体信息子表
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertEntDetail(EntDetail entDetail) {
		entDetail.insertInit(SecurityUtils.getLoginUser().getUsername());

			return entDetailMapper.insertEntDetail(entDetail);
	}

	/**
	 * 修改主体信息子表
	 *
	 * @param entDetail 主体信息子表
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateEntDetail(EntDetail entDetail) {
		entDetail.updateInit(SecurityUtils.getLoginUser().getUsername());

		return entDetailMapper.updateEntDetail(entDetail);
	}

	/**
	 * 批量删除主体信息子表
	 *
	 * @param ids 需要删除的主体信息子表主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteEntDetailByIds(List<Long> ids) {
		return entDetailMapper.logicRemoveByIds(ids);
		//return entDetailMapper.deleteEntDetailByIds(ids);
	}

	/**
	 * 删除主体信息子表信息
	 *
	 * @param id 主体信息子表主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteEntDetailById(Long id) {
		return entDetailMapper.logicRemoveById(id);
		//return entDetailMapper.deleteEntDetailById(id);
	}

	/**
	 *
	 * @title: deleteByEntId
	 * @author: wanghe
	 * @date: 2021年9月15日 下午8:40:06
	 * @param:  entity
	 * @description: 删除
	 * @return: int
	 * @throws
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void  deleteByEntId(String entId){
		entDetailMapper.deleteByEntId(entId);
	}
	/**
	 *
	 * @title: insertBatch
	 * @author: wanghe
	 * @date: 2021年9月14日 下午8:40:06
	 * @param:  entity
	 * @description: 批量保存
	 * @return: int
	 * @throws
	 */
	@Override
	@Transactional(readOnly = false)
	public void saveEntBatch(List<EntDetail> entDetailList, String entId) {
		List<EntDetail> list = new ArrayList<>();
		//判断这几个值是否为空 否则不进库 都是非必填
		entDetailList.forEach(a -> {
			if (StringUtils.isNotBlank(a.getProductId()) || null != a.getScale()
					|| null != a.getAnnualOutput() || null != a.getAnnualValue()
			) {
				a.insertInit(SecurityUtils.getUsername());
				a.setEntId(entId);
				list.add(a);
			}
		});
		if(CollectionUtils.isNotEmpty(list)){
			this.saveBatch(list);
//			this.dao.insertBatch(list);
		}
	}

	/**
	 *
	 * @title: findDetailList
	 * @author: wanghe
	 * @date: 2021年9月15日 上午15:20:11
	 * @param:  id
	 * @description: 查询子表
	 * @return: ResponseResult
	 * @throws
	 */
	public List<EntDetail> findDetailList(Long entId){
		return baseMapper.findDetailList(entId);
	}
}
