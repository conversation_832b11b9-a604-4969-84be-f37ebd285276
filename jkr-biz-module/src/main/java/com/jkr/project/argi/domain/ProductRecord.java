package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 产品检测记录对象 bas_product_record
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_product_record")
public class ProductRecord extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    @Excel(name = "产品id")
    private String productId;

    /**
     * 样品名称
     */
    @Excel(name = "样品名称")
    private String sampleName;

    /**
     * 样品编号
     */
    @Excel(name = "样品编号")
    private String sampleNo;

    /**
     * 二维码url
     */
    @Excel(name = "二维码url")
    private String queryCodeUrl;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private List<Detection> detectionList;// 检测信息list
    @TableField(exist = false)
    private String startYearMonth;//开始年月
    @TableField(exist = false)
    private Product product;//关联产品对象
}
