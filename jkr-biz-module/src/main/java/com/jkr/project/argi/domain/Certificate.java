package com.jkr.project.argi.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 合格证对象 bas_certificate
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_certificate")
public class Certificate extends BaseModel {
    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开具日期", sort = 9, width = 30, dateFormat = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 合格证批次号
     */
    private String batchNo;

    /**
     * 用户id（乡村版合格证用）
     */
    private String userId;

    /**
     * 数据来源（0：正常合格证系统 1：乡村版合格证数据）
     */
    private String dataScope;

    /**
     * 流水号
     */
    private String no;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称", sort = 6)
    private String productName;

    /**
     * 组合品标识
     */
    @Excel(name = "产品类型", sort = 4)
    private String productMixFlag;

    /**
     * $column.columnComment
     */
    private String productIntroduction;

    /**
     * 省
     */
    private String productProvince;

    /**
     * 市
     */
    private String productCity;

    /**
     * 县
     */
    private String productCounty;

    /**
     * 产品生产地址(省市县)
     */
    private String productAddress;

    /**
     * 产品生产详细地址
     */
    private String productDetail;

    /**
     * 分类code
     */
    private String productSortCode;

    /**
     * 分类名称
     */
    @Excel(name = "产品分类", sort = 5)
    private String productSortName;

    /**
     * 产品认证（有机农产品，绿色食品，无公害农产品，农产品地理标志）
     */
    private String productCertificationCode;

    /**
     * 产品认证名称
     */
    private String productCertificationName;

    /**
     * 产品数量（重量）
     */
    private String productNum;

    /**
     * 产品单位code
     */
    private String productUnitCode;

    /**
     * 产品单位名称
     */
    private String productUnitName;

    /**
     * 产品生产主体id
     */
    private String entId;

    /**
     * 产品生产主体名称
     */
    @Excel(name = "生产经营主体名称", sort = 1)
    private String entName;

    /**
     * 主体类型(0：种植;1:养殖)
     */
    @Excel(name = "主体类型", sort = 2)
    private String entBusinessType;

    /**
     * 主体性质(0:企业；1：个人)
     */
    @Excel(name = "主体性质", sort = 3)
    private String entType;

    /**
     * 主体类别
     */
    private String entMainType;

    /**
     * 养殖分类 (0:牧业 1:渔业)
     */
    private String entFarmType;

    /**
     * 身份证号
     */
    private String entCardNo;

    /**
     * 法人
     */
    private String entLegalPerson;

    /**
     * 主体-省份
     */
    private String entProvince;

    /**
     * 主体-城市
     */
    private String entCity;

    /**
     * 主体-县区
     */
    private String entCounty;

    /**
     * 地址(省市县)
     */
    @Excel(name = "所在区域", sort = 11)
    private String entAddress;

    /**
     * 详细地址
     */
    private String entDetail;

    /**
     * 主体-统一社会信用代码
     */
    private String entSocialCode;

    /**
     * 主体-联系电话
     */
    private String entContactsPhone;

    /**
     * 签名base64
     */
    private String entAutograph;

    /**
     * 主体-简介
     */
    private String entCompanyIntroduction;

    /**
     * $column.columnComment
     */
    private String entHonor;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productionDate;

    /**
     * 打印数量
     */
    @Excel(name = "开具数量", sort = 10)
    private String printCount;

    /**
     * 区块链id
     */
    private String blockChainId;

    /**
     * 开始流水号
     */
    private String beginSerialNumber;

    /**
     * 结束流水号
     */
    private String endSerialNumber;

    /**
     * 检测情况
     */
    private String inspectionSituation;

    /**
     * 当前产品样品编号
     */
    private String sampleNo;

    /**
     * 产品检测表id
     */
    private String productInspectionId;

    /**
     * 复购按钮是否显示1显示,其他不显示
     */
    private String reBuyVisible;

    /**
     * 电子证开具标识：0-未开具电子证;1-已开具电子证
     */
    @Excel(name = "开具方式", sort = 7)
    private String electricFlag;

    /**
     * 合格证类型：0-生产证;1-收购证
     */
    @Excel(name = "开具类型", sort = 8)
    private String certificateType;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private String entCode; //主体行政区划
    @TableField(exist = false)
    private Date beginCreateDate;		// 开始 创建时间
    @TableField(exist = false)
    private Date endCreateDate;		// 结束 创建时间
    @TableField(exist = false)
    private String time;
    @TableField(exist = false)
    private Product product; //产品
    @TableField(exist = false)
    private Ent ent; // 实体
    @TableField(exist = false)
    private String sortName;//排序字段名称
    @TableField(exist = false)
    private String sortOrder;//字段排序
    @TableField(exist = false)
    private String tableId;//ent 表tableID
    @TableField(exist = false)
    private List<CertificateNo> certificateNoList;
    @TableField(exist = false)
    private String searchType; //检索类型
    @TableField(exist = false)
    private String certificateNoId;//合格证流水id
    @TableField(exist = false)
    private List<SysFile> sealPicList; //盖章
    @TableField(exist = false)
    private Autograph autograph;//签字信息
    @TableField(exist = false)
    private List<String> inspectionSituationList; //检测类型集合
    @TableField(exist = false)
    private List<CertificateProductItem> certificateProductItemList;
    @TableField(exist = false)
    private Integer beginPrintCount;
    @TableField(exist = false)
    private Integer endPrintCount;
}
