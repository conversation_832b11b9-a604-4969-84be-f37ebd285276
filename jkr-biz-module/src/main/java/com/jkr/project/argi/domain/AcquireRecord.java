package com.jkr.project.argi.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 收购记录对象 bas_acquire_record
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_acquire_record")
public class AcquireRecord extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Excel(name = "主体id")
    private String entId;

    /**
     * 收购标识(0-手动录入;1-查验录入)
     */
    @Excel(name = "收购标识(0-手动录入;1-查验录入)")
    private String acqurieFlag;

    /**
     * 查验表id 查验录入时存入
     */
    @Excel(name = "查验表id 查验录入时存入")
    private String scanRecordId;

    /**
     * 合格证流水号 查验录入时存入
     */
    @Excel(name = "合格证流水号 查验录入时存入")
    private String fullNumber;

    /**
     * 产品id
     */
    @Excel(name = "产品id")
    private String productId;

    /**
     * 产品名称(冗余)
     */
    @Excel(name = "产品名称(冗余)")
    private String productName;

    /**
     * 产品数量 产品数量（重量）
     */
    @Excel(name = "产品数量 产品数量", readConverterExp = "重=量")
    private String productNum;

    /**
     * 产品单位code
     */
    @Excel(name = "产品单位code")
    private String productUnitCode;

    /**
     * 产品单位名称
     */
    @Excel(name = "产品单位名称")
    private String productUnitName;

    /**
     * 开证主体名称
     */
    @Excel(name = "开证主体名称")
    private String certificateEntName;

    /**
     * 开证主体联系电话
     */
    @Excel(name = "开证主体联系电话")
    private String certificateEntContactsPhone;

    /**
     * 产地产区-省
     */
    @Excel(name = "产地产区-省")
    private String productProvince;

    /**
     * 产地产区-市
     */
    @Excel(name = "产地产区-市")
    private String productCity;

    /**
     * 产地产区-县
     */
    @Excel(name = "产地产区-县")
    private String productCounty;

    /**
     * 产地产区-省市县
     */
    @Excel(name = "产地产区-省市县")
    private String productAddress;

    /**
     * 产地产区-详细地址
     */
    @Excel(name = "产地产区-详细地址")
    private String productDetail;

    /**
     * 合格证开具日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合格证开具日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date certificateDate;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private Date beginCreateDate;
    @TableField(exist = false)
    private Date endCreateDate;
    @TableField(exist = false)
    private String addProductOrder;
    @TableField(exist = false)
    private String addAcqurieFlagOrder;
    @TableField(exist = false)
    private String addCreateDateOrder;
    @TableField(exist = false)
    private List<SysFile> fileList = Lists.newArrayList();
}
