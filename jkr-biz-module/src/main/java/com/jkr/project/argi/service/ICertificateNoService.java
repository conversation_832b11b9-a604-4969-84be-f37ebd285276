package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .CertificateNo;

/**
 * 合格证流水Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateNoService {
	/**
	 * 查询合格证流水
	 *
	 * @param id 合格证流水主键
	 * @return 合格证流水
	 */
	public CertificateNo selectCertificateNoById(Long id);

	/**
	 * 查询合格证流水列表
	 *
	 * @param certificateNo 合格证流水
	 * @return 合格证流水集合
	 */
	public List<CertificateNo> selectCertificateNoList(CertificateNo certificateNo);

	/**
	 * 新增合格证流水
	 *
	 * @param certificateNo 合格证流水
	 * @return 结果
	 */
	public int insertCertificateNo(CertificateNo certificateNo);

	/**
	 * 修改合格证流水
	 *
	 * @param certificateNo 合格证流水
	 * @return 结果
	 */
	public int updateCertificateNo(CertificateNo certificateNo);

	/**
	 * 批量删除合格证流水
	 *
	 * @param ids 需要删除的合格证流水主键集合
	 * @return 结果
	 */
	public int deleteCertificateNoByIds(List<Long> ids);

	/**
	 * 删除合格证流水信息
	 *
	 * @param id 合格证流水主键
	 * @return 结果
	 */
	public int deleteCertificateNoById(Long id);

	public CertificateNo getByFullNumber(String fullNumber);

	public Integer getMaxSerialNumber(String batchNo);

	public int insertBatch(List<CertificateNo> list);

	int updateBlockChainId(String id,String blockChainId);

	public List<CertificateNo> getFirstByCertificateId(String certificateId);

}
