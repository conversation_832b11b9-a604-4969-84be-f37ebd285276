package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.EntChange;

/**
 * 主体信息变更Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface EntChangeMapper extends BaseMapper<EntChange>{
	/**
	 * 查询主体信息变更
	 *
	 * @param id 主体信息变更主键
	 * @return 主体信息变更
	 */
	public EntChange selectEntChangeById(Long id);

	/**
	 * 查询主体信息变更列表
	 *
	 * @param entChange 主体信息变更
	 * @return 主体信息变更集合
	 */
	public List<EntChange> selectEntChangeList(EntChange entChange);
	public List<EntChange> findList(EntChange entChange);

	/**
	 * 新增主体信息变更
	 *
	 * @param entChange 主体信息变更
	 * @return 结果
	 */
	public int insertEntChange(EntChange entChange);

	/**
	 * 修改主体信息变更
	 *
	 * @param entChange 主体信息变更
	 * @return 结果
	 */
	public int updateEntChange(EntChange entChange);

	/**
	 * 删除主体信息变更
	 *
	 * @param id 主体信息变更主键
	 * @return 结果
	 */
	public int deleteEntChangeById(Long id);

	/**
	 * 批量删除主体信息变更
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteEntChangeByIds(Long[] ids);

	/**
	 * 批量逻辑删除主体信息变更
	 *
	 * @param  ids 主体信息变更主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过主体信息变更主键id逻辑删除信息
	 *
	 * @param  id 主体信息变更主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
	/**
	 *
	 * @Title: examineSave
	 * @author: lxy
	 * @date: 2021年2月25日14:50:46
	 * @Description: 审批保存
	 * @param:  EntChange
	 * @return: void
	 * @throws
	 */
	void examineSave(EntChange entChange);
	/**
	 *
	 * @title: getUnfinished
	 * @author: lxy
	 * @date: 2021年2月25日 下午7:32:36
	 * @description: 获取未完成的状态的变更记录
	 * @param: entChange
	 * @return: EntChange
	 */
	public EntChange getUnfinished(EntChange entChange);

}
