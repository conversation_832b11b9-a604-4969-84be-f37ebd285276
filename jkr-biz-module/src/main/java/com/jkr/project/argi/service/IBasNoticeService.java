package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .BasNotice;

/**
 * 信息发布Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IBasNoticeService {
	/**
	 * 查询信息发布
	 *
	 * @param id 信息发布主键
	 * @return 信息发布
	 */
	public BasNotice selectBasNoticeById(Long id);

	/**
	 * 查询信息发布列表
	 *
	 * @param basNotice 信息发布
	 * @return 信息发布集合
	 */
	public List<BasNotice> selectBasNoticeList(BasNotice basNotice);

	/**
	 * 新增信息发布
	 *
	 * @param basNotice 信息发布
	 * @return 结果
	 */
	public int insertBasNotice(BasNotice basNotice);

	/**
	 * 修改信息发布
	 *
	 * @param basNotice 信息发布
	 * @return 结果
	 */
	public int updateBasNotice(BasNotice basNotice);

	/**
	 * 批量删除信息发布
	 *
	 * @param ids 需要删除的信息发布主键集合
	 * @return 结果
	 */
	public int deleteBasNoticeByIds(List<Long> ids);

	/**
	 * 删除信息发布信息
	 *
	 * @param id 信息发布主键
	 * @return 结果
	 */
	public int deleteBasNoticeById(Long id);

}
