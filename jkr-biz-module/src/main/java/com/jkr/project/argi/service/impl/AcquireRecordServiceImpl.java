package com.jkr.project.argi.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.domain.Certificate;
import com.jkr.project.argi.domain.CertificateNo;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.service.ICertificateNoService;
import com.jkr.project.argi.service.ICertificateService;
import com.jkr.project.argi.service.IEntService;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.AcquireRecordMapper;
import com.jkr.project.argi.domain.AcquireRecord;
import com.jkr.project.argi.service.IAcquireRecordService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 收购记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
@Transactional
public class AcquireRecordServiceImpl implements IAcquireRecordService {

	@Autowired
	private AcquireRecordMapper acquireRecordMapper;
	@Autowired
	private ICertificateService certificateService;
	@Autowired
	private ICertificateNoService certificateNoService;
	@Autowired
	private IEntService entService;
	@Autowired
	private ISysFileService fileService;

	/**
	 * 查询收购记录
	 *
	 * @param id 收购记录主键
	 * @return 收购记录
	 */
	@Override
	public AcquireRecord selectAcquireRecordById(String id) {
		AcquireRecord acquireRecord = acquireRecordMapper.selectAcquireRecordById(id);
		if (StringUtils.equals(acquireRecord.getAcqurieFlag(), EnumProperty.AcqurieFlagEnum.TYPE_0.getKey())) {
			SysFile sysFile = new SysFile();
			sysFile.setTableName("bas_acquire_record");
			sysFile.setTableId(acquireRecord.getId());
			sysFile.setFieldType("certificatePic");
			List<SysFile> fileList = fileService.findUrlsFileList(sysFile);
			if (acquireRecord != null) {
				acquireRecord.setFileList(fileList);
			}
		}
		return acquireRecord;
	}

	/**
	 * 查询收购记录列表
	 *
	 * @param acquireRecord 收购记录
	 * @return 收购记录
	 */
	@Override
	public List<AcquireRecord> selectAcquireRecordList(AcquireRecord acquireRecord) {
		return acquireRecordMapper.selectAcquireRecordList(acquireRecord);
	}

	/**
	 * 新增收购记录
	 *
	 * @param acquireRecord 收购记录
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertAcquireRecord(AcquireRecord acquireRecord) {
		if (StringUtils.equals(acquireRecord.getAcqurieFlag(), EnumProperty.AcqurieFlagEnum.TYPE_1.getKey())) {
			CertificateNo certificateNo = certificateNoService.getByFullNumber(acquireRecord.getFullNumber());
			if (null != certificateNo) {
				Certificate certificate = certificateService.selectCertificateById(Long.parseLong(certificateNo.getCertificateId()));
				if (null != certificate) {
					acquireRecord.setProductId(certificate.getProductId());
					acquireRecord.setProductName(certificate.getProductName());
					acquireRecord.setProductNum(certificate.getProductNum());
					acquireRecord.setProductUnitCode(certificate.getProductUnitCode());
					acquireRecord.setProductUnitName(certificate.getProductUnitName());
					acquireRecord.setCertificateEntName(certificate.getEntName());
					acquireRecord.setCertificateEntContactsPhone(certificate.getEntContactsPhone());
					acquireRecord.setProductProvince(certificate.getProductProvince());
					acquireRecord.setProductCity(certificate.getProductCity());
					acquireRecord.setProductCounty(certificate.getProductCounty());
					if (StringUtils.equals(certificate.getCertificateType(),EnumProperty.CertificateFlagEnum.TYPE_0.getKey())) {
						if (StringUtils.equals(certificate.getProductMixFlag(), EnumProperty.MixFlagEnum.FLAG_0.getKey())) {
							acquireRecord.setProductAddress(certificate.getProductAddress());
							acquireRecord.setProductDetail(certificate.getProductDetail());
						} else if (StringUtils.equals(certificate.getProductMixFlag(), EnumProperty.MixFlagEnum.FLAG_1.getKey())) {
							//生产类型合格证，组合品判断产地显示的时候，获取主体地址进行展示
							Ent entParam = new Ent();
							Ent ent = entService.selectEntById(SecurityUtils.getDeptId());
							if (ent != null) {
								acquireRecord.setProductAddress(ent.getAddress());
								acquireRecord.setProductDetail(ent.getDetail());
							}
						}
					}
					acquireRecord.setCertificateDate(certificate.getCreateTime());
				}
			}
		}
		acquireRecord.insertInit(SecurityUtils.getLoginUser().getUsername());
		int flag = acquireRecordMapper.insertAcquireRecord(acquireRecord);

		if (StringUtils.equals(acquireRecord.getAcqurieFlag(), EnumProperty.AcqurieFlagEnum.TYPE_0.getKey())) {
			//先删除原有附件
			SysFile attachment = new SysFile();
			attachment.setTableId(acquireRecord.getId());
			attachment.setTableName("bas_acquire_record");
			attachment.setFieldType("certificatePic");
			List<SysFile> fileList = fileService.findUrlsFileList(attachment);
			if (CollUtil.isNotEmpty(fileList)) {
				fileService.deleteSysFileByIds(fileList.stream().map(SysFile::getFileId).collect(Collectors.toList()).toArray(Long[]::new));
			}

			//保存附件
			List<SysFile> finalList = acquireRecord.getFileList();
			if (!finalList.isEmpty()) {
				for (SysFile att : finalList) {
					att.setTableId(acquireRecord.getId());
					att.setTableName("bas_acquire_record");
					att.setFieldType("certificatePic");
				}
				fileService.insertSysFileList(finalList);
			}
		}
		return flag;
	}

	/**
	 * 修改收购记录
	 *
	 * @param acquireRecord 收购记录
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateAcquireRecord(AcquireRecord acquireRecord) {
		if (StringUtils.equals(acquireRecord.getAcqurieFlag(), EnumProperty.AcqurieFlagEnum.TYPE_0.getKey())) {
			//先删除原有附件
			SysFile attachment = new SysFile();
			attachment.setTableId(acquireRecord.getId());
			attachment.setTableName("bas_acquire_record");
			attachment.setFieldType("certificatePic");
			List<SysFile> fileList = fileService.findUrlsFileList(attachment);
			if (CollUtil.isNotEmpty(fileList)) {
				fileService.deleteSysFileByIds(fileList.stream().map(SysFile::getFileId).collect(Collectors.toList()).toArray(Long[]::new));
			}

			//保存附件
			List<SysFile> finalList = acquireRecord.getFileList();
			if (!finalList.isEmpty()) {
				for (SysFile att : finalList) {
					att.setTableId(acquireRecord.getId());
					att.setTableName("bas_acquire_record");
					att.setFieldType("certificatePic");
				}
				fileService.insertSysFileList(finalList);
			}
		}
		acquireRecord.updateInit(SecurityUtils.getLoginUser().getUsername());
		return acquireRecordMapper.updateAcquireRecord(acquireRecord);
	}

	/**
	 * 批量删除收购记录
	 *
	 * @param ids 需要删除的收购记录主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteAcquireRecordByIds(List<Long> ids) {
		return acquireRecordMapper.logicRemoveByIds(ids);
		//return acquireRecordMapper.deleteAcquireRecordByIds(ids);
	}

	/**
	 * 删除收购记录信息
	 *
	 * @param id 收购记录主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteAcquireRecordById(String id) {
		return acquireRecordMapper.logicRemoveById(id);
		//return acquireRecordMapper.deleteAcquireRecordById(id);
	}

}
