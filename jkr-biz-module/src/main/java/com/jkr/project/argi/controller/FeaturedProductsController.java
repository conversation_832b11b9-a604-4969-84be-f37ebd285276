package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.FeaturedProducts;
import com.jkr.project.argi.service.IFeaturedProductsService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 特色产品Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/featuredProducts")
public class FeaturedProductsController extends BaseController {
	@Autowired
	private IFeaturedProductsService featuredProductsService;

/**
 * 查询特色产品列表
 */
@PreAuthorize("@ss.hasPermi('argi:featuredProducts:list')")
@GetMapping("/list")
	public TableDataInfo list(FeaturedProducts featuredProducts) {
		startPage();
		List<FeaturedProducts> list = featuredProductsService.selectFeaturedProductsList(featuredProducts);
		return getDataTable(list);
	}

	/**
	 * 导出特色产品列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:featuredProducts:export')")
	@Log(title = "导出特色产品列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, FeaturedProducts featuredProducts) {
		List<FeaturedProducts> list = featuredProductsService.selectFeaturedProductsList(featuredProducts);
		ExcelUtil<FeaturedProducts> util = new ExcelUtil<FeaturedProducts>(FeaturedProducts. class);
		util.exportExcel(response, list, "特色产品数据");
	}

	/**
	 * 获取特色产品详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:featuredProducts:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(featuredProductsService.selectFeaturedProductsById(id));
	}

	/**
	 * 新增特色产品
	 */
	@PreAuthorize("@ss.hasPermi('argi:featuredProducts:add')")
	@Log(title = "新增特色产品", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody FeaturedProducts featuredProducts) {
		return toAjax(featuredProductsService.insertFeaturedProducts(featuredProducts));
	}

	/**
	 * 修改特色产品
	 */
	@PreAuthorize("@ss.hasPermi('argi:featuredProducts:edit')")
	@Log(title = "修改特色产品", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody FeaturedProducts featuredProducts) {
		return toAjax(featuredProductsService.updateFeaturedProducts(featuredProducts));
	}

	/**
	 * 删除特色产品
	 */
	@PreAuthorize("@ss.hasPermi('argi:featuredProducts:remove')")
	@Log(title = "删除特色产品", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(featuredProductsService.deleteFeaturedProductsById(id));
	}

	/**
	 * 批量删除特色产品
	 */
	@PreAuthorize("@ss.hasPermi('argi:featuredProducts:batchRemove')")
	@Log(title = "批量删除特色产品", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody FeaturedProducts featuredProducts) {
		return toAjax(featuredProductsService.deleteFeaturedProductsByIds(featuredProducts.getIds()));
	}
}
