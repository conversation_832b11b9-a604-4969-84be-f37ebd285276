package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateNoMapper;
import com.jkr.project.argi.domain.CertificateNo;
import com.jkr.project.argi.service.ICertificateNoService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合格证流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateNoServiceImpl implements ICertificateNoService {
	@Autowired
	private CertificateNoMapper certificateNoMapper;

	/**
	 * 查询合格证流水
	 *
	 * @param id 合格证流水主键
	 * @return 合格证流水
	 */
	@Override
	public CertificateNo selectCertificateNoById(Long id) {
		return certificateNoMapper.selectCertificateNoById(id);
	}

	/**
	 * 查询合格证流水列表
	 *
	 * @param certificateNo 合格证流水
	 * @return 合格证流水
	 */
	@Override
	public List<CertificateNo> selectCertificateNoList(CertificateNo certificateNo) {
		return certificateNoMapper.selectCertificateNoList(certificateNo);
	}

	/**
	 * 新增合格证流水
	 *
	 * @param certificateNo 合格证流水
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateNo(CertificateNo certificateNo) {
		certificateNo.insertInit(SecurityUtils.getLoginUser().getUsername());
		return certificateNoMapper.insertCertificateNo(certificateNo);
	}

	/**
	 * 修改合格证流水
	 *
	 * @param certificateNo 合格证流水
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateNo(CertificateNo certificateNo) {
		certificateNo.updateInit(SecurityUtils.getLoginUser().getUsername());
		return certificateNoMapper.updateCertificateNo(certificateNo);
	}

	/**
	 * 批量删除合格证流水
	 *
	 * @param ids 需要删除的合格证流水主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateNoByIds(List<Long> ids) {
		return certificateNoMapper.logicRemoveByIds(ids);
		//return certificateNoMapper.deleteCertificateNoByIds(ids);
	}

	/**
	 * 删除合格证流水信息
	 *
	 * @param id 合格证流水主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateNoById(Long id) {
		return certificateNoMapper.logicRemoveById(id);
		//return certificateNoMapper.deleteCertificateNoById(id);
	}

	/**
	 *
	 * @title: getByFullNumber
	 * @author: lxy
	 * @date: 2020年9月11日 上午10:10:54
	 * @description: 通过合格证号查询
	 * @param: @param fullNumber
	 * @param: @return
	 * @return: CertificateNo
	 */
	public CertificateNo getByFullNumber(String fullNumber) {
		if(StringUtils.isBlank(fullNumber)) {
			return null;
		}
		CertificateNo query=new CertificateNo();
		query.setFullNumber(fullNumber);
		List<CertificateNo> list = selectCertificateNoList(query);
		if(list.isEmpty()) {
			return null;
		}
		return list.get(0);
	}

	@Override
	public Integer getMaxSerialNumber(String batchNo) {
		if(StringUtils.isBlank(batchNo)) {
			return null;
		}
		return certificateNoMapper.getMaxSerialNumber(batchNo);
	}

	@Override
	public int insertBatch(List<CertificateNo> list) {
		if(null==list || list.isEmpty()) {
			return 0;
		}
		for (int i = 0; i < list.size(); i++) {
			CertificateNo certificateNo = list.get(i);
			certificateNo.insertInit(SecurityUtils.getLoginUser().getUsername());
		}
		return certificateNoMapper.insertBatch(list);
	}

	@Override
	public int updateBlockChainId(String id, String blockChainId) {
		return certificateNoMapper.updateBlockChainId(id, blockChainId);
	}

	@Override
	public List<CertificateNo> getFirstByCertificateId(String certificateId) {
		return certificateNoMapper.getFirstByCertificateId(certificateId);
	}
}
