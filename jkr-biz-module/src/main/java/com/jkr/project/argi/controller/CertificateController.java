package com.jkr.project.argi.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.service.IEntService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Certificate;
import com.jkr.project.argi.service.ICertificateService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 合格证Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificate")
public class CertificateController extends BaseController {
    @Autowired
    private ICertificateService certificateService;
    @Autowired
    private IEntService entService;

    /**
     * 查询合格证列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:list')")
    @GetMapping("/list")
    public TableDataInfo list(Certificate certificate) {
        startPage();
        List<Certificate> list = certificateService.selectCertificateList(certificate);
        return getDataTable(list);
    }

    /**
     * 导出合格证列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:export')")
    @Log(title = "导出合格证列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Certificate certificate) {
        List<Certificate> list = certificateService.selectCertificateList(certificate);
        ExcelUtil<Certificate> util = new ExcelUtil<Certificate>(Certificate.class);
        util.exportExcel(response, list, "合格证数据");
    }

    /**
     * 获取合格证详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(certificateService.selectCertificateById(id));
    }

    /**
     * 新增合格证
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:add')")
    @Log(title = "新增合格证", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody Certificate certificate) {
        return toAjax(certificateService.insertCertificate(certificate));
    }

    /**
     * 修改合格证
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:edit')")
    @Log(title = "修改合格证", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody Certificate certificate) {
        return toAjax(certificateService.updateCertificate(certificate));
    }

    /**
     * 删除合格证
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:remove')")
    @Log(title = "删除合格证", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(certificateService.deleteCertificateById(id));
    }

    /**
     * 批量删除合格证
     */
    @PreAuthorize("@ss.hasPermi('argi:certificate:batchRemove')")
    @Log(title = "批量删除合格证", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody Certificate certificate) {
        return toAjax(certificateService.deleteCertificateByIds(certificate.getIds()));
    }

    /**
     *
     * @title: getSummaryInfo
     * @author: lxy
     * @date: 2020年7月25日 上午9:45:12
     * @description: 获取汇总信息
     * @param: @return
     * @return: ResponseResult
     */
    @RequestMapping(value = "/summaryInfo")
    public AjaxResult getSummaryInfo(){
        Map<String,Object> result=null;
        try {
            Ent ent=entService.getByTableId(SecurityUtils.getDeptId()+"");
            result=certificateService.getSummaryInfo(ent.getId()+"");
        } catch(Exception e) {
            return AjaxResult.error("获取数据失败，错误如下：" + e.getMessage());
        }
        return AjaxResult.success(result);
    }


    //增量查询合格证数据
    @GetMapping(value = "/incrementalQuery")
    public AjaxResult incrementalQuery(Date date){
        try {
            List<Certificate> result = certificateService.incrementalQuery(date);
            return AjaxResult.success(result);
        } catch(Exception e) {
            return AjaxResult.error("获取数据失败，错误如下：" + e.getMessage());
        }
    }
}
