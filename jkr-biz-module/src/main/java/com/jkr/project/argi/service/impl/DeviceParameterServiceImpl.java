package com.jkr.project.argi.service.impl;

import java.util.List;

import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.impl.SysFileServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.DeviceParameterMapper;
import com.jkr.project.argi.domain.DeviceParameter;
import com.jkr.project.argi.service.IDeviceParameterService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 蓝牙设备参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Service
@Transactional
public class DeviceParameterServiceImpl implements IDeviceParameterService {
    @Autowired
    private DeviceParameterMapper deviceParameterMapper;

    @Autowired
    private SysFileServiceImpl sysFileService;

    /**
     * 查询蓝牙设备参数
     *
     * @param id 蓝牙设备参数主键
     * @return 蓝牙设备参数
     */
    @Override
    public DeviceParameter selectDeviceParameterById(String id) {
        DeviceParameter deviceParameter = deviceParameterMapper.selectDeviceParameterById(id);
        if (deviceParameter != null) {
            SysFile sysFile = new SysFile();
            sysFile.setTableId(deviceParameter.getId());
            List<SysFile> fileList = sysFileService.findUrlsFileList(sysFile);
            deviceParameter.setDeviceImageFiles(fileList);
        }
        return deviceParameter;
    }

    /**
     * 查询蓝牙设备参数列表
     *
     * @param deviceParameter 蓝牙设备参数
     * @return 蓝牙设备参数
     */
    @Override
    public List<DeviceParameter> selectDeviceParameterList(DeviceParameter deviceParameter) {
        return deviceParameterMapper.selectDeviceParameterList(deviceParameter);
    }

    /**
     * 新增蓝牙设备参数
     *
     * @param deviceParameter 蓝牙设备参数
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int insertDeviceParameter(DeviceParameter deviceParameter) {
        deviceParameter.insertInit(SecurityUtils.getLoginUser().getUsername());
		int result = deviceParameterMapper.insertDeviceParameter(deviceParameter);
		if (deviceParameter.getDeviceImageFiles() != null) {
			for (SysFile file : deviceParameter.getDeviceImageFiles()) {
				file.setTableId(deviceParameter.getId());
				sysFileService.insertSysFile(file);
			}
		}
		return result;
    }

    /**
     * 修改蓝牙设备参数
     *
     * @param deviceParameter 蓝牙设备参数
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int updateDeviceParameter(DeviceParameter deviceParameter) {
        deviceParameter.updateInit(SecurityUtils.getLoginUser().getUsername());
		int result = deviceParameterMapper.updateDeviceParameter(deviceParameter);
		if (deviceParameter.getDeviceImageFiles() != null) {
			for (SysFile file : deviceParameter.getDeviceImageFiles()) {
				file.setTableId(deviceParameter.getId());
				sysFileService.insertSysFile(file);
			}
		}
		return result;
    }

    /**
     * 批量删除蓝牙设备参数
     *
     * @param ids 需要删除的蓝牙设备参数主键
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int deleteDeviceParameterByIds(List<Long> ids) {
        return deviceParameterMapper.logicRemoveByIds(ids);
        //return deviceParameterMapper.deleteDeviceParameterByIds(ids);
    }

    /**
     * 删除蓝牙设备参数信息
     *
     * @param id 蓝牙设备参数主键
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int deleteDeviceParameterById(String id) {
        return deviceParameterMapper.logicRemoveById(id);
        //return deviceParameterMapper.deleteDeviceParameterById(id);
    }

}
