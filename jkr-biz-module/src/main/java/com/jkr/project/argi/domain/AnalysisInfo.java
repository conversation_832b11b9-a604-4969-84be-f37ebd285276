package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 首页统计对象
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class AnalysisInfo {

    private static final long serialVersionUID = 1L;

    private Integer dataValue;
    private String dataCode;
    private String dataLabel;
    // 查询条件
    private String queryAreaCode;
}
