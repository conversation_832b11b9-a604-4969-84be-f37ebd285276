package com.jkr.project.argi.service;

import java.util.List;
import java.util.Map;

import com.jkr.project.argi.domain .ScanRecord;

/**
 * 扫描日志Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IScanRecordService {
	/**
	 * 查询扫描日志
	 *
	 * @param id 扫描日志主键
	 * @return 扫描日志
	 */
	public ScanRecord selectScanRecordById(Long id);

	/**
	 * 查询扫描日志列表
	 *
	 * @param scanRecord 扫描日志
	 * @return 扫描日志集合
	 */
	public List<ScanRecord> selectScanRecordList(ScanRecord scanRecord);

	/**
	 * 新增扫描日志
	 *
	 * @param scanRecord 扫描日志
	 * @return 结果
	 */
	public int insertScanRecord(ScanRecord scanRecord);

	/**
	 * 修改扫描日志
	 *
	 * @param scanRecord 扫描日志
	 * @return 结果
	 */
	public int updateScanRecord(ScanRecord scanRecord);

	/**
	 * 批量删除扫描日志
	 *
	 * @param ids 需要删除的扫描日志主键集合
	 * @return 结果
	 */
	public int deleteScanRecordByIds(List<Long> ids);

	/**
	 * 删除扫描日志信息
	 *
	 * @param id 扫描日志主键
	 * @return 结果
	 */
	public int deleteScanRecordById(Long id);

	public void saveScanRecord(String certificateId);

	public Map<String, Object> saveScanRecord(String certificateId, String fullNumber, String entId);

	public List<ScanRecord> findByCertificateId(String certificateId);

	public Map<String,Object> getCountAmountByCertificateId(String certificateId);

	public void saveFullNumber(String fullNumber);

	public Map<String, Object> saveFullNumber(String fullNumber, String endId);

	public List<ScanRecord> findByFullNumber(String fullNumber);

	public List<ScanRecord> findTraceStatisticList(ScanRecord scanRecord);

	public List<ScanRecord> findEntScanPage(ScanRecord scanRecord);
}
