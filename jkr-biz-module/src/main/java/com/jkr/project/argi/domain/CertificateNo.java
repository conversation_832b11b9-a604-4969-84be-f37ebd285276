package com.jkr.project.argi.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 合格证流水对象 bas_certificate_no
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_certificate_no")
public class CertificateNo extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 合格证id
     */
    @Excel(name = "合格证id")
    private String certificateId;

    /**
     * 合格证批次号
     */
    @Excel(name = "合格证批次号")
    private String batchNo;

    /**
     * 流水号
     */
    @Excel(name = "流水号")
    private Integer serialNumber;

    /**
     * 合格证号
     */
    @Excel(name = "合格证号")
    private String fullNumber;

    /**
     * 打印次数
     */
    @Excel(name = "打印次数")
    private Integer printCount;

    /**
     * 区块连id
     */
    @Excel(name = "区块连id")
    private String blockChainId;

    /**
     * 电子证标识：0-纸质证;1-电子证
     */
    @Excel(name = "电子证标识：0-纸质证;1-电子证")
    private String electricFlag;

    /**
     * 主键集合
     */
    private List<Long> ids;

    public CertificateNo() {
        super();
    }
    public CertificateNo(String certificateId,String batchNo,Integer serialNumber,String fullNumber) {
        super();
        this.certificateId=certificateId;
        this.batchNo=batchNo;
        this.serialNumber=serialNumber;
        this.fullNumber=fullNumber;
        this.printCount=1;
    }
}
