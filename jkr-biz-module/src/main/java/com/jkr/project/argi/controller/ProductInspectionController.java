package com.jkr.project.argi.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.collection.CollUtil;
import com.jkr.common.utils.ValidationUtils;
import com.jkr.framework.security.LoginUser;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.service.IEntService;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.security.access.method.P;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.ProductInspection;
import com.jkr.project.argi.service.IProductInspectionService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 产品检测Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/productInspection")
public class ProductInspectionController extends BaseController {

    @Autowired
    private IProductInspectionService productInspectionService;
    @Autowired
    private IEntService entService;
    @Autowired
    private ISysFileService fileService;

    /**
     * 查询产品检测列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductInspection productInspection) {
        startPage();
        List<ProductInspection> list = productInspectionService.selectProductInspectionList(productInspection);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('argi:productInspection:list')")
    @PostMapping("/listAll")
    public AjaxResult listAll(@RequestBody ProductInspection productInspection) {
        List<ProductInspection> list = productInspectionService.selectProductInspectionList(productInspection);
        if (CollUtil.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                ProductInspection item = list.get(i);
                if (item != null) {
                    //获取附件
                    SysFile attachmentParam = new SysFile();
                    attachmentParam.setTableId(item.getId());
                    List<SysFile> attachmentList = fileService.findUrlsFileList(attachmentParam);
                    item.setFileList(attachmentList);
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 导出产品检测列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:export')")
    @Log(title = "导出产品检测列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductInspection productInspection) {
        List<ProductInspection> list = productInspectionService.selectProductInspectionList(productInspection);
        ExcelUtil<ProductInspection> util = new ExcelUtil<ProductInspection>(ProductInspection.class);
        util.exportExcel(response, list, "产品检测数据");
    }

    /**
     * 获取产品检测详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productInspectionService.selectProductInspectionById(id));
    }

    /**
     * 新增产品检测
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:add')")
    @Log(title = "新增产品检测", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody ProductInspection productInspection) {
        return toAjax(productInspectionService.insertProductInspection(productInspection));
    }

    /**
     *
     * @title: saveEnter
     * @author: lxy
     * @date: 2021年1月15日16:57:04
     * @description: 产品录入检测信息保存方法
     * @param: productInspection
     * @return: AjaxResult
     */
    @PostMapping("/saveEnter")
    public AjaxResult saveEnter(@RequestBody ProductInspection productInspection){
        try {
            List<Map<String, Object>> errorList = ValidationUtils.validationResult(productInspection);
            if (!errorList.isEmpty()) {
                return AjaxResult.error("校验未通过", errorList);
            }
            //保存录入记录
            productInspectionService.saveEnter(productInspection);
            //更新最新样品记录
            productInspectionService.updateNewestSample(productInspection);
        } catch (Exception e) {
            return AjaxResult.error("保存失败，错误如下：" + e.getMessage());
        }
        return AjaxResult.success();
    }

    /**
     *
     * @title: getEnter
     * @author: lxy
     * @date: 2020-7-21
     * @description: 获取录入检测信息
     * @param: productInspection
     * @return: AjaxResult
     */
    @PostMapping("/getEnter")
    public AjaxResult getEnter(@RequestBody ProductInspection productInspection){
        return AjaxResult.success(productInspectionService.getEnter(productInspection.getId()+""));
    }

    /**
     *
     * @title: findNewestList
     * @author: lxy
     * @date: 2021年3月10日10:28:43
     * @description: 获取最新检测数据集合
     * @param: productInspection
     * @return: AjaxResult
     */
    @PostMapping("/findNewestList")
    public AjaxResult findNewestList(@RequestBody ProductInspection productInspection){
        Ent ent = entService.selectEntById(getLoginUser().getDeptId());
        List<ProductInspection> list = productInspectionService.findNewestList(ent.getId()+"", productInspection.getProductId(), productInspection.getInspectionSituation());
        return AjaxResult.success(list);
    }

    /**
     *
     * @title: findNewestListByInspectionSituationList
     * @author: LiuBin
     * @date: 2021年3月10日10:28:43
     * @description: 获取最新检测数据集合
     * @param: productInspection
     * @return: AjaxResult
     */
    @PostMapping("/findNewestListByInspectionSituationList")
    public AjaxResult findNewestListByInspectionSituationList(@RequestBody ProductInspection productInspection){
        Ent ent = entService.selectEntById(getLoginUser().getDeptId());
        Map<String,Object> map = productInspectionService.findNewestListByInspectionSituationList(ent.getId()+"", productInspection.getProductId(), productInspection.getInspectionSituationList());
        return AjaxResult.success(map);
    }

    /**
     * 修改产品检测
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:edit')")
    @Log(title = "修改产品检测", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody ProductInspection productInspection) {
        return toAjax(productInspectionService.updateProductInspection(productInspection));
    }

    /**
     * 删除产品检测
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:remove')")
    @Log(title = "删除产品检测", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(productInspectionService.deleteProductInspectionById(id));
    }

    /**
     * 批量删除产品检测
     */
    @PreAuthorize("@ss.hasPermi('argi:productInspection:batchRemove')")
    @Log(title = "批量删除产品检测", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody ProductInspection productInspection) {
        return toAjax(productInspectionService.deleteProductInspectionByIds(productInspection.getIds()));
    }
}
