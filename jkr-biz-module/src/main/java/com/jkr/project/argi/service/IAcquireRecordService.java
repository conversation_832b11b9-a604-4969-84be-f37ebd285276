package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .AcquireRecord;

/**
 * 收购记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
public interface IAcquireRecordService {
	/**
	 * 查询收购记录
	 *
	 * @param id 收购记录主键
	 * @return 收购记录
	 */
	public AcquireRecord selectAcquireRecordById(String id);

	/**
	 * 查询收购记录列表
	 *
	 * @param acquireRecord 收购记录
	 * @return 收购记录集合
	 */
	public List<AcquireRecord> selectAcquireRecordList(AcquireRecord acquireRecord);

	/**
	 * 新增收购记录
	 *
	 * @param acquireRecord 收购记录
	 * @return 结果
	 */
	public int insertAcquireRecord(AcquireRecord acquireRecord);

	/**
	 * 修改收购记录
	 *
	 * @param acquireRecord 收购记录
	 * @return 结果
	 */
	public int updateAcquireRecord(AcquireRecord acquireRecord);

	/**
	 * 批量删除收购记录
	 *
	 * @param ids 需要删除的收购记录主键集合
	 * @return 结果
	 */
	public int deleteAcquireRecordByIds(List<Long> ids);

	/**
	 * 删除收购记录信息
	 *
	 * @param id 收购记录主键
	 * @return 结果
	 */
	public int deleteAcquireRecordById(String id);

}
