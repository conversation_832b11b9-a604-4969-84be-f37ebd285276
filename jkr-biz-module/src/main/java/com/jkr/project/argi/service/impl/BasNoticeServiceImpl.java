package com.jkr.project.argi.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.constant.PublishStatusConstants;
import com.jkr.project.argi.task.NoticePublishHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.BasNoticeMapper;
import com.jkr.project.argi.domain.BasNotice;
import com.jkr.project.argi.service.IBasNoticeService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 信息发布Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class BasNoticeServiceImpl implements IBasNoticeService {
	@Autowired
	private BasNoticeMapper basNoticeMapper;

	@Autowired
	private NoticePublishHandler noticePublishHandler;

	/**
	 * 查询信息发布
	 *
	 * @param id 信息发布主键
	 * @return 信息发布
	 */
	@Override
	public BasNotice selectBasNoticeById(Long id) {
		return basNoticeMapper.selectBasNoticeById(id);
	}

	/**
	 * 查询信息发布列表
	 *
	 * @param basNotice 信息发布
	 * @return 信息发布
	 */
	@Override
	public List<BasNotice> selectBasNoticeList(BasNotice basNotice) {
		return basNoticeMapper.selectBasNoticeList(basNotice);
	}

	/**
	 * 新增信息发布
	 *
	 * @param basNotice 信息发布
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertBasNotice(BasNotice basNotice) {
		basNotice.insertInit(SecurityUtils.getLoginUser().getUsername());

		// 设置初始状态为未发布
		if (basNotice.getStatus() == null) {
			basNotice.setStatus(PublishStatusConstants.STATUS_UNPUBLISHED);
		}

		int rows = basNoticeMapper.insert(basNotice);

		// 如果设置了发布时间且大于当前时间，添加到发布队列
		if (basNotice.getPublishTime() != null && basNotice.getPublishTime().isAfter(LocalDateTime.now())) {
			noticePublishHandler.addToPublishQueue(basNotice);
		}

		return rows;
	}

	/**
	 * 修改信息发布
	 *
	 * @param basNotice 信息发布
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateBasNotice(BasNotice basNotice) {
		basNotice.updateInit(SecurityUtils.getLoginUser().getUsername());

		// 获取原始记录以比较发布时间变化
		BasNotice originalNotice = basNoticeMapper.selectBasNoticeById(basNotice.getId());

		int rows = basNoticeMapper.updateById(basNotice);

		// 处理发布时间变更时的队列管理
		if (basNotice.getPublishTime() != null) {
			// 如果原来有发布时间，先从队列中移除
			if (originalNotice != null && originalNotice.getPublishTime() != null) {
				noticePublishHandler.removeFromPublishQueue(basNotice.getId());
			}

			// 如果新的发布时间大于当前时间，重新添加到队列
			if (basNotice.getPublishTime().isAfter(LocalDateTime.now())) {
				noticePublishHandler.addToPublishQueue(basNotice);
			}
		} else if (originalNotice != null && originalNotice.getPublishTime() != null) {
			// 如果清空了发布时间，从队列中移除
			noticePublishHandler.removeFromPublishQueue(basNotice.getId());
		}

		return rows;
	}

	/**
	 * 批量删除信息发布
	 *
	 * @param ids 需要删除的信息发布主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteBasNoticeByIds(List<Long> ids) {
		return basNoticeMapper.logicRemoveByIds(ids);
		//return basNoticeMapper.deleteBasNoticeByIds(ids);
	}

	/**
	 * 删除信息发布信息
	 *
	 * @param id 信息发布主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteBasNoticeById(Long id) {
		return basNoticeMapper.logicRemoveById(id);
		//return basNoticeMapper.deleteBasNoticeById(id);
	}

}
