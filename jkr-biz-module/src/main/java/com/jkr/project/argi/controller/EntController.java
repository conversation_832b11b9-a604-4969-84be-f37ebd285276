package com.jkr.project.argi.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.jkr.common.constant.Constants;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.utils.Global;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.ValidationUtils;
import com.jkr.project.argi.domain.EntDetail;
import com.jkr.project.argi.domain.ExcelVo.EntSupervisionVo;
import com.jkr.project.argi.service.IEntDetailService;
import com.jkr.project.argi.service.IProductService;
import com.jkr.project.system.domain.SysArea;
import com.jkr.project.system.domain.SysDept;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.ISysAreaService;
import com.jkr.project.system.service.ISysDeptService;
import com.jkr.project.system.service.ISysUserService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.service.IEntService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 主体信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/ent")
public class EntController extends BaseController {
	@Autowired
	private IEntService entService;
	@Autowired
	private ISysAreaService areaService;
	@Autowired
	private ISysUserService userService;
	@Autowired
	private IEntDetailService entDetailService;
	@Autowired
	private IProductService productService;
	@Autowired
	private ISysDeptService deptService;

	/**
	 * 查询主体信息列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:list')")
	@GetMapping("/list")
	public TableDataInfo list(Ent ent) {
		startPage();
		/**
		 * 根据业务需求添加非冻结状态
		 * 2021年5月17日13:59:12
		 * lxy
		 */
		ent.setFrozenFlag(Global.NO);
		ent.setExamineStatus(EnumProperty.ExamineStatusEnum.PASS.getKey());
		List<Ent> list = entService.findListQuick(ent);
		return getDataTable(list);
	}
	/**
	 * 查询审核主体信息列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:list')")
	@GetMapping("/examine/list")
	public TableDataInfo examineEntList(Ent ent) {
		startPage();
		ent.setRemark(Constants.YES);
		List<Ent> list = entService.findListQuick(ent);
		return getDataTable(list);
	}

	/**
	 * 导出主体信息列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:export')")
	@Log(title = "导出主体信息列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, Ent ent) {
		entService.export(response,ent);
//		ExcelUtil<Ent> util = new ExcelUtil<Ent>(Ent. class);
//		util.exportExcel(response, list, "主体信息数据");
	}
	/**
	 * 导出主体信息列表-监督主体
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:export')")
	@Log(title = "导出主体信息列表", businessType = BusinessType.EXPORT)
	@PostMapping("/exportSupervision")
	public void exportSupervision(HttpServletResponse response, SysDept dept) {
		dept.setSupervisionFlag(Constants.YES);
		List<SysDept> list = deptService.selectEntDeptList(dept);
		List<EntSupervisionVo> entSupervisionVoList=new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			EntSupervisionVo es=new EntSupervisionVo();
			BeanUtils.copyProperties(list.get(i),es);
			es.setExcelNo(i+1);
			es.setDeptTypeName(StringUtils.isEmpty(es.getDeptType())?"--":EnumProperty.AeptTypeEnum.getValueByKey(es.getDeptType()));
			entSupervisionVoList.add(es);
		}
		ExcelUtil<EntSupervisionVo> util = new ExcelUtil<EntSupervisionVo>(EntSupervisionVo. class);
		util.exportExcel(response, entSupervisionVoList, "主体信息数据");
	}

	/**
	 * 获取主体信息详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		Ent ent = entService.selectEntById(id);
//		if (ent != null&&EnumProperty.ExamineStatusEnum.TODO.getKey().equals(ent.getExamineStatus())){
//			ent.setExamineMan(SecurityUtils.getLoginUser().getUser().getNickName());
//		}
		return success(ent);
	}

	/**
	 * 新增主体信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:add')")
	@Log(title = "新增主体信息", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody Ent ent) {
		return toAjax(entService.insertEnt(ent));
	}

	/**
	 * 修改主体信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:edit')")
	@Log(title = "修改主体信息", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody Ent ent) {
		return toAjax(entService.updateEnt(ent));
	}

	/**
	 * 删除主体信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:remove')")
	@Log(title = "删除主体信息", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(entService.deleteEntById(id));
	}

	/**
	 * 批量删除主体信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:batchRemove')")
	@Log(title = "批量删除主体信息", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody Ent ent) {
		return toAjax(entService.deleteEntByIds(ent.getIds()));
	}


	/**
	 *
	 * @title: save
	 * @author: zmy
	 * @date: 2020年7月16日 下午17:57:11
	 * @description: 主体信息保存方法
	 * @param: @param ent
	 * @param: @param request
	 * @param: @param response
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "save", method = RequestMethod.POST)
	@Log(title = "保存主体信息", businessType = BusinessType.UPDATE)
	public AjaxResult save(@RequestBody Ent ent){
		List<Map<String, Object>> errorList = ValidationUtils.validationResult(ent);
		if(!errorList.isEmpty()) {
			return AjaxResult.error("校验未通过",errorList);
		}
		try {
			entService.saveEnt(ent);
		} catch(Exception e) {
			return AjaxResult.error("主体信息保存失败，错误如下：" + e.getMessage());
		}
		return AjaxResult.success(ent);
	}

	/**
	 *
	 * @title: getArea
	 * @author: lp
	 * @date: 2020-7-21
	 * @description: 获取区域
	 * @param: @param area
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "getArea", method = RequestMethod.POST)
	public AjaxResult getArea(SysArea area) {
		List<SysArea> areaList = areaService.findTreeList(area);
		return AjaxResult.success(areaList);
	}

	/**
	 *
	 * @title: checkIdCard
	 * @author: lp
	 * @date: 2020-7-21
	 * @description: 身份证号校验
	 * @param: @param ent
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "checkIdCard", method = RequestMethod.POST)
	public AjaxResult checkIdCard(@RequestBody Ent ent) {
		return AjaxResult.success(entService.checkIdCard(ent));
	}

	/**
	 *
	 * @title: getEntInfo
	 * @author: lp
	 * @date: 2020-7-21
	 * @description: 获取主体
	 * @param: @param ent
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "getEntInfo", method = RequestMethod.POST)
	public AjaxResult getEntInfo(@RequestBody Ent ent) {
		return AjaxResult.success(entService.selectEntById(ent.getId()));
	}

	/**
	 *
	 * @title: checkSocialCode
	 * @author: zmy
	 * @date: 2020-7-21
	 * @description: 统一社会信用代码校验唯一性
	 * @param: @param ent
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "checkSocialCode", method = RequestMethod.POST)
	public AjaxResult checkSocialCode(@RequestBody Ent ent) {
		return AjaxResult.success(entService.checkSocialCode(ent));
	}
	/**
	 *
	 * @title: getEnt
	 * @author: lxy
	 * @date: 2020-7-21
	 * @description: 获取主体信息
	 * @param: @param ent
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "getEnt", method = RequestMethod.GET)
	public AjaxResult getEnt() {
		Ent ent=null;
		try {
			ent=entService.getByTableId(String.valueOf(SecurityUtils.getLoginUser().getUserId()));
		} catch (Exception e) {
			return AjaxResult.error("获取数据错误：" + e.getMessage());
		}
		return AjaxResult.success(ent);
	}
	/**
	 *
	 * @title: findMainTypeList
	 * @author: lxy
	 * @date: 2020年8月11日8:41:13
	 * @description: 获取主体类别集合
	 * @param: entType  主体性质
	 * @param: request
	 * @param: response
	 * @param: @throws Exception
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "findMainTypeList/{entType}", method = RequestMethod.GET)
	public AjaxResult findMainTypeList(@PathVariable(value = "entType") String entType) {
		try {
			return AjaxResult.success(this.entService.findMainTypeList(entType));
		} catch(Exception e) {
			return AjaxResult.error("获取数据失败，错误如下：" + e.getMessage());
		}
	}

	/**
	 *
	 * @title: updateChangeView
	 * @author: lxy
	 * @date: 2021年3月6日16:00:24
	 * @description: 主体变更业务查看状态更新
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "updateChangeView", method = RequestMethod.POST)
	public AjaxResult updateChangeView() {
		entService.updateChangeView();
		return AjaxResult.success();
	}

	/**
	 *
	 * @Title: examineSave
	 * @author: LJX
	 * @date: 2020年7月22日 下午7:39:23
	 * @Description: 审批保存
	 * @param:  ent
	 * @param:  model
	 * @return: ResponseResult
	 * @throws
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:edit')")
	@RequestMapping(value = "/examine/save", method = RequestMethod.POST)
	public AjaxResult examineSave(@RequestBody Ent ent) {
		try {
			entService.examineSave(ent);
		} catch (Exception e) {
			return AjaxResult.error("保存失败，错误如下：" + e.getMessage());
		}
		return AjaxResult.success();
	}

	/**
	 *
	 * @title: frozen
	 * @author: lxy
	 * @date: 2021年5月17日13:44:01
	 * @description: 主体冻结
	 * @param: id
	 * @param: request
	 * @param: response
	 * @param: @throws Exception
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "frozen/{id}", method = RequestMethod.GET)
	public AjaxResult frozen(@PathVariable(value = "id") String id) {
		try {
			Ent ent=entService.selectEntById(Long.valueOf(id));
			entService.frozen(ent);
		} catch(Exception e) {
			return AjaxResult.error("冻结失败，错误如下：" + e.getMessage());
		}
		return AjaxResult.success();
	}
	/**
	 *
	 * @title: cancelFrozen
	 * @author: lxy
	 * @date: 2021年5月17日13:44:01
	 * @description: 主体取消冻结
	 * @param: id
	 * @param: request
	 * @param: response
	 * @param: @throws Exception
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "cancelFrozen/{id}", method = RequestMethod.GET)
	public AjaxResult cancelFrozen(@PathVariable(value = "id") String id){
		try {
			Ent ent=entService.selectEntById(Long.valueOf(id));
			entService.cancelFrozen(ent);
		} catch(Exception e) {
			return AjaxResult.error("冻结失败，错误如下：" + e.getMessage());
		}
		return AjaxResult.success();
	}


	/**
	 * 企业信息导出
	 * @Title: exportExcel
	 * @Author: HeXingChen
	 * @Date: 2022年06月07日 11:46
	 * @param ent
	 * @param response
	 * @return: String
	 */
//	@PreAuthorize("@ss.hasPermi('argi:ent:query')")
	@Log(title = "导出主体信息列表", businessType = BusinessType.EXPORT)
	@PostMapping("/exportExcel")
	public AjaxResult exportExcel(Ent ent, HttpServletResponse response) {
		try {
			ent.setFrozenFlag(Global.NO);
			entService.exportExcel(ent,response);
			return AjaxResult.success();
		} catch (Exception e) {
			AjaxResult.error("导出失败！失败信息：" + e.getMessage());
		}
		return AjaxResult.success();

	}

	/**
	 * @title: findAreaCertificateDataListPage
	 * @author: lxy
	 * @date: 2022年7月14日18:11:18
	 * @description: 行政区划合格证开证主体查询数据列表
	 * @param: [ent, request, response]
	 * @return: com.thinkgem.jeesite.common.persistence.ResponseResult
	 */
	@RequestMapping(value = "find/area/certificate/data/list/page", method = RequestMethod.POST)
	public TableDataInfo findAreaCertificateDataListPage(@RequestBody Ent ent){
		startPage();
		List<Ent> list = entService.findAreaCertificateDataListPage(ent);
		return getDataTable(list);
	}

	/**
	 * 根据手机号与身份证号查询信息
	 *
	 * <AUTHOR>
	 * @date 2021年09月17日 10:40:01
	 * @param num
	 * @return com.thinkgem.jeesite.common.persistence.ResponseResult
	 */
	@RequestMapping(value = "getByNum",method = RequestMethod.POST)
	public AjaxResult getByNum(@RequestParam String num){
		SysUser member = userService.getByPhone(num);
		if(null != member){
			Ent ent = entService.getEntByTableId(String.valueOf(member.getUserId()));
			if(null != ent){
				return AjaxResult.success(ent);
			}else {
				return AjaxResult.error("未查询到用户");
			}
		}else{
			return AjaxResult.error("未查询到用户");
		}
	}
	/**
	 * 校验身份证号唯一
	 *
	 * <AUTHOR>
	 * @date 2021年09月15日 18:35:39
	 * @param idCard
	 * @return com.thinkgem.jeesite.common.persistence.ResponseResult
	 */
	@RequestMapping(value = "validateIdCard",method = RequestMethod.POST)
	public AjaxResult validateIdCardOnly(@RequestParam String idCard,@RequestParam String userId){
		Ent ent = new Ent();
		ent.setCardNo(idCard);
		Integer num = entService.validateIdCard(ent);
		if(0 == num){
			return AjaxResult.error("");
		}else {
			ent = entService.getEntByIdCard(idCard);
//			ProductSiteInfo productSiteInfo = productSiteInfoService.getByUserId(userId);// 获取登陆人 所在区县地址  从最后一级往前比较
//			String code = "";
//			if(StringUtils.isNotEmpty(productSiteInfo.getVillage())){
//				code = productSiteInfo.getVillage();
//				if(code.equals(ent.getVillage())){
//					ent.setRegionFlag(true);
//				}else {
//					ent.setRegionFlag(false);
//				}
//			}else if(StringUtils.isNotEmpty(productSiteInfo.getTown())){
//				code = productSiteInfo.getTown();
//				if(code.equals(ent.getTown())){
//					ent.setRegionFlag(true);
//				}else {
//					ent.setRegionFlag(false);
//				}
//			}else if(StringUtils.isNotEmpty(productSiteInfo.getCounty())){
//				code = productSiteInfo.getCounty();
//				if(code.equals(ent.getCounty())){
//					ent.setRegionFlag(true);
//				}else {
//					ent.setRegionFlag(false);
//				}
//			}else if(StringUtils.isNotEmpty(productSiteInfo.getCity())){
//				code = productSiteInfo.getCity();
//				if(code.equals(ent.getCity())){
//					ent.setRegionFlag(true);
//				}else {
//					ent.setRegionFlag(false);
//				}
//			}else {
//				code = productSiteInfo.getProvince();
//				if(code.equals(ent.getProvince())){
//					ent.setRegionFlag(true);
//				}else {
//					ent.setRegionFlag(false);
//				}
//			}
			List<EntDetail> entDetailList = entDetailService.findDetailList(ent.getId());
			if(CollectionUtils.isNotEmpty(entDetailList)){
				ent.setEntDetailList(entDetailList);
			}
			return AjaxResult.success(ent);
		}
	}
	/**
	 *
	 * @title: findDetailList
	 * @author: wanghe
	 * @date: 2021年9月15日 上午15:20:11
	 * @param:  entId
	 * @description: 子表信息查询
	 * @return: ResponseResult
	 * @throws
	 */
	@RequestMapping(value = "findDetailList", method = RequestMethod.POST)
	public AjaxResult findDetailList(@RequestParam Long entId){
		return AjaxResult.success(entDetailService.findDetailList(entId));
	}
	/**
	 *
	 * @title: getEntInfo
	 * @author: 胡志国
	 * @date: 2020-8-30
	 * @description: 获取检查主体信息和产品分类信息
	 * @param: @param ent
	 * @return: ResponseResult
	 */
	@RequestMapping(value = "getEntAndProductInfo", method = RequestMethod.POST)
	public AjaxResult getEntAndProductInfo(@RequestBody Ent ent) throws Exception {
		if (ent.getId() == null){
			return AjaxResult.error("主体id不能为空！");
		}
		try {
			// 获取企业信息
			Ent entInfo = entService.selectEntById(ent.getId());
			if(entInfo == null) {
				throw new Exception("企业信息不存在!");
			}
			Map<String, Object> map = productService.getProductNameAndCertificationNameMap(String.valueOf(ent.getId()));
			map.put("ent", entInfo);
			return AjaxResult.success(map);
		} catch (Exception e) {
			return AjaxResult.error("获取数据失败，错误如下：" + e.getMessage());
		}
	}

	/**
	 * 获取监管主体列表
	 * <AUTHOR>
	 * @date 2025/7/9 16:27
	 * @param dept
	 * @since 1.0.0
	 * @return com.jkr.framework.web.domain.AjaxResult
	 */
	@PreAuthorize("@ss.hasPermi('argi:ent:list')")
	@GetMapping("/supervisionList")
	public TableDataInfo supervisionList(SysDept dept) {
		startPage();
		dept.setSupervisionFlag(Constants.YES);
		List<SysDept> depts = deptService.selectEntDeptList(dept);
		return getDataTable(depts);
	}

	@PreAuthorize("@ss.hasPermi('argi:ent:query')")
	@GetMapping(value = "/getSupervisionInfo/{deptId}")
	public AjaxResult getSupervisionInfo(@PathVariable Long deptId) {
		return AjaxResult.success(deptService.selectDeptById(deptId));
	}


	//获取同步数据
	@GetMapping(value = "/getSyncData")
	public AjaxResult getSyncData( Date date) {
		return AjaxResult.success(entService.getSyncData(date));
	}
}
