package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Guide;

/**
 * 农业技术指导Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface GuideMapper extends BaseMapper<Guide>{
	/**
	 * 查询农业技术指导
	 *
	 * @param id 农业技术指导主键
	 * @return 农业技术指导
	 */
	public Guide selectGuideById(Long id);

	/**
	 * 查询农业技术指导列表
	 *
	 * @param guide 农业技术指导
	 * @return 农业技术指导集合
	 */
	public List<Guide> selectGuideList(Guide guide);

	/**
	 * 新增农业技术指导
	 *
	 * @param guide 农业技术指导
	 * @return 结果
	 */
	public int insertGuide(Guide guide);

	/**
	 * 修改农业技术指导
	 *
	 * @param guide 农业技术指导
	 * @return 结果
	 */
	public int updateGuide(Guide guide);

	/**
	 * 删除农业技术指导
	 *
	 * @param id 农业技术指导主键
	 * @return 结果
	 */
	public int deleteGuideById(Long id);

	/**
	 * 批量删除农业技术指导
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteGuideByIds(Long[] ids);

	/**
	 * 批量逻辑删除农业技术指导
	 *
	 * @param  ids 农业技术指导主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过农业技术指导主键id逻辑删除信息
	 *
	 * @param  id 农业技术指导主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
