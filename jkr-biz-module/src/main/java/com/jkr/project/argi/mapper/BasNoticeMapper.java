package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.BasNotice;

/**
 * 信息发布Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface BasNoticeMapper extends BaseMapper<BasNotice>{
	/**
	 * 查询信息发布
	 *
	 * @param id 信息发布主键
	 * @return 信息发布
	 */
	public BasNotice selectBasNoticeById(Long id);

	/**
	 * 查询信息发布列表
	 *
	 * @param basNotice 信息发布
	 * @return 信息发布集合
	 */
	public List<BasNotice> selectBasNoticeList(BasNotice basNotice);

	/**
	 * 新增信息发布
	 *
	 * @param basNotice 信息发布
	 * @return 结果
	 */
	public int insertBasNotice(BasNotice basNotice);

	/**
	 * 修改信息发布
	 *
	 * @param basNotice 信息发布
	 * @return 结果
	 */
	public int updateBasNotice(BasNotice basNotice);

	/**
	 * 删除信息发布
	 *
	 * @param id 信息发布主键
	 * @return 结果
	 */
	public int deleteBasNoticeById(Long id);

	/**
	 * 批量删除信息发布
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteBasNoticeByIds(Long[] ids);

	/**
	 * 批量逻辑删除信息发布
	 *
	 * @param  ids 信息发布主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过信息发布主键id逻辑删除信息
	 *
	 * @param  id 信息发布主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
