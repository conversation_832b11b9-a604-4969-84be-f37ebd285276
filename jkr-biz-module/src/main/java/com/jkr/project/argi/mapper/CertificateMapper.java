package com.jkr.project.argi.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jkr.project.argi.domain.Ent;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Certificate;
import org.apache.ibatis.annotations.Param;

/**
 * 合格证Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateMapper extends BaseMapper<Certificate>{
	/**
	 * 查询合格证
	 *
	 * @param id 合格证主键
	 * @return 合格证
	 */
	public Certificate selectCertificateById(Long id);

	/**
	 * 查询合格证列表
	 *
	 * @param certificate 合格证
	 * @return 合格证集合
	 */
	public List<Certificate> selectCertificateList(Certificate certificate);

	/**
	 * 新增合格证
	 *
	 * @param certificate 合格证
	 * @return 结果
	 */
	public int insertCertificate(Certificate certificate);

	/**
	 * 修改合格证
	 *
	 * @param certificate 合格证
	 * @return 结果
	 */
	public int updateCertificate(Certificate certificate);

	/**
	 * 删除合格证
	 *
	 * @param id 合格证主键
	 * @return 结果
	 */
	public int deleteCertificateById(Long id);

	/**
	 * 批量删除合格证
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateByIds(Long[] ids);

	/**
	 * 批量逻辑删除合格证
	 *
	 * @param  ids 合格证主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过合格证主键id逻辑删除信息
	 *
	 * @param  id 合格证主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	/**
	 * 小程序列表，精简字段
	 * @param entity
	 * @return
	 */
	List<Certificate> findListForWechat(Certificate entity);

	/**
	 * @Title updatePrintCount
	 * @Description 更新打印数量
	 * <AUTHOR>
	 * @Date 2020/7/21 16:58
	 * @Param entity
	 * @Return int
	 **/
	int updatePrintCount(Certificate Certificate);

	/**
	 *@title: searchNextNo
	 *@author: CaoBochun
	 *@date: 2020年06月18日 06时02分39秒
	 *@description: 查询最大编号的下一个
	 *@param: []
	 *@return: {@link Integer}
	 */
	Integer searchNextNo(@Param(value = "code") String code);

	/**
	 *
	 * @Title: getFrequencyByEntId
	 * @author: LJX
	 * @date: 2020年7月19日 下午2:57:37
	 * @Description: 根据企业ID获取打印合格证次数和数量
	 * @param:  ent
	 * @return: Map<String,Object>
	 * @throws
	 */
	Map<String, Object> getFrequencyByEntId(Ent ent);

	/**
	 *
	 * @Title: getFrequencyByCountyCode
	 * @author: LJX
	 * @date: 2020年7月19日 下午6:08:00
	 * @Description:根据企业县区code获取打印合格证次数和数量
	 * @param:  county （Certificate certificate 胡志国修改 20210608）
	 * @return: Map<String,Object>
	 * @throws
	 */
	Map<String, Object> getFrequencyByCountyCode(Certificate certificate);

	/**
	 *
	 * @Title: getSummaryInfo
	 * @author: lxy
	 * @date: 2020年7月19日 下午6:08:00
	 * @Description:获取汇总信息
	 * @param:  county
	 * @return: Map<String,Object>
	 * @throws
	 */
	public Map<String, Object> getSummaryInfo(@Param(value = "entId") String entId);

	/**
	 * @Title updateBlockchainId
	 * @Description //TODO 为合格证绑定区块链id
	 * <AUTHOR>
	 * @Date 2020/7/26 16:25
	 * @Param id
	 * @Param blockChainId
	 * @Return int
	 **/
	int updateBlockChainId(@Param(value = "id")String id,@Param(value = "blockChainId")String blockChainId);

	/**
	 *
	 * @title: findInvalidList
	 * @author: lxy
	 * @date: 2021年5月13日 上午11:24:11
	 * @description: 查询作废数据列表
	 * @param: Certificate
	 * @return: List<Certificate>
	 */
	public List<Certificate> findInvalidList(Certificate Certificate);

	/**
	 * @title:findPrintTotalNum
	 * @author:Lizhongyao
	 * @data:2021年09月11日 11:22:28
	 * @param:certificate
	 * @description 获取开具数量
	 * @return java.lang.Integer
	 **/
	Integer findPrintTotalNum(Certificate certificate);
}
