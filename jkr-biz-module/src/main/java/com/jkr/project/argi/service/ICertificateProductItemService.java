package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .CertificateProductItem;

/**
 * 合格证产品子表(组合品使用)Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateProductItemService {
	/**
	 * 查询合格证产品子表(组合品使用)
	 *
	 * @param id 合格证产品子表(组合品使用)主键
	 * @return 合格证产品子表(组合品使用)
	 */
	public CertificateProductItem selectCertificateProductItemById(Long id);

	/**
	 * 查询合格证产品子表(组合品使用)列表
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 合格证产品子表(组合品使用)集合
	 */
	public List<CertificateProductItem> selectCertificateProductItemList(CertificateProductItem certificateProductItem);

	/**
	 * 新增合格证产品子表(组合品使用)
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 结果
	 */
	public int insertCertificateProductItem(CertificateProductItem certificateProductItem);

	/**
	 * 修改合格证产品子表(组合品使用)
	 *
	 * @param certificateProductItem 合格证产品子表(组合品使用)
	 * @return 结果
	 */
	public int updateCertificateProductItem(CertificateProductItem certificateProductItem);

	/**
	 * 批量删除合格证产品子表(组合品使用)
	 *
	 * @param ids 需要删除的合格证产品子表(组合品使用)主键集合
	 * @return 结果
	 */
	public int deleteCertificateProductItemByIds(List<Long> ids);

	/**
	 * 删除合格证产品子表(组合品使用)信息
	 *
	 * @param id 合格证产品子表(组合品使用)主键
	 * @return 结果
	 */
	public int deleteCertificateProductItemById(Long id);

}
