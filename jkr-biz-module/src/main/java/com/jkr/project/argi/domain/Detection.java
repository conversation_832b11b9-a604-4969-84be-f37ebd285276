package com.jkr.project.argi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import java.util.List;

/**
 * 与快检系统对接对象 bas_detection
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
        @Data
        @EqualsAndHashCode(callSuper = true)
        @TableName("bas_detection")
		public class Detection extends BaseModel
		{
		private static final long serialVersionUID = 1L;

				/** 快检系统返回的编号 */
				@Excel(name = "快检系统返回的编号")
		private String sampleNo;

				/** 快检系统返回的二维码图片链接 */
				@Excel(name = "快检系统返回的二维码图片链接")
		private String sampleQrcodeUrl;

				/** 商品名称 */
				@Excel(name = "商品名称")
		private String goodsName;

				/** 检测编号，为样品编号加3位流水号，不可重复。 */
				@Excel(name = "检测编号，为样品编号加3位流水号，不可重复。")
		private String recordNo;

				/** 检测项目代码，请查阅代码集：检测项目 */
				@Excel(name = "检测项目代码，请查阅代码集：检测项目")
		private String testItemCode;

				/** 检测项目 */
				@Excel(name = "检测项目")
		private String testItemLabel;

				/** 检测时间 */
				@JsonFormat(pattern = "yyyy-MM-dd")
				@Excel(name = "检测时间", width = 30, dateFormat = "yyyy-MM-dd")
		private Date testDate;

				/** 检测结果，(0:合格1:不合格) */
				@Excel(name = "检测结果，(0:合格1:不合格)")
		private String testResult;

				/** 检测数值及单位 */
				@Excel(name = "检测数值及单位")
		private String testNumericalUnit;

				/** 限量标准 */
				@Excel(name = "限量标准")
		private String limitStandard;

				/** 检测人 */
				@Excel(name = "检测人")
		private String testMan;

				/** 仪器名称 */
				@Excel(name = "仪器名称")
		private String instrumentName;

				/** 设备号(设备唯一标识) */
				@Excel(name = "设备号(设备唯一标识)")
		private String instrumentNo;

				/** 合格范围 */
				@Excel(name = "合格范围")
		private String qualifiedRange;

				/** 拉取的整体json数据 */
				@Excel(name = "拉取的整体json数据")
		private String jsonData;

				/** 上次更新时间 */
				@JsonFormat(pattern = "yyyy-MM-dd")
				@Excel(name = "上次更新时间", width = 30, dateFormat = "yyyy-MM-dd")
		private Date lastModify;

				/** 排序 */
				@Excel(name = "排序")
		private Long sort;

				/** 启用状态 0:不可用 1:可用 */
				@Excel(name = "启用状态 0:不可用 1:可用")
		private Integer status;

            /** 主键集合 */
            private List<Long> ids;
}
