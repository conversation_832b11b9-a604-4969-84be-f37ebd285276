package com.jkr.project.argi.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import com.jkr.project.system.domain.SysFile;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 主体信息变更对象 bas_ent_change
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_ent_change")
public class EntChange extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Excel(name = "主体id")
    private String entId;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 主体类型(0:种植；1:养殖)
     */
    @Excel(name = "主体类型(0:种植；1:养殖)")
    private String businessType;

    /**
     * 主体性质(0:企业；1:个人)
     */
    @Excel(name = "主体性质(0:企业；1:个人)")
    private String entType;

    /**
     * 主体类别
     */
    @Excel(name = "主体类别")
    private String mainType;

    /**
     * 身份标识：0-生产;1-收购;2-生产收购
     */
    @Excel(name = "身份标识：0-生产;1-收购;2-生产收购")
    private String identityType;

    /**
     * 养殖分类 (0:牧业 1:渔业)
     */
    @Excel(name = "养殖分类 (0:牧业 1:渔业)")
    private String farmType;

    /**
     * 统一社会信用代码
     */
    @Excel(name = "统一社会信用代码")
    private String socialCode;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String cardNo;

    /**
     * 法人
     */
    @Excel(name = "法人")
    private String legalPerson;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contacts;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String contactsPhone;

    /**
     * 省
     */
    @Excel(name = "省")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 县区
     */
    @Excel(name = "县区")
    private String county;

    /**
     * 地址(省市县)
     */
    @Excel(name = "地址(省市县)")
    private String address;

    /**
     * 详细地址
     */
    @Excel(name = "详细地址")
    private String detail;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String lng;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String lat;

    /**
     * 企业介绍
     */
    @Excel(name = "企业介绍")
    private String companyIntroduction;

    /**
     * 荣誉简介
     */
    @Excel(name = "荣誉简介")
    private String entHonor;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date submitDate;

    /**
     * 审核状态（0：待审核；1：通过,-1驳回）
     */
    @Excel(name = "审核状态", readConverterExp = "0=：待审核；1：通过,-1驳回")
    private String examineStatus;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    private String examineMan;

    /**
     * 审核意见
     */
    @Excel(name = "审核意见")
    private String examineOpinion;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date examineDate;

    /**
     * 电子签名
     */
    @Excel(name = "电子签名")
    private String autograph;

    /**
     * 基础信息采集对接标示(0:否 1:是)
     */
    @Excel(name = "基础信息采集对接标示(0:否 1:是)")
    private String basicFlag;

    /**
     * 基础信息录入标示(0:否 1:是)
     */
    @Excel(name = "基础信息录入标示(0:否 1:是)")
    private String basicEnterFlag;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private List<SysFile> fileList = Lists.newArrayList();
    @TableField(exist = false)
    private String loginAreaCode;

    @TableField(exist = false)
    private Date beginExamineDate;    //开始审核时间
    @TableField(exist = false)
    private Date endExamineDate;        //结束审核时间

    @TableField(exist = false)
    private Date beginSubmitDate;    //提交开始时间
    @TableField(exist = false)
    private Date endSubmitDate;        //提交结束时间
}
