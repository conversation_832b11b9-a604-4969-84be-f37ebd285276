package com.jkr.project.argi.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.domain .EntChange;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主体信息变更Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IEntChangeService extends IService<EntChange> {
	/**
	 * 查询主体信息变更
	 *
	 * @param id 主体信息变更主键
	 * @return 主体信息变更
	 */
	public EntChange selectEntChangeById(Long id);

	/**
	 * 查询主体信息变更列表
	 *
	 * @param entChange 主体信息变更
	 * @return 主体信息变更集合
	 */
	public List<EntChange> selectEntChangeList(EntChange entChange);

	/**
	 * 新增主体信息变更
	 *
	 * @param entChange 主体信息变更
	 * @return 结果
	 */
	public int insertEntChange(EntChange entChange);

	/**
	 * 修改主体信息变更
	 *
	 * @param entChange 主体信息变更
	 * @return 结果
	 */
	public int updateEntChange(EntChange entChange);

	/**
	 * 批量删除主体信息变更
	 *
	 * @param ids 需要删除的主体信息变更主键集合
	 * @return 结果
	 */
	public int deleteEntChangeByIds(List<Long> ids);

	/**
	 * 删除主体信息变更信息
	 *
	 * @param id 主体信息变更主键
	 * @return 结果
	 */
	public int deleteEntChangeById(Long id);

	/**
	 * 获取通过集合
	 * <AUTHOR>
	 * @date 2025/5/22 11:44
	 * @param id
	 * @since 1.0.0
	 * @return void
	 */
	List<EntChange> findPassList(Long id);

	void updateBasicEnterFinish(String id);

	EntChange getUnfinished(EntChange entChange);

	EntChange getChange(EntChange entChange);

	void examineSave(EntChange entChange);

	EntChange saveEntChange(EntChange entChange);
}
