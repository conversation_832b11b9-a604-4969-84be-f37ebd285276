package com.jkr.project.argi.task;

import com.jkr.project.argi.constant.PublishStatusConstants;
import com.jkr.project.argi.domain.Guide;
import com.jkr.project.argi.service.DelayedPublishService;
import com.jkr.project.argi.service.IGuideService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Guide发布状态处理器
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Component
@Slf4j
public class GuidePublishHandler {

    private static final String GUIDE_PUBLISH_QUEUE = "guide:publish:queue";

    @Autowired
    private DelayedPublishService delayedPublishService;

    @Lazy
    @Autowired
    private IGuideService guideService;

    /**
     * 添加Guide到发布队列
     *
     * @param guide Guide对象
     */
    public void addToPublishQueue(Guide guide) {
        if (guide != null && guide.getPublishTime() != null) {
            delayedPublishService.addToQueue(GUIDE_PUBLISH_QUEUE, guide.getId(), guide.getPublishTime());
        }
    }

    /**
     * 从发布队列中移除Guide
     *
     * @param guideId Guide ID
     */
    public void removeFromPublishQueue(Long guideId) {
        delayedPublishService.removeFromQueue(GUIDE_PUBLISH_QUEUE, guideId);
    }

    /**
     * 定时检查并处理待发布的Guide
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void handlePublishQueue() {
        delayedPublishService.processQueue(GUIDE_PUBLISH_QUEUE, this::publishGuide);
    }

    /**
     * 发布Guide的具体逻辑
     *
     * @param guideId Guide ID
     */
    private void publishGuide(Long guideId) {
        Guide guide = new Guide();
        guide.setId(guideId);
        guide.setStatus(PublishStatusConstants.STATUS_PUBLISHED);
        guideService.updateGuide(guide);
    }
}