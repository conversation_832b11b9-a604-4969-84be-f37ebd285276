package com.jkr.project.argi.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jkr.project.argi.domain .EntDetail;

/**
 * 主体信息子表Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IEntDetailService extends IService<EntDetail> {
	/**
	 * 查询主体信息子表
	 *
	 * @param id 主体信息子表主键
	 * @return 主体信息子表
	 */
	public EntDetail selectEntDetailById(Long id);

	/**
	 * 查询主体信息子表列表
	 *
	 * @param entDetail 主体信息子表
	 * @return 主体信息子表集合
	 */
	public List<EntDetail> selectEntDetailList(EntDetail entDetail);

	/**
	 * 新增主体信息子表
	 *
	 * @param entDetail 主体信息子表
	 * @return 结果
	 */
	public int insertEntDetail(EntDetail entDetail);

	/**
	 * 修改主体信息子表
	 *
	 * @param entDetail 主体信息子表
	 * @return 结果
	 */
	public int updateEntDetail(EntDetail entDetail);

	/**
	 * 批量删除主体信息子表
	 *
	 * @param ids 需要删除的主体信息子表主键集合
	 * @return 结果
	 */
	public int deleteEntDetailByIds(List<Long> ids);

	/**
	 * 删除主体信息子表信息
	 *
	 * @param id 主体信息子表主键
	 * @return 结果
	 */
	public int deleteEntDetailById(Long id);
	public void  deleteByEntId(String entId);
	public void saveEntBatch(List<EntDetail> entDetailList, String entId) ;

    List<EntDetail> findDetailList(Long id);
}
