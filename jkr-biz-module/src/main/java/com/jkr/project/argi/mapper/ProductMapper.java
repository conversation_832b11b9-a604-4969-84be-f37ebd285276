package com.jkr.project.argi.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Product;
import org.apache.ibatis.annotations.Param;

/**
 * 产品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ProductMapper extends BaseMapper<Product>{
	/**
	 * 查询产品
	 *
	 * @param id 产品主键
	 * @return 产品
	 */
	public Product selectProductById(Long id);

	/**
	 * 查询产品列表
	 *
	 * @param product 产品
	 * @return 产品集合
	 */
	public List<Product> selectProductList(Product product);

	/**
	 * 新增产品
	 *
	 * @param product 产品
	 * @return 结果
	 */
	public int insertProduct(Product product);

	/**
	 * 修改产品
	 *
	 * @param product 产品
	 * @return 结果
	 */
	public int updateProduct(Product product);

	/**
	 * 删除产品
	 *
	 * @param id 产品主键
	 * @return 结果
	 */
	public int deleteProductById(Long id);

	/**
	 * 批量删除产品
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteProductByIds(Long[] ids);

	/**
	 * 批量逻辑删除产品
	 *
	 * @param  ids 产品主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过产品主键id逻辑删除信息
	 *
	 * @param  id 产品主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	void updateInspectionResultById(JSONArray productList);

	List<Product> findProductList(@Param("entId") String entId, @Param("mixFlag")String mixFlag);

	List<Product> findProductPage(Product product);

	Map<String,  Object> findCountGroupName(Product product);

	Map<String,Object> findProductCount(@Param("name") String name,@Param("userId") String userId);

	List<Product> findListByUserId(Product entity);

	public int updatePrintAmount(@Param("id") String id,@Param("amount") Integer amount);

	public int updateInvalidPrintAmount(@Param("productId") String productId,@Param("entId") String entId);


    List<Product> getSyncData(@Param("date") Date date);
}
