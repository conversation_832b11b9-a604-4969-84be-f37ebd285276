package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateNo;
import com.jkr.project.argi.service.ICertificateNoService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 合格证流水Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateNo")
public class CertificateNoController extends BaseController {
	@Autowired
	private ICertificateNoService certificateNoService;

/**
 * 查询合格证流水列表
 */
@PreAuthorize("@ss.hasPermi('argi:certificateNo:list')")
@GetMapping("/list")
	public TableDataInfo list(CertificateNo certificateNo) {
		startPage();
		List<CertificateNo> list = certificateNoService.selectCertificateNoList(certificateNo);
		return getDataTable(list);
	}

	/**
	 * 导出合格证流水列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateNo:export')")
	@Log(title = "导出合格证流水列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, CertificateNo certificateNo) {
		List<CertificateNo> list = certificateNoService.selectCertificateNoList(certificateNo);
		ExcelUtil<CertificateNo> util = new ExcelUtil<CertificateNo>(CertificateNo. class);
		util.exportExcel(response, list, "合格证流水数据");
	}

	/**
	 * 获取合格证流水详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateNo:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(certificateNoService.selectCertificateNoById(id));
	}

	/**
	 * 新增合格证流水
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateNo:add')")
	@Log(title = "新增合格证流水", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody CertificateNo certificateNo) {
		return toAjax(certificateNoService.insertCertificateNo(certificateNo));
	}

	/**
	 * 修改合格证流水
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateNo:edit')")
	@Log(title = "修改合格证流水", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody CertificateNo certificateNo) {
		return toAjax(certificateNoService.updateCertificateNo(certificateNo));
	}

	/**
	 * 删除合格证流水
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateNo:remove')")
	@Log(title = "删除合格证流水", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(certificateNoService.deleteCertificateNoById(id));
	}

	/**
	 * 批量删除合格证流水
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateNo:batchRemove')")
	@Log(title = "批量删除合格证流水", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody CertificateNo certificateNo) {
		return toAjax(certificateNoService.deleteCertificateNoByIds(certificateNo.getIds()));
	}
}
