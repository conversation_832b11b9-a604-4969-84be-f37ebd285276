package com.jkr.project.argi.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.constant.PublishStatusConstants;
import com.jkr.project.argi.task.GuidePublishHandler;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.impl.SysFileServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.GuideMapper;
import com.jkr.project.argi.domain.Guide;
import com.jkr.project.argi.service.IGuideService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 农业技术指导Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class GuideServiceImpl implements IGuideService {
    @Autowired
    private GuideMapper guideMapper;

    @Autowired
    private GuidePublishHandler guidePublishHandler;
    @Autowired
    private SysFileServiceImpl fileService;

    /**
     * 查询农业技术指导
     *
     * @param id 农业技术指导主键
     * @return 农业技术指导
     */
    @Override
    public Guide selectGuideById(Long id) {
        Guide guide = guideMapper.selectGuideById(id);
        if (guide != null) {
            SysFile sysFile = new SysFile();
            sysFile.setTableId(guide.getId());
            List<SysFile> fileList = fileService.findUrlsFileList(sysFile);
            guide.setCoverImageFiles(fileList);
        }
        return guideMapper.selectGuideById(id);
    }

    /**
     * 查询农业技术指导列表
     *
     * @param guide 农业技术指导
     * @return 农业技术指导
     */
    @Override
    public List<Guide> selectGuideList(Guide guide) {
        return guideMapper.selectGuideList(guide);
    }

    /**
     * 新增农业技术指导
     *
     * @param guide 农业技术指导
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int insertGuide(Guide guide) {
        guide.insertInit(SecurityUtils.getLoginUser().getUsername());
        
        // 设置初始状态为未发布
        if (guide.getStatus() == null) {
            guide.setStatus(PublishStatusConstants.STATUS_UNPUBLISHED);
        }
        
        int rows = guideMapper.insert(guide);
        
        // 如果设置了发布时间且大于当前时间，添加到发布队列
        if (guide.getPublishTime() != null && guide.getPublishTime().isAfter(LocalDateTime.now())) {
            guidePublishHandler.addToPublishQueue(guide);
        }
        //保存图片信息
        updateSysFile(guide);

        return rows;
    }

    /**
     * 修改农业技术指导
     *
     * @param guide 农业技术指导
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int updateGuide(Guide guide) {
        updateSysFile(guide);
        return guideMapper.updateById(guide);
    }


    /**
     * 保存图片
     * @param guide
     */
    private void updateSysFile(Guide guide) {
        //保存图片信息
        if (CollectionUtil.isNotEmpty(guide.getCoverImageFiles())) {
            guide.getCoverImageFiles().forEach(coverImageFile -> {
                coverImageFile.setTableId(guide.getId());
                fileService.updateSysFile(coverImageFile);
            });
        }
    }

    /**
     * 批量删除农业技术指导
     *
     * @param ids 需要删除的农业技术指导主键
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int deleteGuideByIds(List<Long> ids) {
        return guideMapper.logicRemoveByIds(ids);
        //return guideMapper.deleteGuideByIds(ids);
    }

    /**
     * 删除农业技术指导信息
     *
     * @param id 农业技术指导主键
     * @return 结果
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public int deleteGuideById(Long id) {
        return guideMapper.logicRemoveById(id);
        //return guideMapper.deleteGuideById(id);
    }

}
