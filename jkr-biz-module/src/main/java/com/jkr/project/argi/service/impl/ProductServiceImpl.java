package com.jkr.project.argi.service.impl;

import java.util.*;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.yitter.idgen.YitIdHelper;
import com.google.common.base.Joiner;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.*;
import com.jkr.project.argi.domain.Ent;
import com.jkr.project.argi.domain.ProductItem;
import com.jkr.project.argi.domain.ProductStorage;
import com.jkr.project.argi.service.IEntService;
import com.jkr.project.argi.service.IProductItemService;
import com.jkr.project.argi.service.IProductStorageService;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ProductMapper;
import com.jkr.project.argi.domain.Product;
import com.jkr.project.argi.service.IProductService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ProductServiceImpl implements IProductService {

	@Autowired
	private ProductMapper productMapper;
	@Autowired
	private IProductStorageService productStorageService;
	@Autowired
	private IProductItemService productItemService;
	@Autowired
	private ISysFileService fileService;
	@Lazy
	@Autowired
	private IEntService entService;
	@Value("${product.reBuyProduct.url}")
	private String productReBuyProductUrl;
	@Value("${source.checkPhoneNum.url}")
	private String sourceCheckPhoneNumUrl;

	/**
	 * 查询产品
	 *
	 * @param id 产品主键
	 * @return 产品
	 */
	@Override
	public Product selectProductById(Long id) {
		Product product = productMapper.selectProductById(id);
		SysFile attachment = new SysFile();
		attachment.setTableName("bas_product");
		//获取附件信息
		attachment.setTableId(product.getId());
		List<SysFile> attachmentList = fileService.findUrlsFileList(attachment);
		product.setFileList(attachmentList);
		//获取保质信息
		product.setProductStorageList(productStorageService.findListByProductId(product.getId()+""));
		//获取组合品子产品信息
		if(EnumProperty.MixFlagEnum.FLAG_1.getKey().equals(product.getMixFlag())){
			product.setProductItemList(productItemService.findListByProductId(product.getId()+""));
		}
		return product;
	}

	/**
	 * 查询产品列表
	 *
	 * @param product 产品
	 * @return 产品
	 */
	@Override
	public List<Product> selectProductList(Product product) {
		return productMapper.selectProductList(product);
	}

	@Override
	public List<Product> findProductPage(Product product) {
		if (entService.selectEntById(SecurityUtils.getDeptId()) != null) {
			product.setEntId(SecurityUtils.getDeptId()+"");
			return productMapper.selectProductList(product);
		}
		return null;
	}

	/**
	 * 新增产品
	 *
	 * @param product 产品
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertProduct(Product product) {
		product.setAddDate(new Date());
		product.setEntId(SecurityUtils.getDeptId()+"");
		product.insertInit(SecurityUtils.getLoginUser().getUsername());
		int flag = productMapper.insertProduct(product);
		if (CollUtil.isNotEmpty(product.getFileList())) {
			for (SysFile file : product.getFileList()) {
				file.setSourceType("0");
			}
		}
		fileService.insertSysFileListByTableId(product.getFileList(), product.getId());
		return flag;
	}

	/**
	 * 修改产品
	 *
	 * @param product 产品
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateProduct(Product product) {
		product.updateInit(SecurityUtils.getLoginUser().getUsername());
		int flag = productMapper.updateProduct(product);
		if (CollUtil.isNotEmpty(product.getFileList())) {
			for (SysFile file : product.getFileList()) {
				file.setSourceType("0");
			}
		}
		fileService.insertSysFileListByTableId(product.getFileList(), product.getId());
		return flag;
	}

	/**
	 * 批量删除产品
	 *
	 * @param ids 需要删除的产品主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductByIds(List<Long> ids) {
		return productMapper.logicRemoveByIds(ids);
		//return productMapper.deleteProductByIds(ids);
	}

	/**
	 * 删除产品信息
	 *
	 * @param id 产品主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductById(Long id) {
		return productMapper.logicRemoveById(id);
		//return productMapper.deleteProductById(id);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateInspectionResultById(JSONArray productList) {
		productMapper.updateInspectionResultById(productList);
	}

	@Override
	public Object findReBuyProductPage(Product product) {
		String reBuyProductUrl = productReBuyProductUrl;
		Map<String, String> requestUrlParam = new HashMap<>();

		requestUrlParam.put("pageNum", ""+product.getPageNum());
		requestUrlParam.put("pageSize",""+product.getPageSize());
		if(product == null){
			throw new ServiceException("缺少参数");
		}
		if(StringUtils.isEmpty(product.getShopId())){
			throw new ServiceException("缺少参数 shopId");
		}
		String shopIdEncry = Sm4Util.encryptEcb(product.getShopId());
		requestUrlParam.put("shopId", shopIdEncry);
		if(StringUtils.isNotEmpty(product.getName())){
			requestUrlParam.put("name", product.getName());
		}

		String codeParm = System.currentTimeMillis()+",sxkj0818";
		String headAuthorization = Base64.encode(codeParm);
		String httpResultStr = HttpClientUtil.doGet(reBuyProductUrl,requestUrlParam,headAuthorization);
		if(StringUtils.isNotEmpty(httpResultStr)){
			JSONObject jsonObject = JSON.parseObject(httpResultStr);
			return jsonObject;
		}
		return null;
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void saveProductNew(Product product) {
		product.insertInit(SecurityUtils.getLoginUser().getUsername());
		productMapper.insertProduct(product);
		if (CollUtil.isNotEmpty(product.getFileList())) {
			for (SysFile file : product.getFileList()) {
				file.setSourceType("0");
			}
		}
		fileService.insertSysFileListByTableId(product.getFileList(), product.getId());
		/**
		 * 修改业务：处理删除的保质存储记录
		 * 每条记录做多3条先临时处理
		 * 2021年10月13日16:47:17
		 * lxy
		 */
		if(null!=product.getProductStorageDelIdList() && !product.getProductStorageDelIdList().isEmpty()) {
			for(String id:product.getProductStorageDelIdList()) {
				ProductStorage productStorage = productStorageService.selectProductStorageById(Long.parseLong(id));
				productStorageService.deleteProductStorageById(productStorage.getId());
			}
		}
		/**
		 * 保质方式存储
		 * 2021年10月13日16:47:17
		 * lxy
		 */
		if(null!=product.getProductStorageList() && !product.getProductStorageList().isEmpty()) {
			for(ProductStorage item:product.getProductStorageList()) {
				item.setEntId(product.getEntId());
				item.setProductId(product.getId()+"");
				productStorageService.insertProductStorage(item);
			}
		}
		//组合品新增子表数据
		if (EnumProperty.MixFlagEnum.FLAG_1.getKey().equals(product.getMixFlag())){
			//删除产品子表
			productItemService.deleteByProductId(product.getId()+"");
			//批量新增
			if(null!=product.getProductItemList() && !product.getProductItemList().isEmpty()) {
				for(ProductItem item:product.getProductItemList()) {
					item.setProductId(product.getId()+"");
					item.setId(YitIdHelper.nextId());
				}
				productItemService.batchInsert(product.getProductItemList());
			}
		}
	}

	@Override
	public Product findProductAttachment(Product product) {
		product = selectProductById(product.getId());
		SysFile attachment = new SysFile();
		attachment.setTableId(product.getId());
		attachment.setTableName("bas_product");
		List<SysFile> attachmentList = fileService.findUrlsFileList(attachment);
		product.setFileList(attachmentList);
		return product;
	}

	@Override
	public List<Product> findProductList(String entId) {
		List<Product> list= productMapper.findProductList(entId, null);
		if(null!=list && !list.isEmpty()) {
			SysFile attachment = new SysFile();
			attachment.setTableName("bas_product");
			for(Product product:list) {
				//获取附件信息
				attachment.setTableId(product.getId());
				List<SysFile> attachmentList = fileService.findUrlsFileList(attachment);
				product.setFileList(attachmentList);
				//获取保质信息
				product.setProductStorageList(productStorageService.findListByProductId(product.getId()+""));
			}
		}
		return list;
	}

	@Override
	public List<Product> findInspectionList() {
		Ent ent = entService.selectEntById(SecurityUtils.getDeptId());
		if(null == ent){
			return new ArrayList<>();
		}
		List<Product> list = productMapper.findProductList(ent.getId()+"", null);
		return list;
	}

	@Override
	public Map<String, Object> getProductNameAndCertificationNameMap(String entId) {
		// 获取产品名称信息
		Map<String, String> productNameMap = new HashMap<String, String>();			// 产品名称map
		Map<String, String> certificationNameMap = new HashMap<String, String>();	// 产品认证名称map
		List<Product> productList = productMapper.findProductList(entId, null);						// 获取企业产品信息
		String productCertificationName = null;			// 产品认证名称

		for(Product product : productList) {
			productNameMap.put(product.getName(), product.getName());	// 产品名称
			productCertificationName = product.getProductCertificationName();	//获取产品认证名称
			if(productCertificationName != null && !productCertificationName.equals("")) {
				String[] certificationNames = productCertificationName.split(",");
				for(String certificationName : certificationNames) {
					certificationNameMap.put(certificationName, certificationName);
				}
			}
		}
		String productName = Joiner.on(",").join(productNameMap.values());
		String certificationName = Joiner.on(",").join(certificationNameMap.values());
		Map<String, Object> map = new HashMap<>();
		map.put("productName", productName);
		map.put("certificationName", certificationName);
		return map;
	}

	@Override
	public Map<String, Object> findCountGroupName(Product entity) {
		entity.setUserId(SecurityUtils.getLoginUser().getUserId()+"");
		return productMapper.findCountGroupName(entity);
	}

	@Override
	public Map<String, Object> findProductCount(String name) {
		return productMapper.findProductCount(name, SecurityUtils.getLoginUser().getUserId()+"");
	}

	@Override
	public List<Product> findListByUserId(Product product) {
		return productMapper.findListByUserId(product);
	}

	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public Integer updatePrintAmount(String id, Integer amount) {
		if(StringUtils.isBlank(id) || null == amount) {
			return null;
		}
		return productMapper.updatePrintAmount(id, amount);
	}

	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public Integer updateValidPrintAmount(String productId, String entId) {
		if(StringUtils.isBlank(productId) || null == productId) {
			return null;
		}
		if(StringUtils.isBlank(entId) || null == entId) {
			return null;
		}
		return productMapper.updateInvalidPrintAmount(productId, entId);
	}

	@Override
	public boolean checkPhoneNumOpenShopCart(String phoneNum) {
		if(StringUtils.isEmpty(phoneNum)){
			throw new ServiceException("缺少参数 phoneNum");
		}
		String phoneNumEncrypt = Sm4Util.encryptEcb(phoneNum);
		String phoneCheckUrl= sourceCheckPhoneNumUrl;
		phoneCheckUrl+="?providerNumber="+phoneNumEncrypt;
		String httpResultStr = HttpClientUtil.doGet(phoneCheckUrl);
		if(StringUtils.isNotEmpty(httpResultStr)){
			JSONObject jsonObject = JSON.parseObject(httpResultStr);
			if(jsonObject!=null){
				String code = jsonObject.getString("code");
				if("200".equals(code)){
					boolean data = jsonObject.getBoolean("data");
					if(data){
						return true;
					}
				}
			}

		}
		return false;
	}

	@Override
	public List<Product> findProductSingleList() {
		Ent ent = entService.selectEntById(SecurityUtils.getDeptId());
		if(null == ent){
			return new ArrayList<>();
		}
		List<Product> list = productMapper.findProductList(ent.getId()+"", "0");
		return list;
	}

	@Override
	public List<Product> getSyncData(Date date) {
		if (ObjectUtil.isNull(date)) {
			return List.of();
		}
		return productMapper.getSyncData(date);
	}
}
