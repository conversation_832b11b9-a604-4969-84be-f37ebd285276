package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateConfigDevice;
import org.apache.ibatis.annotations.Param;

/**
 * 电子合格证配置-设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateConfigDeviceMapper extends BaseMapper<CertificateConfigDevice>{
	/**
	 * 查询电子合格证配置-设备
	 *
	 * @param id 电子合格证配置-设备主键
	 * @return 电子合格证配置-设备
	 */
	public CertificateConfigDevice selectCertificateConfigDeviceById(Long id);

	/**
	 * 查询电子合格证配置-设备列表
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 电子合格证配置-设备集合
	 */
	public List<CertificateConfigDevice> selectCertificateConfigDeviceList(CertificateConfigDevice certificateConfigDevice);

	/**
	 * 新增电子合格证配置-设备
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 结果
	 */
	public int insertCertificateConfigDevice(CertificateConfigDevice certificateConfigDevice);

	/**
	 * 修改电子合格证配置-设备
	 *
	 * @param certificateConfigDevice 电子合格证配置-设备
	 * @return 结果
	 */
	public int updateCertificateConfigDevice(CertificateConfigDevice certificateConfigDevice);

	/**
	 * 删除电子合格证配置-设备
	 *
	 * @param id 电子合格证配置-设备主键
	 * @return 结果
	 */
	public int deleteCertificateConfigDeviceById(Long id);

	/**
	 * 批量删除电子合格证配置-设备
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateConfigDeviceByIds(Long[] ids);

	/**
	 * 批量逻辑删除电子合格证配置-设备
	 *
	 * @param  ids 电子合格证配置-设备主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过电子合格证配置-设备主键id逻辑删除信息
	 *
	 * @param  id 电子合格证配置-设备主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public int insertBatch(@Param("list")List<CertificateConfigDevice> list);

	public int deleteByCertificateConfigId(CertificateConfigDevice certificateConfigDevice);
}
