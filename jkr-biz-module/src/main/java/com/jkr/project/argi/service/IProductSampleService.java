package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .ProductSample;

/**
 * 产品样品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IProductSampleService {
	/**
	 * 查询产品样品
	 *
	 * @param id 产品样品主键
	 * @return 产品样品
	 */
	public ProductSample selectProductSampleById(Long id);

	/**
	 * 查询产品样品列表
	 *
	 * @param productSample 产品样品
	 * @return 产品样品集合
	 */
	public List<ProductSample> selectProductSampleList(ProductSample productSample);

	/**
	 * 新增产品样品
	 *
	 * @param productSample 产品样品
	 * @return 结果
	 */
	public int insertProductSample(ProductSample productSample);

	/**
	 * 修改产品样品
	 *
	 * @param productSample 产品样品
	 * @return 结果
	 */
	public int updateProductSample(ProductSample productSample);

	/**
	 * 批量删除产品样品
	 *
	 * @param ids 需要删除的产品样品主键集合
	 * @return 结果
	 */
	public int deleteProductSampleByIds(List<Long> ids);

	/**
	 * 删除产品样品信息
	 *
	 * @param id 产品样品主键
	 * @return 结果
	 */
	public int deleteProductSampleById(Long id);

	public ProductSample getProductSample(String entId,String productId,String inspectionSituation);

	public void updateNewestSample(String productInspectionId,String entId, String productId, String inspectionSituation);

	public void updateNewestQuickSample(String entId, String productId,String sampleNo);

}
