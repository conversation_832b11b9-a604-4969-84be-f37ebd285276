package com.jkr.project.argi.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jkr.common.enums.CertificateConfigTypeEnum;
import com.jkr.common.enums.EnumSubject;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.argi.domain.CertificateTemplate;
import com.jkr.project.argi.domain.DeviceParameter;
import com.jkr.project.argi.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateConfigMapper;
import com.jkr.project.argi.domain.CertificateConfig;
import org.springframework.transaction.annotation.Transactional;

/**
 * 电子合格证配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateConfigServiceImpl implements ICertificateConfigService {

	@Autowired
	private CertificateConfigMapper certificateConfigMapper;
	@Autowired
	private IDeviceParameterService deviceParameterService;
	@Autowired
	private ICertificateTemplateService certificateTemplateService;
	@Autowired
	private ICertificateConfigDeviceService certificateConfigDeviceService;
	@Autowired
	private ICertificateConfigTemplateService certificateConfigTemplateService;

	/**
	 * 查询电子合格证配置
	 *
	 * @param id 电子合格证配置主键
	 * @return 电子合格证配置
	 */
	@Override
	public CertificateConfig selectCertificateConfigById(Long id) {
		CertificateConfig certificateConfig = certificateConfigMapper.selectCertificateConfigById(id);
		String deviceTemplateIds = this.getDeviceTemplateIds(id.toString());
		certificateConfig.setCertificateTemplateIds(deviceTemplateIds);
		return certificateConfig;
	}

	/**
	 * 查询电子合格证配置列表
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 电子合格证配置
	 */
	@Override
	public List<CertificateConfig> selectCertificateConfigList(CertificateConfig certificateConfig) {
		return certificateConfigMapper.selectCertificateConfigList(certificateConfig);
	}

	/**
	 * 新增电子合格证配置
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateConfig(CertificateConfig certificateConfig) {
		certificateConfig.insertInit(SecurityUtils.getLoginUser().getUsername());
		int result = certificateConfigMapper.insertCertificateConfig(certificateConfig);

		String[] treeIds=certificateConfig.getCertificateTemplateIds().split(",");
		//获取配置的设备id
		List<String> deviceIdList=new ArrayList<String>();
		Map<String,Object> deviceIdMap=new HashMap<String,Object>();
		for(String ids:treeIds) {
			String[] id=ids.split("-");
			//设备id
			String deviceId=id[0];
			if(!deviceIdMap.containsKey(deviceId)) {
				deviceIdMap.put(deviceId, deviceId);
				deviceIdList.add(deviceId);
			}
		}
		//保存配置的打印机
		certificateConfigDeviceService.saveBatch(certificateConfig.getId().toString(), deviceIdList);
		//保存配置的合格证
		certificateConfigTemplateService.saveBatch(certificateConfig.getId().toString(), treeIds);
		return result;
	}

	/**
	 * 修改电子合格证配置
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateConfig(CertificateConfig certificateConfig) {
		certificateConfig.updateInit(SecurityUtils.getLoginUser().getUsername());
		int result = certificateConfigMapper.updateCertificateConfig(certificateConfig);

		String[] treeIds=certificateConfig.getCertificateTemplateIds().split(",");
		//获取配置的设备id
		List<String> deviceIdList=new ArrayList<String>();
		Map<String,Object> deviceIdMap=new HashMap<String,Object>();
		for(String ids:treeIds) {
			String[] id=ids.split("-");
			//设备id
			String deviceId=id[0];
			if(!deviceIdMap.containsKey(deviceId)) {
				deviceIdMap.put(deviceId, deviceId);
				deviceIdList.add(deviceId);
			}
		}
		//编辑时处理旧数据
		certificateConfigDeviceService.deleteByCertificateConfigId(certificateConfig.getId().toString());
		certificateConfigTemplateService.deleteByCertificateConfigId(certificateConfig.getId().toString());
		//保存配置的打印机
		certificateConfigDeviceService.saveBatch(certificateConfig.getId().toString(), deviceIdList);
		//保存配置的合格证
		certificateConfigTemplateService.saveBatch(certificateConfig.getId().toString(), treeIds);
		return result;
	}

	/**
	 * 批量删除电子合格证配置
	 *
	 * @param ids 需要删除的电子合格证配置主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateConfigByIds(List<Long> ids) {
		return certificateConfigMapper.logicRemoveByIds(ids);
		//return certificateConfigMapper.deleteCertificateConfigByIds(ids);
	}

	/**
	 * 删除电子合格证配置信息
	 *
	 * @param id 电子合格证配置主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateConfigById(Long id) {
		int result = certificateConfigMapper.logicRemoveById(id);
		certificateConfigDeviceService.deleteByCertificateConfigId(id.toString());
		certificateConfigTemplateService.deleteByCertificateConfigId(id.toString());
		return result;
		//return certificateConfigMapper.deleteCertificateConfigById(id);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void save(CertificateConfig certificateConfig) {
		certificateConfig.insertInit(SecurityUtils.getLoginUser().getUsername());
		certificateConfigMapper.insertCertificateConfig(certificateConfig);
		String[] treeIds=certificateConfig.getTreeIds().split(",");
		//获取配置的设备id
		List<String> deviceIdList=new ArrayList<String>();
		Map<String,Object> deviceIdMap=new HashMap<String,Object>();
		for(String ids:treeIds) {
			String[] id=ids.split("-");
			//设备id
			String deviceId=id[0];
			if(!deviceIdMap.containsKey(deviceId)) {
				deviceIdMap.put(deviceId, deviceId);
				deviceIdList.add(deviceId);
			}
		}
		//编辑时处理旧数据
		certificateConfigDeviceService.deleteByCertificateConfigId(certificateConfig.getId()+"");
		certificateConfigTemplateService.deleteByCertificateConfigId(certificateConfig.getId()+"");
		//保存配置的打印机
		certificateConfigDeviceService.saveBatch(certificateConfig.getId()+"", deviceIdList);
		//保存配置的合格证
		certificateConfigTemplateService.saveBatch(certificateConfig.getId()+"", treeIds);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void delete(CertificateConfig certificateConfig) {
		certificateConfigMapper.logicRemoveById(certificateConfig.getId());
		//编辑时处理旧数据
		certificateConfigDeviceService.deleteByCertificateConfigId(certificateConfig.getId()+"");
		certificateConfigTemplateService.deleteByCertificateConfigId(certificateConfig.getId()+"");
	}

	@Override
	public List<EnumSubject> findCertificateConfigTypeList() {
		List<EnumSubject> list=new ArrayList<EnumSubject>();
		for(CertificateConfigTypeEnum e : CertificateConfigTypeEnum.values()){
			EnumSubject subject=new EnumSubject(e.getKey(), e.getValue());
			list.add(subject);
		}
		return list;
	}

	/**
	 *
	 * @title: findDeviceTemplateTreeList
	 * @author: lxy
	 * @date: 2021年1月6日 上午11:33:02
	 * @description: 获取设备、合格证树集合
	 * @param: @return
	 * @return: List<Map<String,Object>>
	 */
	@Override
	public List<Map<String, Object>> findDeviceTemplateTreeList() {
		//设备集合
		List<DeviceParameter> deviceParameterList=deviceParameterService.selectDeviceParameterList(new DeviceParameter());
		//合格证集合
		List<CertificateTemplate> certificateTemplateList=certificateTemplateService.selectCertificateTemplateList(new CertificateTemplate());
		List<Map<String,Object>> resultList=new ArrayList<Map<String,Object>>();
		Map<String,Object> map=null;
		for(DeviceParameter deviceParameter:deviceParameterList) {
			map=new HashMap<String,Object>();
			map.put("id", deviceParameter.getId());
			map.put("name", deviceParameter.getName());
			map.put("parentId", "0");
			map.put("deviceTemplateId", deviceParameter.getId()+"-"+deviceParameter.getId());
			map.put("type", "device");
			List<Map<String,Object>> childList=new ArrayList<Map<String,Object>>();
			for(CertificateTemplate certificateTemplate:certificateTemplateList) {
				Map<String,Object> childMap=new HashMap<String,Object>();
				childMap.put("id", certificateTemplate.getId());
				childMap.put("name", certificateTemplate.getName());
				childMap.put("parentId", deviceParameter.getId());
				childMap.put("deviceTemplateId", deviceParameter.getId()+"-"+certificateTemplate.getId());
				childMap.put("type", "template");
				childList.add(childMap);
			}
			map.put("children", childList);
			resultList.add(map);
		}
		return resultList;
	}

	/**
	 *
	 * @title: getDeviceTemplateIds
	 * @author: lxy
	 * @date: 2021年1月6日 下午2:50:04
	 * @description: 拼装获取设备、合格证模板ids
	 * @param: certificateConfigId
	 * @return: String
	 */
	@Override
	public String getDeviceTemplateIds(String certificateConfigId) {
		return certificateConfigTemplateService.getDeviceTemplateIds(certificateConfigId);
	}

	/**
	 *
	 * @title: getCertificateConfig
	 * @author: lxy
	 * @date: 2021年1月7日 上午10:44:51
	 * @description: 获取合格证配置
	 * @param: certificateConfig
	 * @return: List<Object>
	 * @throws Exception
	 */
	@Override
	public List<Object> getCertificateConfig(CertificateConfig certificateConfig) {
		CertificateConfig config = null;
		// 先按区域查找配置信息
		config = this.getAreaConfig(certificateConfig);
		// 区域配置没有，采用通用配置
		if (null == config) {
			config = this.getGeneralConfig();
		}
		// 通用配置也没有则抛出异常处理
		if (null == config) {
			throw new ServiceException("没有打印机、合格证配置信息");
		}
		return this.certificateConfigTemplateService.findDeviceTemplateConfigList(config.getId()+"");
	}

	/**
	 *
	 * @title: getAreaConfig
	 * @author: lxy
	 * @date: 2021年1月7日 上午10:49:44
	 * @description: 获取指定区域配置
	 * @param: certificateConfig
	 * @return: CertificateConfig
	 */
	public CertificateConfig getAreaConfig(CertificateConfig certificateConfig) {
		List<CertificateConfig> list=certificateConfigMapper.selectCertificateConfigList(certificateConfig);
		if(null!=list && !list.isEmpty()) {
			return list.get(0);
		}
		return null;
	}

	/**
	 *
	 * @title: getGeneralConfig
	 * @author: lxy
	 * @date: 2021年1月7日 上午10:49:44
	 * @description: 获取指定区域配置
	 * @param: certificateConfig
	 * @return: CertificateConfig
	 */
	public CertificateConfig getGeneralConfig() {
		CertificateConfig certificateConfigParam=new CertificateConfig();
		certificateConfigParam.setType(CertificateConfigTypeEnum.CODE_0.getKey());
		List<CertificateConfig> list=certificateConfigMapper.selectCertificateConfigList(certificateConfigParam);
		if(null!=list && !list.isEmpty()) {
			return list.get(0);
		}
		return null;
	}
}
