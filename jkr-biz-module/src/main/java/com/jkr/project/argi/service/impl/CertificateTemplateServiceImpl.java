package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.impl.SysFileServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateTemplateMapper;
import com.jkr.project.argi.domain.CertificateTemplate;
import com.jkr.project.argi.service.ICertificateTemplateService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合格证模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateTemplateServiceImpl implements ICertificateTemplateService {
	@Autowired
	private CertificateTemplateMapper certificateTemplateMapper;

	@Autowired
	private SysFileServiceImpl sysFileService;

	/**
	 * 查询合格证模板
	 *
	 * @param id 合格证模板主键
	 * @return 合格证模板
	 */
	@Override
	public CertificateTemplate selectCertificateTemplateById(Long id) {
		CertificateTemplate certificateTemplate = certificateTemplateMapper.selectCertificateTemplateById(id);
		if (certificateTemplate != null) {
			SysFile sysFile = new SysFile();
			sysFile.setTableId(certificateTemplate.getId());
			List<SysFile> fileList = sysFileService.findUrlsFileList(sysFile);
			certificateTemplate.setImageFiles(fileList);
		}
		return certificateTemplate;
	}

	/**
	 * 查询合格证模板列表
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 合格证模板
	 */
	@Override
	public List<CertificateTemplate> selectCertificateTemplateList(CertificateTemplate certificateTemplate) {
		return certificateTemplateMapper.selectCertificateTemplateList(certificateTemplate);
	}

	/**
	 * 新增合格证模板
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificateTemplate(CertificateTemplate certificateTemplate) {
		certificateTemplate.insertInit(SecurityUtils.getLoginUser().getUsername());
		int result = certificateTemplateMapper.insertCertificateTemplate(certificateTemplate);
		if (certificateTemplate.getImageFiles() != null) {
			for (SysFile file : certificateTemplate.getImageFiles()) {
				file.setTableId(certificateTemplate.getId());
				sysFileService.insertSysFile(file);
			}
		}
		return result;
	}

	/**
	 * 修改合格证模板
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificateTemplate(CertificateTemplate certificateTemplate) {
		certificateTemplate.updateInit(SecurityUtils.getLoginUser().getUsername());
		int result = certificateTemplateMapper.updateCertificateTemplate(certificateTemplate);
		if (certificateTemplate.getImageFiles() != null) {
			for (SysFile file : certificateTemplate.getImageFiles()) {
				file.setTableId(certificateTemplate.getId());
				sysFileService.insertSysFile(file);
			}
		}
		return result;
	}

	/**
	 * 批量删除合格证模板
	 *
	 * @param ids 需要删除的合格证模板主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateTemplateByIds(List<Long> ids) {
		return certificateTemplateMapper.logicRemoveByIds(ids);
		//return certificateTemplateMapper.deleteCertificateTemplateByIds(ids);
	}

	/**
	 * 删除合格证模板信息
	 *
	 * @param id 合格证模板主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateTemplateById(Long id) {
		return certificateTemplateMapper.logicRemoveById(id);
		//return certificateTemplateMapper.deleteCertificateTemplateById(id);
	}

}
