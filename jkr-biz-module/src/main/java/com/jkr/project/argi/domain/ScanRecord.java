package com.jkr.project.argi.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 扫描日志对象 bas_scan_record
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_scan_record")
public class ScanRecord extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 合格证id
     */
    @Excel(name = "合格证id")
    private String certificateId;

    /**
     * 合格证号
     */
    @Excel(name = "合格证号")
    private String fullNumber;

    /**
     * 产品生产主体id（查询人）
     */
    @Excel(name = "产品生产主体id", readConverterExp = "查=询人")
    private String entId;

    /**
     * 扫描时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "扫描时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date scanDate;

    /**
     * 省
     */
    @Excel(name = "省")
    private String province;

    /**
     * 市
     */
    @Excel(name = "市")
    private String city;

    /**
     * 区
     */
    @Excel(name = "区")
    private String county;

    /**
     * ip
     */
    @Excel(name = "ip")
    private String ip;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;
	@TableField(exist = false)
	private String batchNo;		// 合格证编号
	@TableField(exist = false)
	private String beginSerialNumber;//开始流水号
	@TableField(exist = false)
	private String endSerialNumber;//结束流水号
	@TableField(exist = false)
	private String entName; //企业名称
	@TableField(exist = false)
	private String productName;		// 产品名称
	@TableField(exist = false)
	private String productSortCode;		// 分类code
	@TableField(exist = false)
	private String productSortName;		// 分类名称
	@TableField(exist = false)
	private Date beginScanDate;		// 开始 创建时间
	@TableField(exist = false)
	private Date endScanDate;		// 结束 创建时间
	@TableField(exist = false)
	private String entAreaCode;		// 主体行政区划code
	@TableField(exist = false)
	private String scanAddress;		// 扫描地点
	@TableField(exist = false)
	private String addProductOrder;
	@TableField(exist = false)
	private String addScanDateOrder;
}
