package com.jkr.project.argi.service.impl;

import java.util.List;

import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.PropertyEnum;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ProductSampleMapper;
import com.jkr.project.argi.domain.ProductSample;
import com.jkr.project.argi.service.IProductSampleService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品样品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ProductSampleServiceImpl implements IProductSampleService {
	@Autowired
	private ProductSampleMapper productSampleMapper;

	/**
	 * 查询产品样品
	 *
	 * @param id 产品样品主键
	 * @return 产品样品
	 */
	@Override
	public ProductSample selectProductSampleById(Long id) {
		return productSampleMapper.selectProductSampleById(id);
	}

	/**
	 * 查询产品样品列表
	 *
	 * @param productSample 产品样品
	 * @return 产品样品
	 */
	@Override
	public List<ProductSample> selectProductSampleList(ProductSample productSample) {
		return productSampleMapper.selectProductSampleList(productSample);
	}

	/**
	 * 新增产品样品
	 *
	 * @param productSample 产品样品
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertProductSample(ProductSample productSample) {
		productSample.insertInit(SecurityUtils.getLoginUser().getUsername());

		return productSampleMapper.insertProductSample(productSample);
	}

	/**
	 * 修改产品样品
	 *
	 * @param productSample 产品样品
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateProductSample(ProductSample productSample) {
		productSample.updateInit(SecurityUtils.getLoginUser().getUsername());

		return productSampleMapper.updateProductSample(productSample);
	}

	/**
	 * 批量删除产品样品
	 *
	 * @param ids 需要删除的产品样品主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductSampleByIds(List<Long> ids) {
		return productSampleMapper.logicRemoveByIds(ids);
		//return productSampleMapper.deleteProductSampleByIds(ids);
	}

	/**
	 * 删除产品样品信息
	 *
	 * @param id 产品样品主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductSampleById(Long id) {
		return productSampleMapper.logicRemoveById(id);
		//return productSampleMapper.deleteProductSampleById(id);
	}

	@Override
	public ProductSample getProductSample(String entId, String productId, String inspectionSituation) {
		if(StringUtils.isBlank(entId) || StringUtils.isBlank(productId) || StringUtils.isBlank(inspectionSituation)) {
			return null;
		}
		ProductSample productSample=new ProductSample();
		productSample.setEntId(entId);
		productSample.setProductId(productId);
		productSample.setInspectionSituation(inspectionSituation);
		List<ProductSample> list = productSampleMapper.selectProductSampleList(productSample);
		if(null!=list && !list.isEmpty()) {
			return list.get(0);
		}
		return null;
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateNewestSample(String productInspectionId, String entId, String productId, String inspectionSituation) {
		if (StringUtils.isBlank(productInspectionId) || StringUtils.isBlank(entId) || StringUtils.isBlank(productId) || StringUtils.isBlank(inspectionSituation)) {
			throw new ServiceException("更新样品方法参数不全");
		}
		ProductSample productSampleParam = new ProductSample(entId, productId, inspectionSituation);
		/**
		 * 2021年1月18日13:28:09
		 * lxy
		 * 更新样品编号逻辑
		 * 更具参数获取数据库list记录，判断list是否为空：
		 * 1、空-直接新增
		 * 2、不空-判断检测记录id是否相同，不同时更新样品数据
		 */
		List<ProductSample> list = productSampleMapper.selectProductSampleList(productSampleParam);
		if (!list.isEmpty()) {
			ProductSample productSample = list.get(0);
			if(!productInspectionId.equals(productSample.getProductInspectionId())) {
				productSample.setProductInspectionId(productInspectionId);
				productSample.updateInit(SecurityUtils.getLoginUser().getUsername());
				productSampleMapper.updateProductSample(productSample);
			}
		} else {
			productSampleParam.setProductInspectionId(productInspectionId);
			productSampleParam.insertInit(SecurityUtils.getLoginUser().getUsername());
			productSampleMapper.insertProductSample(productSampleParam);
		}
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateNewestQuickSample(String entId, String productId, String sampleNo) {
		if (StringUtils.isBlank(entId) || StringUtils.isBlank(productId) || StringUtils.isBlank(sampleNo)) {
			throw new ServiceException("更新样品方法参数不全");
		}
		String inspectionSituation = PropertyEnum.InspectionSituationEnum.IS_02.getKey();
		ProductSample productSampleParam = new ProductSample(entId, productId,inspectionSituation);
		/**
		 * 2021年1月18日13:28:09
		 * lxy
		 * 更新样品编号逻辑
		 * 更具参数获取数据库list记录，判断list是否为空：
		 * 1、空-直接新增
		 * 2、不空-判断检测记录sampleNo是否相同，不同时更新样品数据
		 */
		List<ProductSample> list = productSampleMapper.selectProductSampleList(productSampleParam);
		if (!list.isEmpty()) {
			ProductSample productSample = list.get(0);
			if(!sampleNo.equals(productSample.getSampleNo())) {
				productSample.setSampleNo(sampleNo);
				productSample.updateInit(SecurityUtils.getLoginUser().getUsername());
				productSampleMapper.updateProductSample(productSample);
			}
		} else {
			productSampleParam.setSampleNo(sampleNo);
			productSampleParam.insertInit(SecurityUtils.getLoginUser().getUsername());
			productSampleMapper.insertProductSample(productSampleParam);
		}
	}

}
