package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .CertificateConfigTemplate;

/**
 * 电子合格证配置-模板Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateConfigTemplateService {
	/**
	 * 查询电子合格证配置-模板
	 *
	 * @param id 电子合格证配置-模板主键
	 * @return 电子合格证配置-模板
	 */
	public CertificateConfigTemplate selectCertificateConfigTemplateById(Long id);

	/**
	 * 查询电子合格证配置-模板列表
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 电子合格证配置-模板集合
	 */
	public List<CertificateConfigTemplate> selectCertificateConfigTemplateList(CertificateConfigTemplate certificateConfigTemplate);

	/**
	 * 新增电子合格证配置-模板
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 结果
	 */
	public int insertCertificateConfigTemplate(CertificateConfigTemplate certificateConfigTemplate);

	/**
	 * 修改电子合格证配置-模板
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 结果
	 */
	public int updateCertificateConfigTemplate(CertificateConfigTemplate certificateConfigTemplate);

	/**
	 * 批量删除电子合格证配置-模板
	 *
	 * @param ids 需要删除的电子合格证配置-模板主键集合
	 * @return 结果
	 */
	public int deleteCertificateConfigTemplateByIds(List<Long> ids);

	/**
	 * 删除电子合格证配置-模板信息
	 *
	 * @param id 电子合格证配置-模板主键
	 * @return 结果
	 */
	public int deleteCertificateConfigTemplateById(Long id);

	public void saveBatch(String certificateConfigId, String[] treeIds);

	public String getDeviceTemplateIds(String certificateConfigId);

	public int deleteByCertificateConfigId(String certificateConfigId);

	public List<Object> findDeviceTemplateConfigList(String certificateConfigId);

}
