package com.jkr.project.argi.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.jkr.project.argi.domain .Product;

/**
 * 产品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IProductService {
	/**
	 * 查询产品
	 *
	 * @param id 产品主键
	 * @return 产品
	 */
	public Product selectProductById(Long id);

	/**
	 * 查询产品列表
	 *
	 * @param product 产品
	 * @return 产品集合
	 */
	public List<Product> selectProductList(Product product);

	public List<Product> findProductPage(Product product);

	/**
	 * 新增产品
	 *
	 * @param product 产品
	 * @return 结果
	 */
	public int insertProduct(Product product);

	/**
	 * 修改产品
	 *
	 * @param product 产品
	 * @return 结果
	 */
	public int updateProduct(Product product);

	/**
	 * 批量删除产品
	 *
	 * @param ids 需要删除的产品主键集合
	 * @return 结果
	 */
	public int deleteProductByIds(List<Long> ids);

	/**
	 * 删除产品信息
	 *
	 * @param id 产品主键
	 * @return 结果
	 */
	public int deleteProductById(Long id);

	public void updateInspectionResultById(JSONArray productList);

	public Object findReBuyProductPage(Product product);

	public void saveProductNew(Product product);

	public Product findProductAttachment(Product product);

	public List<Product> findProductList(String entId);

	public List<Product> findInspectionList();

	public Map<String, Object> getProductNameAndCertificationNameMap(String entId);

	public Map<String,Object> findCountGroupName(Product entity);

	public Map<String,Object> findProductCount(String name);

	public List<Product> findListByUserId(Product product);

	public Integer updatePrintAmount(String id,Integer amount);

	public Integer updateValidPrintAmount(String productId,String entId);

	public boolean checkPhoneNumOpenShopCart(String phoneNum);

	public List<Product> findProductSingleList();

	List<Product> getSyncData(Date date);
}
