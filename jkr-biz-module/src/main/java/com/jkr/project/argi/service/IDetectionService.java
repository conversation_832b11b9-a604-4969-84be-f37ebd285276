package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .Detection;

/**
 * 与快检系统对接Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IDetectionService {
	/**
	 * 查询与快检系统对接
	 *
	 * @param id 与快检系统对接主键
	 * @return 与快检系统对接
	 */
	public Detection selectDetectionById(Long id);

	/**
	 * 查询与快检系统对接列表
	 *
	 * @param detection 与快检系统对接
	 * @return 与快检系统对接集合
	 */
	public List<Detection> selectDetectionList(Detection detection);

	/**
	 * 新增与快检系统对接
	 *
	 * @param detection 与快检系统对接
	 * @return 结果
	 */
	public int insertDetection(Detection detection);

	/**
	 * 修改与快检系统对接
	 *
	 * @param detection 与快检系统对接
	 * @return 结果
	 */
	public int updateDetection(Detection detection);

	/**
	 * 批量删除与快检系统对接
	 *
	 * @param ids 需要删除的与快检系统对接主键集合
	 * @return 结果
	 */
	public int deleteDetectionByIds(List<Long> ids);

	/**
	 * 删除与快检系统对接信息
	 *
	 * @param id 与快检系统对接主键
	 * @return 结果
	 */
	public int deleteDetectionById(Long id);

}
