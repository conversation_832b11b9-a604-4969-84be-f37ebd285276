package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateProductInspection;
import org.apache.ibatis.annotations.Param;

/**
 * 合格证-产品检测关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateProductInspectionMapper extends BaseMapper<CertificateProductInspection>{
	/**
	 * 查询合格证-产品检测关系
	 *
	 * @param id 合格证-产品检测关系主键
	 * @return 合格证-产品检测关系
	 */
	public CertificateProductInspection selectCertificateProductInspectionById(Long id);

	/**
	 * 查询合格证-产品检测关系列表
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 合格证-产品检测关系集合
	 */
	public List<CertificateProductInspection> selectCertificateProductInspectionList(CertificateProductInspection certificateProductInspection);

	/**
	 * 新增合格证-产品检测关系
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 结果
	 */
	public int insertCertificateProductInspection(CertificateProductInspection certificateProductInspection);

	/**
	 * 修改合格证-产品检测关系
	 *
	 * @param certificateProductInspection 合格证-产品检测关系
	 * @return 结果
	 */
	public int updateCertificateProductInspection(CertificateProductInspection certificateProductInspection);

	/**
	 * 删除合格证-产品检测关系
	 *
	 * @param id 合格证-产品检测关系主键
	 * @return 结果
	 */
	public int deleteCertificateProductInspectionById(Long id);

	/**
	 * 批量删除合格证-产品检测关系
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateProductInspectionByIds(Long[] ids);

	/**
	 * 批量逻辑删除合格证-产品检测关系
	 *
	 * @param  ids 合格证-产品检测关系主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过合格证-产品检测关系主键id逻辑删除信息
	 *
	 * @param  id 合格证-产品检测关系主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public List<String> getProductInspectionByCertificateId(@Param("certificateId") String certificateId);

	public int insertBatch(@Param("list")List<CertificateProductInspection> list);

	public List<CertificateProductInspection> getCertificateProductInspectionByCertificateId(@Param("certificateId") String certificateId);

}
