package com.jkr.project.argi.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.function.Consumer;

/**
 * 通用延时发布服务
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@Service
@Slf4j
public class DelayedPublishService {

    @Value("${delayed.publish.enabled}")
    private boolean enabled;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 添加实体到延时发布队列
     *
     * @param queueKey 队列键名
     * @param entityId 实体ID
     * @param publishTime 发布时间
     */
    public void addToQueue(String queueKey, Long entityId, LocalDateTime publishTime) {
        if (entityId != null && publishTime != null) {
            // 使用publishTime作为score，将实体id存入zset
            redisTemplate.opsForZSet().add(queueKey, 
                    String.valueOf(entityId), 
                    publishTime.toEpochSecond(java.time.ZoneOffset.UTC));
            log.info("实体已添加到延时发布队列，队列: {}, ID: {}, 发布时间: {}", queueKey, entityId, publishTime);
        }
    }

    /**
     * 从延时发布队列中移除实体
     *
     * @param queueKey 队列键名
     * @param entityId 实体ID
     */
    public void removeFromQueue(String queueKey, Long entityId) {
        if (entityId != null) {
            redisTemplate.opsForZSet().remove(queueKey, String.valueOf(entityId));
            log.info("实体已从延时发布队列中移除，队列: {}, ID: {}", queueKey, entityId);
        }
    }

    /**
     * 处理指定队列的延时发布任务
     *
     * @param queueKey 队列键名
     * @param publishHandler 发布处理函数
     */
    public void processQueue(String queueKey, Consumer<Long> publishHandler) {
        if (!enabled) {
            return;
        }
        
        log.debug("开始处理延时发布队列: {}", queueKey);
        LocalDateTime now = LocalDateTime.now();
        double score = now.toEpochSecond(java.time.ZoneOffset.UTC);
        
        // 获取当前时间之前的所有待发布实体
        Set<Object> publishSet = redisTemplate.opsForZSet()
                .rangeByScore(queueKey, 0, score);

        if (publishSet != null && !publishSet.isEmpty()) {
            for (Object entityId : publishSet) {
                try {
                    log.info("处理延时发布任务，队列: {}, 实体ID: {}", queueKey, entityId);
                    
                    // 调用具体的发布处理逻辑
                    publishHandler.accept(Long.valueOf(entityId.toString()));

                    // 从发布队列中移除
                    redisTemplate.opsForZSet().remove(queueKey, entityId);
                    log.info("延时发布任务处理成功，队列: {}, ID: {}", queueKey, entityId);
                } catch (Exception e) {
                    log.error("处理延时发布任务失败，队列: {}, 实体ID: {}, 错误: {}", queueKey, entityId, e.getMessage(), e);
                }
            }
        }
        log.debug("延时发布队列处理完成: {}", queueKey);
    }

    /**
     * 定时处理所有延时发布队列
     * 这个方法会被具体的处理器调用
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void handleAllQueues() {
        if (!enabled) {
            return;
        }
        // 这个方法可以被扩展来处理多个队列
        // 目前由具体的处理器来调用 processQueue 方法
    }
}
