package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .CertificateTemplate;

/**
 * 合格证模板Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateTemplateService {
	/**
	 * 查询合格证模板
	 *
	 * @param id 合格证模板主键
	 * @return 合格证模板
	 */
	public CertificateTemplate selectCertificateTemplateById(Long id);

	/**
	 * 查询合格证模板列表
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 合格证模板集合
	 */
	public List<CertificateTemplate> selectCertificateTemplateList(CertificateTemplate certificateTemplate);

	/**
	 * 新增合格证模板
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 结果
	 */
	public int insertCertificateTemplate(CertificateTemplate certificateTemplate);

	/**
	 * 修改合格证模板
	 *
	 * @param certificateTemplate 合格证模板
	 * @return 结果
	 */
	public int updateCertificateTemplate(CertificateTemplate certificateTemplate);

	/**
	 * 批量删除合格证模板
	 *
	 * @param ids 需要删除的合格证模板主键集合
	 * @return 结果
	 */
	public int deleteCertificateTemplateByIds(List<Long> ids);

	/**
	 * 删除合格证模板信息
	 *
	 * @param id 合格证模板主键
	 * @return 结果
	 */
	public int deleteCertificateTemplateById(Long id);

}
