package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.ProductStorage;

/**
 * 产品保质方式Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ProductStorageMapper extends BaseMapper<ProductStorage>{
	/**
	 * 查询产品保质方式
	 *
	 * @param id 产品保质方式主键
	 * @return 产品保质方式
	 */
	public ProductStorage selectProductStorageById(Long id);

	/**
	 * 查询产品保质方式列表
	 *
	 * @param productStorage 产品保质方式
	 * @return 产品保质方式集合
	 */
	public List<ProductStorage> selectProductStorageList(ProductStorage productStorage);

	/**
	 * 新增产品保质方式
	 *
	 * @param productStorage 产品保质方式
	 * @return 结果
	 */
	public int insertProductStorage(ProductStorage productStorage);

	/**
	 * 修改产品保质方式
	 *
	 * @param productStorage 产品保质方式
	 * @return 结果
	 */
	public int updateProductStorage(ProductStorage productStorage);

	/**
	 * 删除产品保质方式
	 *
	 * @param id 产品保质方式主键
	 * @return 结果
	 */
	public int deleteProductStorageById(Long id);

	/**
	 * 批量删除产品保质方式
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteProductStorageByIds(Long[] ids);

	/**
	 * 批量逻辑删除产品保质方式
	 *
	 * @param  ids 产品保质方式主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过产品保质方式主键id逻辑删除信息
	 *
	 * @param  id 产品保质方式主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
