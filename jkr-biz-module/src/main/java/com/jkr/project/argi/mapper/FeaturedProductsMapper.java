package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.FeaturedProducts;

/**
 * 特色产品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface FeaturedProductsMapper extends BaseMapper<FeaturedProducts>{
	/**
	 * 查询特色产品
	 *
	 * @param id 特色产品主键
	 * @return 特色产品
	 */
	public FeaturedProducts selectFeaturedProductsById(Long id);

	/**
	 * 查询特色产品列表
	 *
	 * @param featuredProducts 特色产品
	 * @return 特色产品集合
	 */
	public List<FeaturedProducts> selectFeaturedProductsList(FeaturedProducts featuredProducts);

	/**
	 * 新增特色产品
	 *
	 * @param featuredProducts 特色产品
	 * @return 结果
	 */
	public int insertFeaturedProducts(FeaturedProducts featuredProducts);

	/**
	 * 修改特色产品
	 *
	 * @param featuredProducts 特色产品
	 * @return 结果
	 */
	public int updateFeaturedProducts(FeaturedProducts featuredProducts);

	/**
	 * 删除特色产品
	 *
	 * @param id 特色产品主键
	 * @return 结果
	 */
	public int deleteFeaturedProductsById(Long id);

	/**
	 * 批量删除特色产品
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteFeaturedProductsByIds(Long[] ids);

	/**
	 * 批量逻辑删除特色产品
	 *
	 * @param  ids 特色产品主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过特色产品主键id逻辑删除信息
	 *
	 * @param  id 特色产品主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
