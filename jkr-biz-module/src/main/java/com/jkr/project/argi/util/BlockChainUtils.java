package com.jkr.project.argi.util;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * @Title BlockChainUtils
 * @Description //TODO 区块链工具类
 * <AUTHOR>
 * @Date 2020/7/24 09:51
 **/
public class BlockChainUtils {

    private static Logger logger = LoggerFactory.getLogger(BlockChainUtils.class);

    @Value("${blockChain.baseUrl}")
    private static String  baseUrl;
    @Value("${blockChain.insertUrl}")
    private static String  blkInsertUrl;
    @Value("${blockChain.queryByIdUrl}")
    private static String  blkQueryByIdUrl;
    @Value("${blockChain.checkByIdUrl}")
    private static String  blkCheckByIdUrl;

    /**
     *@ClassName: makeSign
     *@Author: qfx
     *@Description: 制作签名
     *@Date: 2020/5/13 13:52
     *@return: java.lang.String
     **/
    public static String makeSign() throws Exception{
        long reqtime = System.currentTimeMillis()/1000;
        Map<String,Object> map = new HashMap<>();
        map.put("reqtime",reqtime);
        map.put("password",AesUtil.PASSWORD);
        String content = JSONObject.toJSONString(map);
        logger.debug("签名内容："+ content);
        String aesStr =  AesUtil.aesEncrypt(content);
        return aesStr;
    }

    /**
     * @Title insertData
     * @Description //TODO 调用区块链插入数据接口
     * <AUTHOR>
     * @Date 2020/7/24 10:59
     * @Param map
     * @Return java.lang.String
     **/
    public static ResponseEntity<Map> insertData(Map<String,Object> map) throws Exception{
        return accessBlockInterface(blkInsertUrl,map);
    }

    /**
     * @Title blkQueryByTxId
     * @Description //TODO 通过区块链id查询区块链内容
     * <AUTHOR>
     * @Date 2020/7/24 11:04
     * @Param txId
     * @Return java.lang.String
     **/
    public static ResponseEntity<Map> queryById(String id) throws Exception{
        Map<String,Object> map = new HashMap<>();
        map.put("id",id);
        return accessBlockInterface(blkQueryByIdUrl,map);
    }

    /**
     * @Title checkByTxId
     * @Description //TODO 通过id校验区块链数据
     * <AUTHOR>
     * @Date 2020/7/24 11:04
     * @Param txId
     * @Return java.lang.String
     **/
    public static ResponseEntity<Map> checkById(String id) throws Exception{
        Map<String,Object> map = new HashMap<>();
        map.put("txid",id);
        return accessBlockInterface(blkCheckByIdUrl,map);
    }

    /**
     * @Title accessBlockInterface
     * @Description //TODO 访问区块链接口通用方法
     * <AUTHOR>
     * @Date 2020/7/24 10:57
     * @Param map
     * @Return java.lang.String
     **/
    public static ResponseEntity<Map> accessBlockInterface(String url,Map<String,Object> map) throws Exception{
        // 创建签名
        String sign = makeSign();
        Map<String,Object> hasMap = new HashMap<>();
        hasMap.put("sign",sign);
        hasMap.put("params",JSONObject.toJSONString(map));
        //跨域访问表头
        HttpHeaders headers = new HttpHeaders();

        RestTemplate restTemplate = new RestTemplate();

        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(hasMap,headers);
        ResponseEntity<Map> responseEntity = restTemplate.postForEntity(url,httpEntity,Map.class);

        return responseEntity;
    }

}
