package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.ProductSample;

/**
 * 产品样品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface ProductSampleMapper extends BaseMapper<ProductSample>{
	/**
	 * 查询产品样品
	 *
	 * @param id 产品样品主键
	 * @return 产品样品
	 */
	public ProductSample selectProductSampleById(Long id);

	/**
	 * 查询产品样品列表
	 *
	 * @param productSample 产品样品
	 * @return 产品样品集合
	 */
	public List<ProductSample> selectProductSampleList(ProductSample productSample);

	/**
	 * 新增产品样品
	 *
	 * @param productSample 产品样品
	 * @return 结果
	 */
	public int insertProductSample(ProductSample productSample);

	/**
	 * 修改产品样品
	 *
	 * @param productSample 产品样品
	 * @return 结果
	 */
	public int updateProductSample(ProductSample productSample);

	/**
	 * 删除产品样品
	 *
	 * @param id 产品样品主键
	 * @return 结果
	 */
	public int deleteProductSampleById(Long id);

	/**
	 * 批量删除产品样品
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteProductSampleByIds(Long[] ids);

	/**
	 * 批量逻辑删除产品样品
	 *
	 * @param  ids 产品样品主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过产品样品主键id逻辑删除信息
	 *
	 * @param  id 产品样品主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
