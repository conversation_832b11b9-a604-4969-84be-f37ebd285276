package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Feedback;

/**
 * 反馈信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface FeedbackMapper extends BaseMapper<Feedback>{
	/**
	 * 查询反馈信息
	 *
	 * @param id 反馈信息主键
	 * @return 反馈信息
	 */
	public Feedback selectFeedbackById(Long id);

	/**
	 * 查询反馈信息列表
	 *
	 * @param feedback 反馈信息
	 * @return 反馈信息集合
	 */
	public List<Feedback> selectFeedbackList(Feedback feedback);

	/**
	 * 新增反馈信息
	 *
	 * @param feedback 反馈信息
	 * @return 结果
	 */
	public int insertFeedback(Feedback feedback);

	/**
	 * 修改反馈信息
	 *
	 * @param feedback 反馈信息
	 * @return 结果
	 */
	public int updateFeedback(Feedback feedback);

	/**
	 * 删除反馈信息
	 *
	 * @param id 反馈信息主键
	 * @return 结果
	 */
	public int deleteFeedbackById(Long id);

	/**
	 * 批量删除反馈信息
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteFeedbackByIds(Long[] ids);

	/**
	 * 批量逻辑删除反馈信息
	 *
	 * @param  ids 反馈信息主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过反馈信息主键id逻辑删除信息
	 *
	 * @param  id 反馈信息主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
