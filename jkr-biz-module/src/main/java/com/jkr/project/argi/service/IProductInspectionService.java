package com.jkr.project.argi.service;

import java.util.List;
import java.util.Map;

import com.jkr.project.argi.domain .ProductInspection;

/**
 * 产品检测Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IProductInspectionService {
	/**
	 * 查询产品检测
	 *
	 * @param id 产品检测主键
	 * @return 产品检测
	 */
	public ProductInspection selectProductInspectionById(Long id);

	/**
	 * 查询产品检测列表
	 *
	 * @param productInspection 产品检测
	 * @return 产品检测集合
	 */
	public List<ProductInspection> selectProductInspectionList(ProductInspection productInspection);

	/**
	 * 新增产品检测
	 *
	 * @param productInspection 产品检测
	 * @return 结果
	 */
	public int insertProductInspection(ProductInspection productInspection);

	/**
	 * 修改产品检测
	 *
	 * @param productInspection 产品检测
	 * @return 结果
	 */
	public int updateProductInspection(ProductInspection productInspection);

	/**
	 * 批量删除产品检测
	 *
	 * @param ids 需要删除的产品检测主键集合
	 * @return 结果
	 */
	public int deleteProductInspectionByIds(List<Long> ids);

	/**
	 * 删除产品检测信息
	 *
	 * @param id 产品检测主键
	 * @return 结果
	 */
	public int deleteProductInspectionById(Long id);

	public ProductInspection getEnter(String id);

	public void saveEnter(ProductInspection productInspection);

	public void updateNewestSample(ProductInspection productInspection);

	public int syncSaveQuick(List<Map<String, Object>> list);

	public ProductInspection getProductInspection(String entId,String productId,String inspectionSituation);

	public List<ProductInspection> findNewestList(String entId,String productId,String inspectionSituation);

	public Map<String,Object> findNewestListByInspectionSituationList(String entId,String productId,List<String> inspectionSituationList);
}
