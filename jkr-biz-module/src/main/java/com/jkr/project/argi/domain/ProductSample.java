package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 产品样品对象 bas_product_sample
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_product_sample")
public class ProductSample extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 主体id
     */
    @Excel(name = "主体id")
    private String entId;

    /**
     * 产品id
     */
    @Excel(name = "产品id")
    private String productId;

    /**
     * 检测情况
     */
    @Excel(name = "检测情况")
    private String inspectionSituation;

    /**
     * 检测结果表id
     */
    @Excel(name = "检测结果表id")
    private String productInspectionId;

    /**
     * 样品编号
     */
    @Excel(name = "样品编号")
    private String sampleNo;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;

	public ProductSample() {
		super();
	}

	public ProductSample(String entId,String productId,String inspectionSituation) {
		super();
		this.entId=entId;
		this.productId=productId;
		this.inspectionSituation=inspectionSituation;
	}
}
