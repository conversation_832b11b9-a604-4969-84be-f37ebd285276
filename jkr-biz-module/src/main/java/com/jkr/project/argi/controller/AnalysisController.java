package com.jkr.project.argi.controller;

import com.jkr.common.enums.EnumProperty;
import com.jkr.common.utils.PageUtils;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.argi.domain.AcquireRecord;
import com.jkr.project.argi.domain.AnalysisInfo;
import com.jkr.project.argi.service.IAcquireRecordService;
import com.jkr.project.argi.service.IAnalysisService;
import com.jkr.project.argi.service.ICertificateNoService;
import com.jkr.project.system.domain.SysArea;
import com.jkr.project.system.service.ISysAreaService;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 首页统计Controller
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@RestController
@RequestMapping("/argi/home/<USER>")
public class AnalysisController extends BaseController {

    @Autowired
    private IAnalysisService analysisService;

    @PostMapping("/findCount")
    public AjaxResult findCount(){
        return AjaxResult.success(analysisService.findCount());
    }

    @PostMapping("/findEntMainTypeCount")
    public AjaxResult findEntMainTypeCount(@RequestBody AnalysisInfo analysisInfo){
        return AjaxResult.success(analysisService.findEntMainTypeCount(analysisInfo));
    }

    @PostMapping("/findCertificateByMainType")
    public AjaxResult findCertificateByMainType(@RequestBody AnalysisInfo analysisInfo){
        return AjaxResult.success(analysisService.findCertificateByMainType(analysisInfo));
    }

    @PostMapping("/findCertificateByProductSortCode")
    public AjaxResult findCertificateByProductSortCode(@RequestBody AnalysisInfo analysisInfo){
        return AjaxResult.success(analysisService.findCertificateByProductSortCode(analysisInfo));
    }

    @PostMapping("/findProductByProductSortCode")
    public AjaxResult findProductByProductSortCode(@RequestBody AnalysisInfo analysisInfo){
        return AjaxResult.success(analysisService.findProductByProductSortCode(analysisInfo));
    }

    @PostMapping("/findScanByProductSortCode")
    public AjaxResult findScanByProductSortCode(@RequestBody AnalysisInfo analysisInfo){
        return AjaxResult.success(analysisService.findScanByProductSortCode(analysisInfo));
    }
}
