package com.jkr.project.argi.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import java.util.List;

/**
 * 反馈信息对象 bas_feedback
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
        @Data
        @EqualsAndHashCode(callSuper = true)
        @TableName("bas_feedback")
		public class Feedback extends BaseModel
		{
		private static final long serialVersionUID = 1L;

				/** 小程序登录人id */
				@Excel(name = "小程序登录人id")
		private String memberId;

				/** 联系电话 */
				@Excel(name = "联系电话")
		private String phone;

				/** 反馈内容 */
				@Excel(name = "反馈内容")
		private String content;

            /** 主键集合 */
            private List<Long> ids;
}
