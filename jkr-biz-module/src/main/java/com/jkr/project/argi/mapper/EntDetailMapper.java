package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.EntDetail;
import org.apache.ibatis.annotations.Param;

/**
 * 主体信息子表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface EntDetailMapper extends BaseMapper<EntDetail>{
	/**
	 * 查询主体信息子表
	 *
	 * @param id 主体信息子表主键
	 * @return 主体信息子表
	 */
	public EntDetail selectEntDetailById(Long id);

	/**
	 * 查询主体信息子表列表
	 *
	 * @param entDetail 主体信息子表
	 * @return 主体信息子表集合
	 */
	public List<EntDetail> selectEntDetailList(EntDetail entDetail);

	/**
	 * 新增主体信息子表
	 *
	 * @param entDetail 主体信息子表
	 * @return 结果
	 */
	public int insertEntDetail(EntDetail entDetail);

	/**
	 * 修改主体信息子表
	 *
	 * @param entDetail 主体信息子表
	 * @return 结果
	 */
	public int updateEntDetail(EntDetail entDetail);

	/**
	 * 删除主体信息子表
	 *
	 * @param id 主体信息子表主键
	 * @return 结果
	 */
	public int deleteEntDetailById(Long id);

	/**
	 * 批量删除主体信息子表
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteEntDetailByIds(Long[] ids);

	/**
	 * 批量逻辑删除主体信息子表
	 *
	 * @param  ids 主体信息子表主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过主体信息子表主键id逻辑删除信息
	 *
	 * @param  id 主体信息子表主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	/**
	 *
	 * @title: deleteByEntId
	 * @author: wanghe
	 * @date: 2021年9月15日 下午8:40:06
	 * @param:  entId
	 * @description: 删除
	 * @return:
	 * @throws
	 */
	void deleteByEntId(@Param("entId") String entId);

	/**
	 *
	 * @title: findDetailList
	 * @author: wanghe
	 * @date: 2021年9月15日 上午15:20:11
	 * @param:  id
	 * @description: 查询子表
	 * @return: ResponseResult
	 * @throws
	 */
	List<EntDetail> findDetailList(@Param("entId") Long entId);
}
