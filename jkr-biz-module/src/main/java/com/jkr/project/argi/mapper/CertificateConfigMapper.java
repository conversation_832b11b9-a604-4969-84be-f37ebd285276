package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateConfig;

/**
 * 电子合格证配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateConfigMapper extends BaseMapper<CertificateConfig>{
	/**
	 * 查询电子合格证配置
	 *
	 * @param id 电子合格证配置主键
	 * @return 电子合格证配置
	 */
	public CertificateConfig selectCertificateConfigById(Long id);

	/**
	 * 查询电子合格证配置列表
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 电子合格证配置集合
	 */
	public List<CertificateConfig> selectCertificateConfigList(CertificateConfig certificateConfig);

	/**
	 * 新增电子合格证配置
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 结果
	 */
	public int insertCertificateConfig(CertificateConfig certificateConfig);

	/**
	 * 修改电子合格证配置
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 结果
	 */
	public int updateCertificateConfig(CertificateConfig certificateConfig);

	/**
	 * 删除电子合格证配置
	 *
	 * @param id 电子合格证配置主键
	 * @return 结果
	 */
	public int deleteCertificateConfigById(Long id);

	/**
	 * 批量删除电子合格证配置
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateConfigByIds(Long[] ids);

	/**
	 * 批量逻辑删除电子合格证配置
	 *
	 * @param  ids 电子合格证配置主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过电子合格证配置主键id逻辑删除信息
	 *
	 * @param  id 电子合格证配置主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
