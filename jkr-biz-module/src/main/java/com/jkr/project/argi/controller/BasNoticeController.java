package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.BasNotice;
import com.jkr.project.argi.service.IBasNoticeService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 信息发布Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/basNotice")
public class BasNoticeController extends BaseController {
    @Autowired
    private IBasNoticeService basNoticeService;

    /**
     * 查询信息发布列表
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasNotice basNotice) {
        startPage();
        List<BasNotice> list = basNoticeService.selectBasNoticeList(basNotice);
        return getDataTable(list);
    }

    /**
     * 导出信息发布列表
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:export')")
    @Log(title = "导出信息发布列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasNotice basNotice) {
        List<BasNotice> list = basNoticeService.selectBasNoticeList(basNotice);
        ExcelUtil<BasNotice> util = new ExcelUtil<BasNotice>(BasNotice.class);
        util.exportExcel(response, list, "信息发布数据");
    }

    /**
     * 获取信息发布详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(basNoticeService.selectBasNoticeById(id));
    }

    /**
     * 新增信息发布
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:add')")
    @Log(title = "新增信息发布", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody BasNotice basNotice) {
        return toAjax(basNoticeService.insertBasNotice(basNotice));
    }

    /**
     * 修改信息发布
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:edit')")
    @Log(title = "修改信息发布", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody BasNotice basNotice) {
        return toAjax(basNoticeService.updateBasNotice(basNotice));
    }

    /**
     * 删除信息发布
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:remove')")
    @Log(title = "删除信息发布", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(basNoticeService.deleteBasNoticeById(id));
    }

    /**
     * 批量删除信息发布
     */
    @PreAuthorize("@ss.hasPermi('argi:basNotice:batchRemove')")
    @Log(title = "批量删除信息发布", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody BasNotice basNotice) {
        return toAjax(basNoticeService.deleteBasNoticeByIds(basNotice.getIds()));
    }
}
