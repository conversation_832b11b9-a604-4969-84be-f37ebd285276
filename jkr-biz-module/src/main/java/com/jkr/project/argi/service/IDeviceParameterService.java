package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .DeviceParameter;

/**
 * 蓝牙设备参数Service接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
public interface IDeviceParameterService {
	/**
	 * 查询蓝牙设备参数
	 *
	 * @param id 蓝牙设备参数主键
	 * @return 蓝牙设备参数
	 */
	public DeviceParameter selectDeviceParameterById(String id);

	/**
	 * 查询蓝牙设备参数列表
	 *
	 * @param deviceParameter 蓝牙设备参数
	 * @return 蓝牙设备参数集合
	 */
	public List<DeviceParameter> selectDeviceParameterList(DeviceParameter deviceParameter);

	/**
	 * 新增蓝牙设备参数
	 *
	 * @param deviceParameter 蓝牙设备参数
	 * @return 结果
	 */
	public int insertDeviceParameter(DeviceParameter deviceParameter);

	/**
	 * 修改蓝牙设备参数
	 *
	 * @param deviceParameter 蓝牙设备参数
	 * @return 结果
	 */
	public int updateDeviceParameter(DeviceParameter deviceParameter);

	/**
	 * 批量删除蓝牙设备参数
	 *
	 * @param ids 需要删除的蓝牙设备参数主键集合
	 * @return 结果
	 */
	public int deleteDeviceParameterByIds(List<Long> ids);

	/**
	 * 删除蓝牙设备参数信息
	 *
	 * @param id 蓝牙设备参数主键
	 * @return 结果
	 */
	public int deleteDeviceParameterById(String id);

}
