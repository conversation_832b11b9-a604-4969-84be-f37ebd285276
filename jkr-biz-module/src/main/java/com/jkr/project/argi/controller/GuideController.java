package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.Guide;
import com.jkr.project.argi.service.IGuideService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 农业技术指导Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/guide")
public class GuideController extends BaseController {
    @Autowired
    private IGuideService guideService;

    /**
     * 查询农业技术指导列表
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:list')")
    @GetMapping("/list")
    public TableDataInfo list(Guide guide) {
        startPage();
        List<Guide> list = guideService.selectGuideList(guide);
        return getDataTable(list);
    }

    /**
     * 导出农业技术指导列表
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:export')")
    @Log(title = "导出农业技术指导列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Guide guide) {
        List<Guide> list = guideService.selectGuideList(guide);
        ExcelUtil<Guide> util = new ExcelUtil<Guide>(Guide.class);
        util.exportExcel(response, list, "农业技术指导数据");
    }

    /**
     * 获取农业技术指导详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(guideService.selectGuideById(id));
    }

    /**
     * 新增农业技术指导
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:add')")
    @Log(title = "新增农业技术指导", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody Guide guide) {
        return toAjax(guideService.insertGuide(guide));
    }

    /**
     * 修改农业技术指导
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:edit')")
    @Log(title = "修改农业技术指导", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody Guide guide) {
        return toAjax(guideService.updateGuide(guide));
    }

    /**
     * 删除农业技术指导
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:remove')")
    @Log(title = "删除农业技术指导", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(guideService.deleteGuideById(id));
    }

    /**
     * 批量删除农业技术指导
     */
    @PreAuthorize("@ss.hasPermi('argi:guide:batchRemove')")
    @Log(title = "批量删除农业技术指导", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody Guide guide) {
        return toAjax(guideService.deleteGuideByIds(guide.getIds()));
    }
}
