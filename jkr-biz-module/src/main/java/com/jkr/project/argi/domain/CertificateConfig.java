package com.jkr.project.argi.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jkr.project.system.domain.SysArea;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 电子合格证配置对象 bas_certificate_config
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_certificate_config")
public class CertificateConfig extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 类型0=通用;1=指定区域
     */
    @Excel(name = "类型0=通用;1=指定区域")
    private String type;

    /**
     * 区域编码
     */
    @Excel(name = "区域编码")
    private String areaId;

	@TableField(exist = false)
	private SysArea area;

    @TableField(exist = false)
    private String areaName;

    /**
     * 主键集合
     */
	@TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private String treeIds;

    @TableField(exist = false)
    private String certificateTemplateIds;
}
