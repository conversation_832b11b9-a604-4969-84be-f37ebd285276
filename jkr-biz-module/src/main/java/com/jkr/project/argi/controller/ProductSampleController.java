package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.ProductSample;
import com.jkr.project.argi.service.IProductSampleService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 产品样品Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/productSample")
public class ProductSampleController extends BaseController {
    @Autowired
    private IProductSampleService productSampleService;

    /**
     * 查询产品样品列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductSample productSample) {
        startPage();
        List<ProductSample> list = productSampleService.selectProductSampleList(productSample);
        return getDataTable(list);
    }

    /**
     * 导出产品样品列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:export')")
    @Log(title = "导出产品样品列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductSample productSample) {
        List<ProductSample> list = productSampleService.selectProductSampleList(productSample);
        ExcelUtil<ProductSample> util = new ExcelUtil<ProductSample>(ProductSample.class);
        util.exportExcel(response, list, "产品样品数据");
    }

    /**
     * 获取产品样品详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productSampleService.selectProductSampleById(id));
    }

    /**
     * 新增产品样品
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:add')")
    @Log(title = "新增产品样品", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody ProductSample productSample) {
        return toAjax(productSampleService.insertProductSample(productSample));
    }

    /**
     * 修改产品样品
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:edit')")
    @Log(title = "修改产品样品", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody ProductSample productSample) {
        return toAjax(productSampleService.updateProductSample(productSample));
    }

    /**
     * 删除产品样品
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:remove')")
    @Log(title = "删除产品样品", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(productSampleService.deleteProductSampleById(id));
    }

    /**
     * 批量删除产品样品
     */
    @PreAuthorize("@ss.hasPermi('argi:productSample:batchRemove')")
    @Log(title = "批量删除产品样品", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody ProductSample productSample) {
        return toAjax(productSampleService.deleteProductSampleByIds(productSample.getIds()));
    }
}
