package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.FeedbackMapper;
import com.jkr.project.argi.domain.Feedback;
import com.jkr.project.argi.service.IFeedbackService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 反馈信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class FeedbackServiceImpl implements IFeedbackService {
	@Autowired
	private FeedbackMapper feedbackMapper;

	/**
	 * 查询反馈信息
	 *
	 * @param id 反馈信息主键
	 * @return 反馈信息
	 */
	@Override
	public Feedback selectFeedbackById(Long id) {
		return feedbackMapper.selectFeedbackById(id);
	}

	/**
	 * 查询反馈信息列表
	 *
	 * @param feedback 反馈信息
	 * @return 反馈信息
	 */
	@Override
	public List<Feedback> selectFeedbackList(Feedback feedback) {
		return feedbackMapper.selectFeedbackList(feedback);
	}

	/**
	 * 新增反馈信息
	 *
	 * @param feedback 反馈信息
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertFeedback(Feedback feedback) {
		feedback.insertInit(SecurityUtils.getLoginUser().getUsername());
        
			return feedbackMapper.insertFeedback(feedback);
	}

	/**
	 * 修改反馈信息
	 *
	 * @param feedback 反馈信息
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateFeedback(Feedback feedback) {
		feedback.updateInit(SecurityUtils.getLoginUser().getUsername());
        
		return feedbackMapper.updateFeedback(feedback);
	}

	/**
	 * 批量删除反馈信息
	 *
	 * @param ids 需要删除的反馈信息主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteFeedbackByIds(List<Long> ids) {
		return feedbackMapper.logicRemoveByIds(ids);
		//return feedbackMapper.deleteFeedbackByIds(ids);
	}

	/**
	 * 删除反馈信息信息
	 *
	 * @param id 反馈信息主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteFeedbackById(Long id) {
		return feedbackMapper.logicRemoveById(id);
		//return feedbackMapper.deleteFeedbackById(id);
	}

}
