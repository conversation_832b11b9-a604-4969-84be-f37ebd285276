package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateProductItem;
import com.jkr.project.argi.service.ICertificateProductItemService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 合格证产品子表(组合品使用)Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateProductItem")
public class CertificateProductItemController extends BaseController {
	@Autowired
	private ICertificateProductItemService certificateProductItemService;

/**
 * 查询合格证产品子表(组合品使用)列表
 */
@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:list')")
@GetMapping("/list")
	public TableDataInfo list(CertificateProductItem certificateProductItem) {
		startPage();
		List<CertificateProductItem> list = certificateProductItemService.selectCertificateProductItemList(certificateProductItem);
		return getDataTable(list);
	}

	/**
	 * 导出合格证产品子表(组合品使用)列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:export')")
	@Log(title = "导出合格证产品子表(组合品使用)列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, CertificateProductItem certificateProductItem) {
		List<CertificateProductItem> list = certificateProductItemService.selectCertificateProductItemList(certificateProductItem);
		ExcelUtil<CertificateProductItem> util = new ExcelUtil<CertificateProductItem>(CertificateProductItem. class);
		util.exportExcel(response, list, "合格证产品子表(组合品使用)数据");
	}

	/**
	 * 获取合格证产品子表(组合品使用)详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(certificateProductItemService.selectCertificateProductItemById(id));
	}

	/**
	 * 新增合格证产品子表(组合品使用)
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:add')")
	@Log(title = "新增合格证产品子表(组合品使用)", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody CertificateProductItem certificateProductItem) {
		return toAjax(certificateProductItemService.insertCertificateProductItem(certificateProductItem));
	}

	/**
	 * 修改合格证产品子表(组合品使用)
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:edit')")
	@Log(title = "修改合格证产品子表(组合品使用)", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody CertificateProductItem certificateProductItem) {
		return toAjax(certificateProductItemService.updateCertificateProductItem(certificateProductItem));
	}

	/**
	 * 删除合格证产品子表(组合品使用)
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:remove')")
	@Log(title = "删除合格证产品子表(组合品使用)", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(certificateProductItemService.deleteCertificateProductItemById(id));
	}

	/**
	 * 批量删除合格证产品子表(组合品使用)
	 */
	@PreAuthorize("@ss.hasPermi('argi:certificateProductItem:batchRemove')")
	@Log(title = "批量删除合格证产品子表(组合品使用)", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody CertificateProductItem certificateProductItem) {
		return toAjax(certificateProductItemService.deleteCertificateProductItemByIds(certificateProductItem.getIds()));
	}
}
