package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.CertificateConfigTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * 电子合格证配置-模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface CertificateConfigTemplateMapper extends BaseMapper<CertificateConfigTemplate>{
	/**
	 * 查询电子合格证配置-模板
	 *
	 * @param id 电子合格证配置-模板主键
	 * @return 电子合格证配置-模板
	 */
	public CertificateConfigTemplate selectCertificateConfigTemplateById(Long id);

	/**
	 * 查询电子合格证配置-模板列表
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 电子合格证配置-模板集合
	 */
	public List<CertificateConfigTemplate> selectCertificateConfigTemplateList(CertificateConfigTemplate certificateConfigTemplate);

	/**
	 * 新增电子合格证配置-模板
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 结果
	 */
	public int insertCertificateConfigTemplate(CertificateConfigTemplate certificateConfigTemplate);

	/**
	 * 修改电子合格证配置-模板
	 *
	 * @param certificateConfigTemplate 电子合格证配置-模板
	 * @return 结果
	 */
	public int updateCertificateConfigTemplate(CertificateConfigTemplate certificateConfigTemplate);

	/**
	 * 删除电子合格证配置-模板
	 *
	 * @param id 电子合格证配置-模板主键
	 * @return 结果
	 */
	public int deleteCertificateConfigTemplateById(Long id);

	/**
	 * 批量删除电子合格证配置-模板
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteCertificateConfigTemplateByIds(Long[] ids);

	/**
	 * 批量逻辑删除电子合格证配置-模板
	 *
	 * @param  ids 电子合格证配置-模板主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过电子合格证配置-模板主键id逻辑删除信息
	 *
	 * @param  id 电子合格证配置-模板主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);

	public int insertBatch(@Param("list")List<CertificateConfigTemplate> list);

	public int deleteByCertificateConfigId(CertificateConfigTemplate certificateConfigTemplate);
}
