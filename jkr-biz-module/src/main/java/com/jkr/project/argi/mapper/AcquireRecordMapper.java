package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.AcquireRecord;

/**
 * 收购记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Mapper
public interface AcquireRecordMapper extends BaseMapper<AcquireRecord>{
	/**
	 * 查询收购记录
	 *
	 * @param id 收购记录主键
	 * @return 收购记录
	 */
	public AcquireRecord selectAcquireRecordById(String id);

	/**
	 * 查询收购记录列表
	 *
	 * @param acquireRecord 收购记录
	 * @return 收购记录集合
	 */
	public List<AcquireRecord> selectAcquireRecordList(AcquireRecord acquireRecord);

	/**
	 * 新增收购记录
	 *
	 * @param acquireRecord 收购记录
	 * @return 结果
	 */
	public int insertAcquireRecord(AcquireRecord acquireRecord);

	/**
	 * 修改收购记录
	 *
	 * @param acquireRecord 收购记录
	 * @return 结果
	 */
	public int updateAcquireRecord(AcquireRecord acquireRecord);

	/**
	 * 删除收购记录
	 *
	 * @param id 收购记录主键
	 * @return 结果
	 */
	public int deleteAcquireRecordById(String id);

	/**
	 * 批量删除收购记录
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteAcquireRecordByIds(String[] ids);

	/**
	 * 批量逻辑删除收购记录
	 *
	 * @param  ids 收购记录主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过收购记录主键id逻辑删除信息
	 *
	 * @param  id 收购记录主键
	 * @return 结果
	 */
	public int logicRemoveById(String id);
}
