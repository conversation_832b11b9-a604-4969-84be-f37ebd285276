package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.FeaturedProductsMapper;
import com.jkr.project.argi.domain.FeaturedProducts;
import com.jkr.project.argi.service.IFeaturedProductsService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 特色产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class FeaturedProductsServiceImpl implements IFeaturedProductsService {
	@Autowired
	private FeaturedProductsMapper featuredProductsMapper;

	/**
	 * 查询特色产品
	 *
	 * @param id 特色产品主键
	 * @return 特色产品
	 */
	@Override
	public FeaturedProducts selectFeaturedProductsById(Long id) {
		return featuredProductsMapper.selectFeaturedProductsById(id);
	}

	/**
	 * 查询特色产品列表
	 *
	 * @param featuredProducts 特色产品
	 * @return 特色产品
	 */
	@Override
	public List<FeaturedProducts> selectFeaturedProductsList(FeaturedProducts featuredProducts) {
		return featuredProductsMapper.selectFeaturedProductsList(featuredProducts);
	}

	/**
	 * 新增特色产品
	 *
	 * @param featuredProducts 特色产品
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertFeaturedProducts(FeaturedProducts featuredProducts) {
		featuredProducts.insertInit(SecurityUtils.getLoginUser().getUsername());
        
			return featuredProductsMapper.insertFeaturedProducts(featuredProducts);
	}

	/**
	 * 修改特色产品
	 *
	 * @param featuredProducts 特色产品
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateFeaturedProducts(FeaturedProducts featuredProducts) {
		featuredProducts.updateInit(SecurityUtils.getLoginUser().getUsername());
        
		return featuredProductsMapper.updateFeaturedProducts(featuredProducts);
	}

	/**
	 * 批量删除特色产品
	 *
	 * @param ids 需要删除的特色产品主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteFeaturedProductsByIds(List<Long> ids) {
		return featuredProductsMapper.logicRemoveByIds(ids);
		//return featuredProductsMapper.deleteFeaturedProductsByIds(ids);
	}

	/**
	 * 删除特色产品信息
	 *
	 * @param id 特色产品主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteFeaturedProductsById(Long id) {
		return featuredProductsMapper.logicRemoveById(id);
		//return featuredProductsMapper.deleteFeaturedProductsById(id);
	}

}
