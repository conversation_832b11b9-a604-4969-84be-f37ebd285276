package com.jkr.project.argi.service;

import java.util.List;
import java.util.Map;

import com.jkr.common.enums.EnumSubject;
import com.jkr.project.argi.domain .CertificateConfig;

/**
 * 电子合格证配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface ICertificateConfigService {
	/**
	 * 查询电子合格证配置
	 *
	 * @param id 电子合格证配置主键
	 * @return 电子合格证配置
	 */
	public CertificateConfig selectCertificateConfigById(Long id);

	/**
	 * 查询电子合格证配置列表
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 电子合格证配置集合
	 */
	public List<CertificateConfig> selectCertificateConfigList(CertificateConfig certificateConfig);

	/**
	 * 新增电子合格证配置
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 结果
	 */
	public int insertCertificateConfig(CertificateConfig certificateConfig);

	/**
	 * 修改电子合格证配置
	 *
	 * @param certificateConfig 电子合格证配置
	 * @return 结果
	 */
	public int updateCertificateConfig(CertificateConfig certificateConfig);

	/**
	 * 批量删除电子合格证配置
	 *
	 * @param ids 需要删除的电子合格证配置主键集合
	 * @return 结果
	 */
	public int deleteCertificateConfigByIds(List<Long> ids);

	/**
	 * 删除电子合格证配置信息
	 *
	 * @param id 电子合格证配置主键
	 * @return 结果
	 */
	public int deleteCertificateConfigById(Long id);

	public void save(CertificateConfig certificateConfig);

	public void delete(CertificateConfig certificateConfig);

	public List<EnumSubject> findCertificateConfigTypeList();

	public List<Map<String,Object>> findDeviceTemplateTreeList();

	public String getDeviceTemplateIds(String certificateConfigId);

	public List<Object> getCertificateConfig(CertificateConfig certificateConfig);

}
