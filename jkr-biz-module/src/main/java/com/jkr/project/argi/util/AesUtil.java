package com.jkr.project.argi.util;

import com.jkr.common.utils.Encodes;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * AES的加密和解密
 */

public class AesUtil {
    //密钥
    public static final String PASSWORD = "7DJgTLRZ17WLTm19";
    //加密算法
    private static final String ALGORITHMSTR = "AES/CBC/PKCS5Padding";
    //偏移量
    private static final String IV_STRING = "JZqrMKbMk5H6ewEE";

    /**
     * @Decription : CBC算法解密
     */
    public static String aesDecrypt(String encrypt) {

        try {
            return aesDecrypt(encrypt, PASSWORD);

        } catch (Exception e) {

            throw new RuntimeException("CBC解密出错");
        }
    }


    /**
     * @Decription : CBC 算法加密
     */
    public static String aesEncrypt(String content) {

        try {

            return aesEncrypt(content, PASSWORD).replaceAll("\r\n", "").replaceAll("\n", "");

        } catch (Exception e) {

            e.printStackTrace();

            return "";

        }
    }

    /**
     * @param : 待编码的byte[]
     * @return :  编码后的base 64 code
     * @Decription : base 64 encode
     */
    private static String base64Encode(byte[] bytes) {

        return Encodes.encodeBase64(bytes);

    }

    /**
     * @param : 待解码的base 64 code
     * @return :  解码后的byte[]
     * @Decription : base 64 decode
     */
    private static byte[] base64Decode(String base64Code) throws Exception {

        return Encodes.decodeBase64(base64Code);

    }

    /**
     * @param : content 待加密的内容
     * @param : encryptKey 加密密钥
     * @return :  加密后的byte[]
     */
    private static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {

        KeyGenerator kgen = KeyGenerator.getInstance("AES");

        kgen.init(128);

        byte[] initParam = IV_STRING.getBytes();

        IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);

        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);

        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"), ivParameterSpec);

        return cipher.doFinal(content.getBytes("utf-8"));

    }

    /**
     * @param : content 待加密的内容
     * @param : encryptKey 加密密钥
     * @return :  加密后的base 64 code
     */
    public static String aesEncrypt(String content, String encryptKey) throws Exception {

        return base64Encode(aesEncryptToBytes(content, encryptKey));

    }

    /**
     * @param : encryptBytes 待解密的byte[]
     * @param : decryptKey 解密密钥
     * @return :  解密后的String
     */
    private static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {

        KeyGenerator kgen = KeyGenerator.getInstance("AES");

        kgen.init(128);

        byte[] initParam = IV_STRING.getBytes();

        IvParameterSpec ivParameterSpec = new IvParameterSpec(initParam);

        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);

        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"), ivParameterSpec);

        byte[] decryptBytes = cipher.doFinal(encryptBytes);

        return new String(decryptBytes);

    }

    /**
     * @param : encryptStr 待解密的base 64 code
     * @param : decryptKey 解密密钥
     * @return :  解密后的string
     * @Decription : 将base 64 code AES解密
     */
    public static String aesDecrypt(String encryptStr, String decryptKey) throws Exception {

        return aesDecryptByBytes(base64Decode(encryptStr), decryptKey);

    }

/**
 * 测试
 * aesEncrypt 加密方法
 * aesDecrypt 解密方法
 */
    /*public static void main(String[] args) throws Exception {

        Map<String, Object> con = new HashMap<>();

        con.put("testkey", "testValue");

        String beforeEncrypt = JSON.toJSONString(con);

        System.out.println("加密前：" + beforeEncrypt);

        String afterEncrypt = aesEncrypt(beforeEncrypt, PASSWORD);

        System.out.println("加密后：" + afterEncrypt);

        System.out.println("解密前：" + afterEncrypt);

        String decrypt = aesDecrypt(afterEncrypt, PASSWORD);

        System.out.println("解密后：" + decrypt);
    }*/
}
