package com.jkr.project.argi.service.impl;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.github.yitter.idgen.YitIdHelper;
import com.jkr.common.enums.EnumProperty;
import com.jkr.common.enums.ProductMixTypeEnum;
import com.jkr.common.utils.*;
import com.jkr.framework.security.LoginUser;
import com.jkr.project.argi.domain.*;
import com.jkr.project.argi.mapper.CertificateProductInspectionMapper;
import com.jkr.project.argi.mapper.CertificateProductItemMapper;
import com.jkr.project.argi.service.*;
import com.jkr.project.argi.util.BlockChainUtils;
import com.jkr.project.argi.util.SerialNumberUtils;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.CertificateMapper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * 合格证Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class CertificateServiceImpl implements ICertificateService {

	private static final Logger logger = LoggerFactory.getLogger(CertificateServiceImpl.class);

	@Autowired
	private CertificateMapper certificateMapper;
	@Lazy
	@Autowired
	private IEntService entService;
	@Lazy
	@Autowired
	private ICertificateNoService certificateNoService;
	@Lazy
	@Autowired
	private IProductSampleService productSampleService;
	@Lazy
	@Autowired
	private IProductService productService;
	@Autowired
	private IAutographService autographService;
	@Lazy
	@Autowired
	private CertificateProductInspectionMapper certificateProductInspectionDao;
	@Autowired
	private CertificateProductItemMapper certificateProductItemDao;
	@Lazy
	@Autowired
	private ISysFileService fileService;

	@Value("${serialNumberLength}")
	private String serialNumberLength;
	@Value("${blockChainEnabled}")
	private String blockChainEnabled;
	@Value("${dataCentrePushEnabled}")
	private String dataCentrePushEnabled;
	@Value("${dataCentre.pushAreaCode}")
	private String dataCentrePushAreaCode;
	@Value("${dataCentre.appId}")
	private String dataCentreAppId;
	@Value("${dataCentre.notifyUrl}")
	private String dataCentreNotifyUrl;
	@Value("${dataCentre.secret}")
	private String dataCentreSecret;
	@Value("${dataCentre.gateway}")
	private String dataCentreGateway;
	@Value("${dataCentre.traceApi}")
	private String dataCentreTraceApi;

	/**
	 * 查询合格证
	 *
	 * @param id 合格证主键
	 * @return 合格证
	 */
	@Override
	public Certificate selectCertificateById(Long id) {
		List<String> stringList = certificateProductInspectionDao.getProductInspectionByCertificateId(id+"");
		Certificate certificate = certificateMapper.selectCertificateById(id);
		if(certificate == null){
			return null;
		}
		if(!stringList.isEmpty()){
			certificate.setInspectionSituationList(stringList);
		}
		if(StringUtils.isNotEmpty(certificate.getProductId())){
			Product product = productService.selectProductById(Long.parseLong(certificate.getProductId()));
			certificate.setProduct(product);
		}
		if (ProductMixTypeEnum.MIX_TYPE_1.getKey().equals(certificate.getProductMixFlag())) {
			CertificateProductItem certificateProductItem = new CertificateProductItem();
			certificateProductItem.setCertificateId(certificate.getId()+"");
			certificate.setCertificateProductItemList(certificateProductItemDao.selectCertificateProductItemList(certificateProductItem));
		}
		return certificate;
	}

	/**
	 * 查询合格证列表
	 *
	 * @param certificate 合格证
	 * @return 合格证
	 */
	@Override
	public List<Certificate> selectCertificateList(Certificate certificate) {
		return certificateMapper.selectCertificateList(certificate);
	}

	/**
	 * 新增合格证
	 *
	 * @param certificate 合格证
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertCertificate(Certificate certificate) {
		certificate.insertInit(SecurityUtils.getLoginUser().getUsername());
		return certificateMapper.insertCertificate(certificate);
	}

	/**
	 * 修改合格证
	 *
	 * @param certificate 合格证
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateCertificate(Certificate certificate) {
		certificate.updateInit(SecurityUtils.getLoginUser().getUsername());

		return certificateMapper.updateCertificate(certificate);
	}

	/**
	 * 批量删除合格证
	 *
	 * @param ids 需要删除的合格证主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateByIds(List<Long> ids) {
		return certificateMapper.logicRemoveByIds(ids);
		//return certificateMapper.deleteCertificateByIds(ids);
	}

	/**
	 * 删除合格证信息
	 *
	 * @param id 合格证主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteCertificateById(Long id) {
		return certificateMapper.logicRemoveById(id);
		//return certificateMapper.deleteCertificateById(id);
	}

	/**
	 * 电子证展示-查询接口
	 * @param id
	 * @return
	 */
	@Override
	public Certificate getForElectronicShow(String id) {
		Certificate certificate = certificateMapper.selectCertificateById(Long.parseLong(id));
		if (certificate == null) {
			return null;
		}
		SysFile attachment = new SysFile();
		attachment.setTableId(Long.parseLong(certificate.getEntId()));
		attachment.setFieldType("sealPic");
		List<SysFile> sealPicList = fileService.findUrlsFileList(attachment);
		certificate.setSealPicList(sealPicList);
		certificate.setAutograph(autographService.getByEntId(certificate.getEntId()));

		List<com.jkr.project.argi.domain.CertificateNo> certificateNoList=certificateNoService.getFirstByCertificateId(id);
		if(!certificateNoList.isEmpty()) {
			certificate.setCertificateNoList(certificateNoList);
		}
		return certificate;
	}

	@Override
	public List<Certificate> findPageForWechat(Certificate certificate) {
		// 拼接 机构与地区编码绑定查询 2021-06-07 胡志国添加

		return certificateMapper.selectCertificateList(certificate);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updatePrintCount(String id, String printCountNew) {
		Certificate certificate = new Certificate();
		certificate.setId(Long.parseLong(id));
		certificate.setPrintCount(printCountNew);
		certificate.updateInit(SecurityUtils.getLoginUser().getUsername());
		certificateMapper.updateCertificate(certificate);
		TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
				new TransactionSynchronizationAdapter() {
					@Override
					public void afterCommit() {
						// 关联区块链 必须使用 certificateService
						joinBlockChain(certificate.getId()+"");
					}
				}
		);
	}

	@Override
	@Async
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void joinBlockChain(String id) {
		Certificate certificate = certificateMapper.selectCertificateById(Long.parseLong(id));
		Map<String, Object> resultMap = new HashMap<>();
		String blockchainId = "";
		String msg ="";
		try{
			if (StringUtils.isNotBlank(id)){
				Map<String, Object> map = new HashMap<>();
				// 暂时只要数据更新就重新生成一个区块链id
				/*blockchainId = certificate.getBlockChainId();
				if(StringUtils.isNotBlank(blockchainId)){
					map.put("_id",blockchainId); // 区块链id
				}*/
				map.put("id",certificate.getId()); // 合格证主键
				map.put("batchNo",certificate.getBatchNo()); // 合格证编号
				map.put("certificateIssuingDate", DateUtil.format(certificate.getCreateTime(), "yyyy-MM-dd HH:mm:ss")); //合格证开具时间
				map.put("productId",certificate.getProductId()); // 产品id
				map.put("productName",certificate.getProductName()); // 产品名称
				map.put("productIntroduction",certificate.getProductIntroduction()); // 产品介绍
				map.put("productProvince", certificate.getProductProvince()); // 产品生产-省份code
				map.put("productCity", certificate.getProductCity()); // 产品生产-城市code
				map.put("productCounty", certificate.getProductCounty()); // 产品生产-县区code
				map.put("productAddress", certificate.getProductAddress()); // 产品生产所在区域
				map.put("productDetail",certificate.getProductDetail()); // 产品生产地址
				map.put("productSortName",certificate.getProductSortName()); // 产品类别
				map.put("productCertificationName",certificate.getProductCertificationName()); // 产品认证名称
				map.put("productNum",certificate.getProductNum()); // 产品数量/重量
				map.put("productUnitName",certificate.getProductUnitName()); // 产品单位（斤、个等）
				map.put("productionDate",DateUtil.format(certificate.getProductionDate(), "yyyy-MM-dd HH:mm:ss")); // 产品生产日期
				map.put("entId",certificate.getEntId()); // 产品生产单位
				map.put("entBusinessType",certificate.getEntBusinessType()); // 主体类型
				map.put("entType",certificate.getEntType()); // 主体性质
				map.put("entDetail",certificate.getEntDetail()); // 主体详细地址
				map.put("entSocialCode",certificate.getEntSocialCode()); // 主体-统一社会信用代码
				map.put("entContactsPhone",certificate.getEntContactsPhone()); // 主体-联系电话
				map.put("entCompanyIntroduction",certificate.getEntCompanyIntroduction()); // 主体简介
				map.put("entHonor",certificate.getEntHonor()); // 主体荣誉
				map.put("printCount",certificate.getPrintCount()); // 打印数量
				map.put("createDate",DateUtil.format(certificate.getCreateTime(), "yyyy-MM-dd HH:mm:ss")); // 数据创建时间
				map.put("updateDate",DateUtil.format(certificate.getUpdateTime(), "yyyy-MM-dd HH:mm:ss")); // 数据更新时间
				map.put("inspectionSituation", certificate.getInspectionSituation()); // 检测情况
				map.put("sampleNo", certificate.getSampleNo()); // 当前产品样品编号
				//ResponseEntity<Map> responseEntity = BlockChainUtils.checkById("b72077ddee491976953251c1736bcbc9b7863ef03e1dd02c120d82361cbb4759");
				ResponseEntity<Map> responseEntity = BlockChainUtils.insertData(map);
				resultMap = responseEntity.getBody();
				if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK){
					logger.info("【插入失败：区块链】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,resultMap.get("message").toString());
					msg ="失败";
					//throw new RuntimeException(msg);
				}

				if(resultMap.get("code").equals(200)){
					if(StringUtils.isBlank(blockchainId)) {
						blockchainId = ((Map<String, Object>) resultMap.get("data")).get("txId").toString();
						//给当前追溯数据添加区块链ID
						updateBlockChainId(id, blockchainId);
					}
				}else{
					//throw new RuntimeException(resultMap.get("message").toString());
				}

				logger.info("【插入成功】 ID为：{} 的追溯数据插入成功。区块链ID为：{}",id,blockchainId);
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("【插入失败】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,e.getMessage());
			msg ="失败";
			// 确保数据回滚
			//throw new RuntimeException(msg);
		}
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void updateBlockChainId(String id, String blockChainId) {
		certificateMapper.updateBlockChainId(id, blockChainId);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void insertAll(Certificate certificate) {
		Ent ent = entService.getByTableId(SecurityUtils.getDeptId()+"");
		Product product = productService.selectProductById(Long.parseLong(certificate.getProductId()));
		if(!PropertyEnum.InspectionSituationEnum.IS_02.getKey().equals(certificate.getInspectionSituation())) {
			product.setCurrentSampleNo(null);
		}
		certificate.setEnt(ent);
		certificate.setProduct(product);
		// 批次、流水号生成
		//this.batchNoGeneration(certificate,ent,product);
		/**
		 * 使用新流水号生成规则
		 * 2020年8月4日15:16:58
		 * lxy
		 */
		this.batchGenerationSerialNumber(certificate,ent,product);
		certificate.insertInit(SecurityUtils.getLoginUser().getUsername());
		certificateMapper.insertCertificate(certificate);
		/**
		 * 更新主体合格证开具信息
		 * 2021年1月13日16:36:06
		 * lxy
		 */
		this.entService.updateCertificateAmount(ent.getId()+"", Integer.valueOf(certificate.getPrintCount()));

		/**
		 * 更新产品打印数量信息
		 * 2022年1月10日14:31:08
		 * lxy
		 */
		this.productService.updatePrintAmount(certificate.getProductId(), Integer.valueOf(certificate.getPrintCount()));
		// 企业、产品相关附件与合格证关联
		fileService.copyAttachment(ent.getId(),"bas_ent",certificate.getId(),"bas_certificate");
		fileService.copyAttachment(product.getId(),"bas_product",certificate.getId(),"bas_certificate");
		/*TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
				new TransactionSynchronizationAdapter() {
					@Override
					public void afterCommit() {
						// 关联区块链 必须使用 certificateService
						certificateService.joinBlockChain(certificate.getId());
					}
				}
		);*/
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void batchGenerationSerialNumber(Certificate certificate, Ent ent, Product product) {
		certificate.insertInit(SecurityUtils.getLoginUser().getUsername());
		//1-6位主体所在区域
		String code = ent.getCounty();
		//第7位主体类型
		String businessType = ent.getBusinessType();
		//第8位主体性质
		String entType = ent.getEntType();
		//第9-10位产品分类
		String productSortCode = product.getProductSortCode();
		//第11-16位年月日
		String date=DateUtil.format(new Date(), "yyMMdd");
		//第17-23位  流水号
		String batchNo=code+businessType+entType+productSortCode+date;
		certificate.setBatchNo(batchNo);
		batchGenerationSerialNumber(certificate);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void batchGenerationSerialNumber(Certificate certificate) {
		String batchNo = certificate.getBatchNo();
		//批量保存流水号
		Integer printCount=Integer.parseInt(certificate.getPrintCount());
		List<CertificateNo> certificateNoList=new ArrayList<CertificateNo>();
		for(int i=0;i<printCount;i++) {
			Integer serialNumber= SerialNumberUtils.getInstance().geneterNextNumber(batchNo);
			//完整合格证号
			String fullNumber=batchNo+String.format("%0"+serialNumberLength+"d", serialNumber);
			CertificateNo certificateNo=new CertificateNo(certificate.getId()+"", batchNo, serialNumber, fullNumber);
			//由于跳号问题，如再次开具，暂不更新范围字段 --by Wlq 20250115
			if (ObjectUtil.isEmpty(certificate.getId())) {
				//主表记录开始、截止流水号
				if(i==0) {
					//首位流水号
					certificate.setBeginSerialNumber(fullNumber);
					//末尾流水号
					certificate.setEndSerialNumber(fullNumber);
				}else if(i==printCount-1) {
					//末尾流水号
					certificate.setEndSerialNumber(fullNumber);
				}

				//电子证标识
				if("1".equals(certificate.getElectricFlag())) {
					certificateNo.setElectricFlag("1");
				}
			}
			certificateNo.insertInit(SecurityUtils.getLoginUser().getUsername());
			certificateNoList.add(certificateNo);
		}

		if(!certificateNoList.isEmpty()) {
			this.certificateNoService.insertBatch(certificateNoList);
			if("true".equals(blockChainEnabled) || "1".equals(blockChainEnabled)) {
				TransactionSynchronizationManager.registerSynchronization( //在一个有事务的方法中，等事务提交后调另外一个方法可以用
						new TransactionSynchronizationAdapter() {
							@Override
							public void afterCommit() {
								// 关联区块链 必须使用 certificateService
								joinBlockChainCertificateNo(certificate.getId()+"", certificateNoList);
							}
						}
				);
			}
			/**
			 * 数据中心推送
			 * lxy
			 * 2021年7月12日8:47:06
			 */
			if("true".equals(dataCentrePushEnabled) || "1".equals(dataCentrePushEnabled)) {
				/**
				 * 按允许推送区域推送数据
				 * lxy
				 * 2021年8月3日16:17:21
				 */
				if(dataCentrePushAreaCode.equals(certificate.getEnt().getCity())) {
					dataCentrePush(certificateNoList, certificate);
				}
			}
			certificate.setCertificateNoList(certificateNoList);
		}
	}

	@Override
	@Async
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void joinBlockChainCertificateNo(String id, List<CertificateNo> list) {
		Certificate certificate = certificateMapper.selectCertificateById(Long.parseLong(id));
		try{
			if (StringUtils.isNotBlank(id)){
				Map<String, Object> map = new HashMap<>();
				for(CertificateNo certificateNo:list) {
					String blockchainId = "";
					Map<String, Object> resultMap = new HashMap<>();
					map.put("id",certificateNo.getId()); // 合格证流水主键
					map.put("certificateId",certificateNo.getCertificateId()); // 合格证主键
					map.put("batchNo",certificateNo.getBatchNo()); // 合格证批次号
					map.put("fullNumber",certificateNo.getFullNumber()); // 合格证全编号
					map.put("certificateIssuingDate", DateUtils.formatDate(certificate.getCreateTime(), "yyyy-MM-dd HH:mm:ss")); //合格证开具时间

					map.put("productId",certificate.getProductId()); // 产品id
					map.put("productName",certificate.getProductName()); // 产品名称
					map.put("productIntroduction",certificate.getProductIntroduction()); // 产品介绍
					map.put("productProvince", certificate.getProductProvince()); // 产品生产-省份code
					map.put("productCity", certificate.getProductCity()); // 产品生产-城市code
					map.put("productCounty", certificate.getProductCounty()); // 产品生产-县区code
					map.put("productAddress", certificate.getProductAddress()); // 产品生产所在区域
					map.put("productDetail",certificate.getProductDetail()); // 产品生产地址
					map.put("productSortCode", certificate.getProductSortCode()); // 产品类别code
					map.put("productSortName",certificate.getProductSortName()); // 产品类别
					map.put("productCertificationName",certificate.getProductCertificationName()); // 产品认证名称
					map.put("productNum",certificate.getProductNum()); // 产品数量/重量
					map.put("productUnitCode", certificate.getProductUnitCode()); // 产品单位（斤、个等）code
					map.put("productUnitName",certificate.getProductUnitName()); // 产品单位（斤、个等）
					map.put("productionDate", DateUtils.formatDate(certificate.getProductionDate(), "yyyy-MM-dd HH:mm:ss")); // 产品生产日期

					map.put("entId",certificate.getEntId()); // 产品生产单位
					map.put("entName", certificate.getEntName()); // 主体名称
					map.put("entBusinessType",certificate.getEntBusinessType()); // 主体类型
					map.put("entType",certificate.getEntType()); // 主体性质
					map.put("entMainType",certificate.getEntMainType()); // 主体类别
					map.put("entFarmType",certificate.getEntFarmType()); // 养殖分类 (0:牧业 1:渔业)
					map.put("entCardNo", certificate.getEntCardNo()); // 身份证号
					map.put("entLegalPerson", certificate.getEntLegalPerson()); // 法人
					map.put("entAddress", certificate.getEntAddress()); // 主体所在区域
					map.put("entDetail",certificate.getEntDetail()); // 主体详细地址
					map.put("entSocialCode",certificate.getEntSocialCode()); // 主体-统一社会信用代码
					map.put("entContactsPhone",certificate.getEntContactsPhone()); // 主体-联系电话
					map.put("entAutograph", certificate.getEntAutograph()); // 签名base64
					map.put("entCompanyIntroduction",certificate.getEntCompanyIntroduction()); // 主体简介
					map.put("entHonor",certificate.getEntHonor()); // 主体荣誉
					map.put("printCount",certificateNo.getPrintCount()); // 打印数量 默认1 补打业务后 需要调整
					map.put("createDate", DateUtils.formatDate(certificateNo.getCreateTime(), "yyyy-MM-dd HH:mm:ss")); // 数据创建时间
					map.put("updateDate", DateUtils.formatDate(certificateNo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss")); // 数据更新时间
					map.put("inspectionSituation", certificate.getInspectionSituation()); // 检测情况
					map.put("sampleNo", certificate.getSampleNo()); // 当前产品样品编号
					ResponseEntity<Map> responseEntity = BlockChainUtils.insertData(map);
					resultMap = responseEntity.getBody();
					if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK){
						logger.info("【插入失败：区块链】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,resultMap.get("message").toString());
						//throw new RuntimeException(msg);
					}

					if(resultMap.get("code").equals(200)){
						if(StringUtils.isBlank(blockchainId)) {
							blockchainId = ((Map<String, Object>) resultMap.get("data")).get("txId").toString();
							//给当前追溯数据添加区块链ID
							certificateNoService.updateBlockChainId(certificateNo.getId()+"",blockchainId);
						}
					}else{
						//throw new RuntimeException(resultMap.get("message").toString());
					}

					logger.info("【插入成功】 ID为：{} 的追溯数据插入成功。区块链ID为：{}",id,blockchainId);
				}
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("【插入失败】 ID为：{} 的追溯数据插入失败。失败原因：{}",id,e.getMessage());
			// 确保数据回滚
			//throw new RuntimeException(msg);
		}
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void dataCentrePush(List<CertificateNo> certificateNoList, Certificate certificate) {
		ExecutorService fixedThreadPool = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
		fixedThreadPool.execute(new Runnable() {
			public void run() {
				try {
					dataCentrePushDo(certificateNoList,certificate);
					Thread.sleep(100);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		});
		//手动关闭线程池
		fixedThreadPool.shutdown();
	}

	@Override
	public void dataCentrePushDo(List<CertificateNo> certificateNoList, Certificate certificate) {
		if (null != certificateNoList && !certificateNoList.isEmpty()) {
			Map<String, String> data = new HashMap<>();
			data.put(SXSignUtil.SXConstants.FIELD_NONCE_STR, SXSignUtil.generateNonceStr());
			data.put(SXSignUtil.SXConstants.FIELD_TIME_STAMP, SXSignUtil.getCurrentTimestampStr());
			data.put(SXSignUtil.SXConstants.FIELD_APPID, dataCentreAppId);
			data.put(SXSignUtil.SXConstants.FIELD_NOTIFY_URL, dataCentreNotifyUrl);
			for (CertificateNo item : certificateNoList) {
				data.put("id", YitIdHelper.nextId()+"");
				data.put("no", item.getFullNumber());
				data.put("branchNo", item.getBatchNo());
				data.put("nodeId", certificate.getEnt().getId()+"");
				data.put("nodeName", certificate.getEnt().getName());
				String nodeType = "";
				if (EnumProperty.BusinessTypeEnum.TYPE_0.getKey().equals(certificate.getEnt().getBusinessType())) {// 种植
					nodeType = EnumProperty.NodeTypeEnum.TYPE_0.getKey();
				} else if (EnumProperty.BusinessTypeEnum.TYPE_1.getKey().equals(certificate.getEnt().getBusinessType())) {// 养殖
					nodeType = EnumProperty.NodeTypeEnum.TYPE_1.getKey();
				}
				data.put("nodeType", nodeType);
				data.put("dataId", item.getId()+"");
				data.put("dataTime", DateUtils.formatDate(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
				data.put("areaCode", certificate.getEnt().getCounty());
				data.put("address", certificate.getEnt().getDetail());
				data.put("nodeSource", "certificate");

				data.put("productName", certificate.getProductName());
				data.put("productId", certificate.getProductId());
				try {
					String sign = SXSignUtil.sign(data, dataCentreSecret);
					data.put(SXSignUtil.SXConstants.FIELD_SIGN, sign);
					String apiPath=dataCentreGateway+dataCentreTraceApi;
					String response = SXRequest.requestOnce(apiPath, JSONUtil.toJsonStr(data),
							null);
					/*if (response.contains(PushFlagEnum.PUSH_1.getValue())) {
						// trace.setPushFlag(PushFlagEnum.PUSH_1.getKey());
						// this.traceService.update(trace);
					}*/
				} catch (Exception e) {

				}
			}
		}
	}

	@Override
	public Integer findPrintTotalNum(Certificate certificate) {
		return certificateMapper.findPrintTotalNum(certificate);
	}

	@Override
	public Map<String, Object> getSummaryInfo(String entId) {
		return certificateMapper.getSummaryInfo(entId);
	}

	@Override
	public List<Certificate> incrementalQuery(Date date) {
		if (date == null) {
			return List.of();
		}
		return certificateMapper.incrementalQuery(date);
	}


}
