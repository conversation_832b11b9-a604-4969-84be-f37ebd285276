package com.jkr.project.argi.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jkr.project.argi.constant.PublishStatusConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;

import java.util.List;

/**
 * 信息发布对象 bas_notice
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bas_notice")
public class BasNotice extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 内容
     */
    @Excel(name = "内容")
    private String content;


    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    /**
     * 状态值 0-未发布 1-已发布 2-已下架
     */
    private String status;

    /**
     * 主键集合
     */
    @TableField(exist = false)
    private List<Long> ids;

    /**
     * 发布开始时间
     */
    @TableField(exist = false)
    private String publishBeginTime;

    /**
     * 发布结束时间
     */
    @TableField(exist = false)
    private String publishEndTime;

    /**
     * 创建开始时间
     */
    @TableField(exist = false)
    private String createBeginTime;

    /**
     * 创建结束时间
     */
    @TableField(exist = false)
    private String createEndTime;
}
