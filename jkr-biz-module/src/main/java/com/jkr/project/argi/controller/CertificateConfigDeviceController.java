package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateConfigDevice;
import com.jkr.project.argi.service.ICertificateConfigDeviceService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 电子合格证配置-设备Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateConfigDevice")
public class CertificateConfigDeviceController extends BaseController {
    @Autowired
    private ICertificateConfigDeviceService certificateConfigDeviceService;

    /**
     * 查询电子合格证配置-设备列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertificateConfigDevice certificateConfigDevice) {
        startPage();
        List<CertificateConfigDevice> list = certificateConfigDeviceService.selectCertificateConfigDeviceList(certificateConfigDevice);
        return getDataTable(list);
    }

    /**
     * 导出电子合格证配置-设备列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:export')")
    @Log(title = "导出电子合格证配置-设备列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertificateConfigDevice certificateConfigDevice) {
        List<CertificateConfigDevice> list = certificateConfigDeviceService.selectCertificateConfigDeviceList(certificateConfigDevice);
        ExcelUtil<CertificateConfigDevice> util = new ExcelUtil<CertificateConfigDevice>(CertificateConfigDevice.class);
        util.exportExcel(response, list, "电子合格证配置-设备数据");
    }

    /**
     * 获取电子合格证配置-设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(certificateConfigDeviceService.selectCertificateConfigDeviceById(id));
    }

    /**
     * 新增电子合格证配置-设备
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:add')")
    @Log(title = "新增电子合格证配置-设备", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody CertificateConfigDevice certificateConfigDevice) {
        return toAjax(certificateConfigDeviceService.insertCertificateConfigDevice(certificateConfigDevice));
    }

    /**
     * 修改电子合格证配置-设备
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:edit')")
    @Log(title = "修改电子合格证配置-设备", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody CertificateConfigDevice certificateConfigDevice) {
        return toAjax(certificateConfigDeviceService.updateCertificateConfigDevice(certificateConfigDevice));
    }

    /**
     * 删除电子合格证配置-设备
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:remove')")
    @Log(title = "删除电子合格证配置-设备", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(certificateConfigDeviceService.deleteCertificateConfigDeviceById(id));
    }

    /**
     * 批量删除电子合格证配置-设备
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigDevice:batchRemove')")
    @Log(title = "批量删除电子合格证配置-设备", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody CertificateConfigDevice certificateConfigDevice) {
        return toAjax(certificateConfigDeviceService.deleteCertificateConfigDeviceByIds(certificateConfigDevice.getIds()));
    }
}
