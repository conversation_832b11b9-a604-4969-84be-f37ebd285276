package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.DeviceParameter;
import com.jkr.project.argi.service.IDeviceParameterService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 蓝牙设备参数Controller
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/argi/deviceParameter")
public class DeviceParameterController extends BaseController {
    @Autowired
    private IDeviceParameterService deviceParameterService;

    /**
     * 查询蓝牙设备参数列表
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceParameter deviceParameter) {
        startPage();
        List<DeviceParameter> list = deviceParameterService.selectDeviceParameterList(deviceParameter);
        return getDataTable(list);
    }

    /**
     * 导出蓝牙设备参数列表
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:export')")
    @Log(title = "导出蓝牙设备参数列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceParameter deviceParameter) {
        List<DeviceParameter> list = deviceParameterService.selectDeviceParameterList(deviceParameter);
        ExcelUtil<DeviceParameter> util = new ExcelUtil<DeviceParameter>(DeviceParameter.class);
        util.exportExcel(response, list, "蓝牙设备参数数据");
    }

    /**
     * 获取蓝牙设备参数详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(deviceParameterService.selectDeviceParameterById(id));
    }

    /**
     * 新增蓝牙设备参数
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:add')")
    @Log(title = "新增蓝牙设备参数", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody DeviceParameter deviceParameter) {
        return toAjax(deviceParameterService.insertDeviceParameter(deviceParameter));
    }

    /**
     * 修改蓝牙设备参数
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:edit')")
    @Log(title = "修改蓝牙设备参数", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody DeviceParameter deviceParameter) {
        return toAjax(deviceParameterService.updateDeviceParameter(deviceParameter));
    }

    /**
     * 删除蓝牙设备参数
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:remove')")
    @Log(title = "删除蓝牙设备参数", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable String id) {
        return toAjax(deviceParameterService.deleteDeviceParameterById(id));
    }

    /**
     * 批量删除蓝牙设备参数
     */
    @PreAuthorize("@ss.hasPermi('argi:parameter:batchRemove')")
    @Log(title = "批量删除蓝牙设备参数", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody DeviceParameter deviceParameter) {
        return toAjax(deviceParameterService.deleteDeviceParameterByIds(deviceParameter.getIds()));
    }
}
