package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.Autograph;

/**
 * 签名Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
public interface AutographMapper extends BaseMapper<Autograph>{
	/**
	 * 查询签名
	 *
	 * @param id 签名主键
	 * @return 签名
	 */
	public Autograph selectAutographById(Long id);

	/**
	 * 查询签名列表
	 *
	 * @param autograph 签名
	 * @return 签名集合
	 */
	public List<Autograph> selectAutographList(Autograph autograph);

	/**
	 * 新增签名
	 *
	 * @param autograph 签名
	 * @return 结果
	 */
	public int insertAutograph(Autograph autograph);

	/**
	 * 修改签名
	 *
	 * @param autograph 签名
	 * @return 结果
	 */
	public int updateAutograph(Autograph autograph);

	/**
	 * 删除签名
	 *
	 * @param id 签名主键
	 * @return 结果
	 */
	public int deleteAutographById(Long id);

	/**
	 * 批量删除签名
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteAutographByIds(Long[] ids);

	/**
	 * 批量逻辑删除签名
	 *
	 * @param  ids 签名主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过签名主键id逻辑删除信息
	 *
	 * @param  id 签名主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
	/**
	 * 根据企业ID获取签名
	 * <AUTHOR>
	 * @date 2025/5/22 13:25
	 * @param entId
	 * @since 1.0.0
	 * @return com.jkr.project.argi.domain.Autograph
	 */
	public Autograph getByEntId(String entId);

	/**
	 *
	 * @Title: deleteByEntId
	 * @author: lvpeng
	 * @date: 2020-7-22
	 * @Description: 根据主体id删除
	 * @param:  entId
	 */
	void deleteByEntId(String endId);
}
