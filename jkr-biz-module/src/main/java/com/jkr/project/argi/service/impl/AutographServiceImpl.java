package com.jkr.project.argi.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jkr.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.AutographMapper;
import com.jkr.project.argi.domain.Autograph;
import com.jkr.project.argi.service.IAutographService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 签名Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class AutographServiceImpl  extends ServiceImpl<AutographMapper, Autograph>  implements IAutographService {
	@Autowired
	private AutographMapper autographMapper;

	/**
	 * 查询签名
	 *
	 * @param id 签名主键
	 * @return 签名
	 */
	@Override
	public Autograph selectAutographById(Long id) {
		return autographMapper.selectAutographById(id);
	}

	/**
	 * 查询签名列表
	 *
	 * @param autograph 签名
	 * @return 签名
	 */
	@Override
	public List<Autograph> selectAutographList(Autograph autograph) {
		return autographMapper.selectAutographList(autograph);
	}

	/**
	 * 新增签名
	 *
	 * @param autograph 签名
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertAutograph(Autograph autograph) {
		autograph.insertInit(SecurityUtils.getLoginUser().getUsername());

			return autographMapper.insertAutograph(autograph);
	}

	/**
	 * 修改签名
	 *
	 * @param autograph 签名
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateAutograph(Autograph autograph) {
		autograph.updateInit(SecurityUtils.getLoginUser().getUsername());

		return autographMapper.updateAutograph(autograph);
	}

	/**
	 * 批量删除签名
	 *
	 * @param ids 需要删除的签名主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteAutographByIds(List<Long> ids) {
		return autographMapper.logicRemoveByIds(ids);
		//return autographMapper.deleteAutographByIds(ids);
	}

	/**
	 * 删除签名信息
	 *
	 * @param id 签名主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteAutographById(Long id) {
		return autographMapper.logicRemoveById(id);
		//return autographMapper.deleteAutographById(id);
	}

	/**
	 *
	 * @Title: getByEntId
	 * @author: LJX
	 * @date: 2020年7月21日 上午11:00:40
	 * @Description: 根据企业ID获取签名
	 * @param:  entId
	 * @return: Autograph
	 * @throws
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public Autograph getByEntId(String entId) {
		return autographMapper.getByEntId(entId);
	}

	/**
	 *
	 * @Title: deleteByEntId
	 * @Description: 根据主体id删除
	 * @param:  entId
	 * @return:
	 * @throws
	 */
	@Override
	@Transactional(readOnly = false,rollbackFor=Exception.class)
	public void deleteByEntId(String endId){
		autographMapper.deleteByEntId(endId);
	}

}
