package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.CertificateConfigTemplate;
import com.jkr.project.argi.service.ICertificateConfigTemplateService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 电子合格证配置-模板Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/certificateConfigTemplate")
public class CertificateConfigTemplateController extends BaseController {
    @Autowired
    private ICertificateConfigTemplateService certificateConfigTemplateService;

    /**
     * 查询电子合格证配置-模板列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertificateConfigTemplate certificateConfigTemplate) {
        startPage();
        List<CertificateConfigTemplate> list = certificateConfigTemplateService.selectCertificateConfigTemplateList(certificateConfigTemplate);
        return getDataTable(list);
    }

    /**
     * 导出电子合格证配置-模板列表
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:export')")
    @Log(title = "导出电子合格证配置-模板列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertificateConfigTemplate certificateConfigTemplate) {
        List<CertificateConfigTemplate> list = certificateConfigTemplateService.selectCertificateConfigTemplateList(certificateConfigTemplate);
        ExcelUtil<CertificateConfigTemplate> util = new ExcelUtil<CertificateConfigTemplate>(CertificateConfigTemplate.class);
        util.exportExcel(response, list, "电子合格证配置-模板数据");
    }

    /**
     * 获取电子合格证配置-模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(certificateConfigTemplateService.selectCertificateConfigTemplateById(id));
    }

    /**
     * 新增电子合格证配置-模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:add')")
    @Log(title = "新增电子合格证配置-模板", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody CertificateConfigTemplate certificateConfigTemplate) {
        return toAjax(certificateConfigTemplateService.insertCertificateConfigTemplate(certificateConfigTemplate));
    }

    /**
     * 修改电子合格证配置-模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:edit')")
    @Log(title = "修改电子合格证配置-模板", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody CertificateConfigTemplate certificateConfigTemplate) {
        return toAjax(certificateConfigTemplateService.updateCertificateConfigTemplate(certificateConfigTemplate));
    }

    /**
     * 删除电子合格证配置-模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:remove')")
    @Log(title = "删除电子合格证配置-模板", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(certificateConfigTemplateService.deleteCertificateConfigTemplateById(id));
    }

    /**
     * 批量删除电子合格证配置-模板
     */
    @PreAuthorize("@ss.hasPermi('argi:certificateConfigTemplate:batchRemove')")
    @Log(title = "批量删除电子合格证配置-模板", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody CertificateConfigTemplate certificateConfigTemplate) {
        return toAjax(certificateConfigTemplateService.deleteCertificateConfigTemplateByIds(certificateConfigTemplate.getIds()));
    }
}
