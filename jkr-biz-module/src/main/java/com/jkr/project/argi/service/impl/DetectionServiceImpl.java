package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.DetectionMapper;
import com.jkr.project.argi.domain.Detection;
import com.jkr.project.argi.service.IDetectionService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 与快检系统对接Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class DetectionServiceImpl implements IDetectionService {
	@Autowired
	private DetectionMapper detectionMapper;

	/**
	 * 查询与快检系统对接
	 *
	 * @param id 与快检系统对接主键
	 * @return 与快检系统对接
	 */
	@Override
	public Detection selectDetectionById(Long id) {
		return detectionMapper.selectDetectionById(id);
	}

	/**
	 * 查询与快检系统对接列表
	 *
	 * @param detection 与快检系统对接
	 * @return 与快检系统对接
	 */
	@Override
	public List<Detection> selectDetectionList(Detection detection) {
		return detectionMapper.selectDetectionList(detection);
	}

	/**
	 * 新增与快检系统对接
	 *
	 * @param detection 与快检系统对接
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertDetection(Detection detection) {
		detection.insertInit(SecurityUtils.getLoginUser().getUsername());
        
			return detectionMapper.insertDetection(detection);
	}

	/**
	 * 修改与快检系统对接
	 *
	 * @param detection 与快检系统对接
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateDetection(Detection detection) {
		detection.updateInit(SecurityUtils.getLoginUser().getUsername());
        
		return detectionMapper.updateDetection(detection);
	}

	/**
	 * 批量删除与快检系统对接
	 *
	 * @param ids 需要删除的与快检系统对接主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteDetectionByIds(List<Long> ids) {
		return detectionMapper.logicRemoveByIds(ids);
		//return detectionMapper.deleteDetectionByIds(ids);
	}

	/**
	 * 删除与快检系统对接信息
	 *
	 * @param id 与快检系统对接主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteDetectionById(Long id) {
		return detectionMapper.logicRemoveById(id);
		//return detectionMapper.deleteDetectionById(id);
	}

}
