package com.jkr.project.argi.controller;

import java.util.List;

import com.jkr.common.utils.PageUtils;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.ProductItem;
import com.jkr.project.argi.service.IProductItemService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 产品子表(组合品使用)Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/productItem")
public class ProductItemController extends BaseController {

    @Autowired
    private IProductItemService productItemService;

    /**
     * 查询产品子表(组合品使用)列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductItem productItem) {
        startPage();
        List<ProductItem> list = productItemService.selectProductItemList(productItem);
        return getDataTable(list);
    }

    /**
     * 导出产品子表(组合品使用)列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:export')")
    @Log(title = "导出产品子表(组合品使用)列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductItem productItem) {
        List<ProductItem> list = productItemService.selectProductItemList(productItem);
        ExcelUtil<ProductItem> util = new ExcelUtil<ProductItem>(ProductItem.class);
        util.exportExcel(response, list, "产品子表(组合品使用)数据");
    }

    /**
     * 获取产品子表(组合品使用)详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productItemService.selectProductItemById(id));
    }

    /**
     * 新增产品子表(组合品使用)
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:add')")
    @Log(title = "新增产品子表(组合品使用)", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody ProductItem productItem) {
        return toAjax(productItemService.insertProductItem(productItem));
    }

    /**
     * 修改产品子表(组合品使用)
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:edit')")
    @Log(title = "修改产品子表(组合品使用)", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody ProductItem productItem) {
        return toAjax(productItemService.updateProductItem(productItem));
    }

    /**
     * 删除产品子表(组合品使用)
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:remove')")
    @Log(title = "删除产品子表(组合品使用)", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(productItemService.deleteProductItemById(id));
    }

    /**
     * 批量删除产品子表(组合品使用)
     */
    @PreAuthorize("@ss.hasPermi('argi:productItem:batchRemove')")
    @Log(title = "批量删除产品子表(组合品使用)", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody ProductItem productItem) {
        return toAjax(productItemService.deleteProductItemByIds(productItem.getIds()));
    }

    @PostMapping("/findProductItemPage")
    public TableDataInfo findProductItemPage(@RequestBody ProductItem productItem) {
        PageUtils.startPage(productItem.getPageNum(), productItem.getPageSize());
    	List<ProductItem> list = productItemService.selectProductItemList(productItem);
    	return getDataTable(list);
    }
}
