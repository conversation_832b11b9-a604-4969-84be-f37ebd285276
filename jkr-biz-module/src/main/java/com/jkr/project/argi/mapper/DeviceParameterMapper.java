package com.jkr.project.argi.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.argi.domain.DeviceParameter;

/**
 * 蓝牙设备参数Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@Mapper
public interface DeviceParameterMapper extends BaseMapper<DeviceParameter>{
	/**
	 * 查询蓝牙设备参数
	 *
	 * @param id 蓝牙设备参数主键
	 * @return 蓝牙设备参数
	 */
	public DeviceParameter selectDeviceParameterById(String id);

	/**
	 * 查询蓝牙设备参数列表
	 *
	 * @param deviceParameter 蓝牙设备参数
	 * @return 蓝牙设备参数集合
	 */
	public List<DeviceParameter> selectDeviceParameterList(DeviceParameter deviceParameter);

	/**
	 * 新增蓝牙设备参数
	 *
	 * @param deviceParameter 蓝牙设备参数
	 * @return 结果
	 */
	public int insertDeviceParameter(DeviceParameter deviceParameter);

	/**
	 * 修改蓝牙设备参数
	 *
	 * @param deviceParameter 蓝牙设备参数
	 * @return 结果
	 */
	public int updateDeviceParameter(DeviceParameter deviceParameter);

	/**
	 * 删除蓝牙设备参数
	 *
	 * @param id 蓝牙设备参数主键
	 * @return 结果
	 */
	public int deleteDeviceParameterById(String id);

	/**
	 * 批量删除蓝牙设备参数
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteDeviceParameterByIds(String[] ids);

	/**
	 * 批量逻辑删除蓝牙设备参数
	 *
	 * @param  ids 蓝牙设备参数主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过蓝牙设备参数主键id逻辑删除信息
	 *
	 * @param  id 蓝牙设备参数主键
	 * @return 结果
	 */
	public int logicRemoveById(String id);
}
