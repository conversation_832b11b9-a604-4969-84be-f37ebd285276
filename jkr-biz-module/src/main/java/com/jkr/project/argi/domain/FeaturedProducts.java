package com.jkr.project.argi.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import java.util.List;

/**
 * 特色产品对象 bas_featured_products
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
        @Data
        @EqualsAndHashCode(callSuper = true)
        @TableName("bas_featured_products")
		public class FeaturedProducts extends BaseModel
		{
		private static final long serialVersionUID = 1L;

				/** 监管code */
				@Excel(name = "监管code")
		private String officeCode;

				/** 行政区划code */
				@Excel(name = "行政区划code")
		private String code;

				/** 行政区划Id */
				@Excel(name = "行政区划Id")
		private String areaId;

				/** 名称 */
				@Excel(name = "名称")
		private String name;

				/** 地址(省市县) */
				@Excel(name = "地址(省市县)")
		private String address;

				/** 业务种类name */
				@Excel(name = "业务种类name")
		private String businessName;

				/** 业务种类code */
				@Excel(name = "业务种类code")
		private String businessCode;

				/** 生产日期 */
				@JsonFormat(pattern = "yyyy-MM-dd")
				@Excel(name = "生产日期", width = 30, dateFormat = "yyyy-MM-dd")
		private Date productDate;

				/** 规模 */
				@Excel(name = "规模")
		private String scale;

				/** 规模单位 */
				@Excel(name = "规模单位")
		private String scaleUnit;

				/** 产量 */
				@Excel(name = "产量")
		private Long yield;

				/** 产量单位 */
				@Excel(name = "产量单位")
		private String yieldUnit;

				/** 产值 */
				@Excel(name = "产值")
		private Long outputValue;

				/** 供应周期name */
				@Excel(name = "供应周期name")
		private String supplyName;

				/** 供应周期code */
				@Excel(name = "供应周期code")
		private String supplyCode;

				/** 地图显示标志（1是 0否） */
				@Excel(name = "地图显示标志", readConverterExp = "1=是,0=否")
		private Long mapDisplay;

				/** 排序 */
				@Excel(name = "排序")
		private Long showSort;

				/** 介绍 */
				@Excel(name = "介绍")
		private String introduce;

				/** 产地介绍 */
				@Excel(name = "产地介绍")
		private String productIntroduce;

				/** 是否默认（1是 0否） */
				@Excel(name = "是否默认", readConverterExp = "1=是,0=否")
		private Long isDefault;

            /** 主键集合 */
            private List<Long> ids;
}
