package com.jkr.project.argi.service;

import java.util.List;

import com.jkr.project.argi.domain .FeaturedProducts;

/**
 * 特色产品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
public interface IFeaturedProductsService {
	/**
	 * 查询特色产品
	 *
	 * @param id 特色产品主键
	 * @return 特色产品
	 */
	public FeaturedProducts selectFeaturedProductsById(Long id);

	/**
	 * 查询特色产品列表
	 *
	 * @param featuredProducts 特色产品
	 * @return 特色产品集合
	 */
	public List<FeaturedProducts> selectFeaturedProductsList(FeaturedProducts featuredProducts);

	/**
	 * 新增特色产品
	 *
	 * @param featuredProducts 特色产品
	 * @return 结果
	 */
	public int insertFeaturedProducts(FeaturedProducts featuredProducts);

	/**
	 * 修改特色产品
	 *
	 * @param featuredProducts 特色产品
	 * @return 结果
	 */
	public int updateFeaturedProducts(FeaturedProducts featuredProducts);

	/**
	 * 批量删除特色产品
	 *
	 * @param ids 需要删除的特色产品主键集合
	 * @return 结果
	 */
	public int deleteFeaturedProductsByIds(List<Long> ids);

	/**
	 * 删除特色产品信息
	 *
	 * @param id 特色产品主键
	 * @return 结果
	 */
	public int deleteFeaturedProductsById(Long id);

}
