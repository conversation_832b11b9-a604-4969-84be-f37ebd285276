package com.jkr.project.argi.service.impl;

import java.util.List;
		import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.argi.mapper.ProductItemMapper;
import com.jkr.project.argi.domain.ProductItem;
import com.jkr.project.argi.service.IProductItemService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品子表(组合品使用)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Transactional
public class ProductItemServiceImpl implements IProductItemService {

	@Autowired
	private ProductItemMapper productItemMapper;

	/**
	 * 查询产品子表(组合品使用)
	 *
	 * @param id 产品子表(组合品使用)主键
	 * @return 产品子表(组合品使用)
	 */
	@Override
	public ProductItem selectProductItemById(Long id) {
		return productItemMapper.selectProductItemById(id);
	}

	/**
	 * 查询产品子表(组合品使用)列表
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 产品子表(组合品使用)
	 */
	@Override
	public List<ProductItem> selectProductItemList(ProductItem productItem) {
		return productItemMapper.selectProductItemList(productItem);
	}

	/**
	 * 新增产品子表(组合品使用)
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertProductItem(ProductItem productItem) {
		productItem.insertInit(SecurityUtils.getLoginUser().getUsername());

			return productItemMapper.insertProductItem(productItem);
	}

	/**
	 * 修改产品子表(组合品使用)
	 *
	 * @param productItem 产品子表(组合品使用)
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateProductItem(ProductItem productItem) {
		productItem.updateInit(SecurityUtils.getLoginUser().getUsername());

		return productItemMapper.updateProductItem(productItem);
	}

	/**
	 * 批量删除产品子表(组合品使用)
	 *
	 * @param ids 需要删除的产品子表(组合品使用)主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductItemByIds(List<Long> ids) {
		return productItemMapper.logicRemoveByIds(ids);
		//return productItemMapper.deleteProductItemByIds(ids);
	}

	/**
	 * 删除产品子表(组合品使用)信息
	 *
	 * @param id 产品子表(组合品使用)主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteProductItemById(Long id) {
		return productItemMapper.logicRemoveById(id);
		//return productItemMapper.deleteProductItemById(id);
	}

	@Override
	public List<ProductItem> findListByProductId(String productId) {
		if(StringUtils.isBlank(productId)) {
			return null;
		}
		ProductItem productItem=new ProductItem();
		productItem.setProductId(productId);
		return selectProductItemList(productItem);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void batchInsert(List<ProductItem> productItemList) {
		productItemMapper.batchInsert(productItemList);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteByProductId(String id) {
		productItemMapper.deleteByProductId(id);
	}

	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public void deleteBatchByProductId(List<String> list) {
		productItemMapper.deleteBatchByProductId(list);
	}

}
