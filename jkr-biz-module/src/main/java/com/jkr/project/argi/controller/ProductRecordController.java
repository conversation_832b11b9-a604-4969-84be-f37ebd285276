package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.ProductRecord;
import com.jkr.project.argi.service.IProductRecordService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 产品检测记录Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/productRecord")
public class ProductRecordController extends BaseController {

    @Autowired
    private IProductRecordService productRecordService;

    /**
     * 查询产品检测记录列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductRecord productRecord) {
        startPage();
        List<ProductRecord> list = productRecordService.selectProductRecordList(productRecord);
        return getDataTable(list);
    }

    /**
     * 导出产品检测记录列表
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:export')")
    @Log(title = "导出产品检测记录列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductRecord productRecord) {
        List<ProductRecord> list = productRecordService.selectProductRecordList(productRecord);
        ExcelUtil<ProductRecord> util = new ExcelUtil<ProductRecord>(ProductRecord.class);
        util.exportExcel(response, list, "产品检测记录数据");
    }

    /**
     * 获取产品检测记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:query')")
    @GetMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productRecordService.selectProductRecordById(id));
    }

    /**
     * 新增产品检测记录
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:add')")
    @Log(title = "新增产品检测记录", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody ProductRecord productRecord) {
        return toAjax(productRecordService.insertProductRecord(productRecord));
    }

    /**
     * 修改产品检测记录
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:edit')")
    @Log(title = "修改产品检测记录", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody ProductRecord productRecord) {
        return toAjax(productRecordService.updateProductRecord(productRecord));
    }

    /**
     * 删除产品检测记录
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:remove')")
    @Log(title = "删除产品检测记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(productRecordService.deleteProductRecordById(id));
    }

    /**
     * 批量删除产品检测记录
     */
    @PreAuthorize("@ss.hasPermi('argi:productRecord:batchRemove')")
    @Log(title = "批量删除产品检测记录", businessType = BusinessType.DELETE)
    @PostMapping("/batchRemove")
    public AjaxResult batchRemove(@RequestBody ProductRecord productRecord) {
        return toAjax(productRecordService.deleteProductRecordByIds(productRecord.getIds()));
    }
}
