package com.jkr.project.argi.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.argi.domain.EntDetail;
import com.jkr.project.argi.service.IEntDetailService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 主体信息子表Controller
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@RestController
@RequestMapping("/argi/entDetail")
public class EntDetailController extends BaseController {
	@Autowired
	private IEntDetailService entDetailService;

/**
 * 查询主体信息子表列表
 */
@PreAuthorize("@ss.hasPermi('argi:entDetail:list')")
@GetMapping("/list")
	public TableDataInfo list(EntDetail entDetail) {
		startPage();
		List<EntDetail> list = entDetailService.selectEntDetailList(entDetail);
		return getDataTable(list);
	}

	/**
	 * 导出主体信息子表列表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entDetail:export')")
	@Log(title = "导出主体信息子表列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, EntDetail entDetail) {
		List<EntDetail> list = entDetailService.selectEntDetailList(entDetail);
		ExcelUtil<EntDetail> util = new ExcelUtil<EntDetail>(EntDetail. class);
		util.exportExcel(response, list, "主体信息子表数据");
	}

	/**
	 * 获取主体信息子表详细信息
	 */
	@PreAuthorize("@ss.hasPermi('argi:entDetail:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(entDetailService.selectEntDetailById(id));
	}

	/**
	 * 新增主体信息子表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entDetail:add')")
	@Log(title = "新增主体信息子表", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody EntDetail entDetail) {
		return toAjax(entDetailService.insertEntDetail(entDetail));
	}

	/**
	 * 修改主体信息子表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entDetail:edit')")
	@Log(title = "修改主体信息子表", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody EntDetail entDetail) {
		return toAjax(entDetailService.updateEntDetail(entDetail));
	}

	/**
	 * 删除主体信息子表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entDetail:remove')")
	@Log(title = "删除主体信息子表", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(entDetailService.deleteEntDetailById(id));
	}

	/**
	 * 批量删除主体信息子表
	 */
	@PreAuthorize("@ss.hasPermi('argi:entDetail:batchRemove')")
	@Log(title = "批量删除主体信息子表", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody EntDetail entDetail) {
		return toAjax(entDetailService.deleteEntDetailByIds(entDetail.getIds()));
	}
}
