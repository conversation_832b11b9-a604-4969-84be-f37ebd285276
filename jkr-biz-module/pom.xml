<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jkr</groupId>
        <artifactId>argi_promise_cert_server</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>jkr-biz-module</artifactId>
    <version>1.0.0</version>
    <description>
        业务模块，主要在此处编写代码，处理业务逻辑；此模块为示例，可删除自行创建
    </description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.jkr</groupId>
            <artifactId>jkr-system-module</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
</project>
