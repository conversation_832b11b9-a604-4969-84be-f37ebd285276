package com.jkr.common.utils;

import org.junit.jupiter.api.Test;

class PasswordCheckerTest {

	@Test
	void isPasswordValid() {
		String password1 = "Abc@132579";
		String password2 = "Abb@1b3579";
		String password3 = "Abc@12345";
		String password4 = "Aaa1!Aa12";
		String password5 = "Aa1!Aa1Aa1!Aa1Aa1!Aa1";
		String password6 = "Aa1!Aa1Aa1!Aa1Aa1!Aa1!";

		System.out.println("Password 1: " + PasswordChecker.isPasswordValid(password1));
		System.out.println("Password 2: " + PasswordChecker.isPasswordValid(password2));
		System.out.println("Password 3: " + PasswordChecker.isPasswordValid(password3));
		System.out.println("Password 4: " + PasswordChecker.isPasswordValid(password4));
		System.out.println("Password 5: " + PasswordChecker.isPasswordValid(password5));
		System.out.println("Password 6: " + PasswordChecker.isPasswordValid(password6));
	}
}