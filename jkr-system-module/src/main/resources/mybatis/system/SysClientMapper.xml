<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysClientMapper">

    <resultMap type="com.jkr.project.system.domain.SysClient" id="SysClientResult">
        <result property="id"    column="id"    />
        <result property="clientAppId"    column="client_app_id"    />
        <result property="clientSecret"    column="client_secret"    />
        <result property="type"    column="type"    />
        <result property="enName"    column="en_name"    />
        <result property="cnName"    column="cn_name"    />
        <result property="shortCnName"    column="short_cn_name"    />
        <result property="redirectPath"    column="redirect_path"    />
        <result property="serverPath"    column="server_path"    />
        <result property="loginAuth"    column="login_auth"    />
        <result property="allowImgUrl"    column="allow_img_url"    />
        <result property="unAllowImgUrl"    column="un_allow_img_url"    />
        <result property="sort"    column="sort"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysClientVo">
        select id, client_app_id, client_secret, type, en_name, cn_name, short_cn_name,
               redirect_path, server_path, login_auth,  allow_img_url, un_allow_img_url, sort,
               del_flag, create_by, create_time, update_by, update_time, remark from sys_client
    </sql>

    <select id="selectSysClientList" parameterType="com.jkr.project.system.domain.SysClient" resultMap="SysClientResult">
        <include refid="selectSysClientVo"/>
        <where>
            del_flag = '1'
            <if test="clientAppId != null  and clientAppId != ''"> and client_app_id = #{clientAppId}</if>
            <if test="clientSecret != null  and clientSecret != ''"> and client_secret = #{clientSecret}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="enName != null  and enName != ''"> and en_name like concat('%', #{enName}, '%')</if>
            <if test="cnName != null  and cnName != ''"> and cn_name like concat('%', #{cnName}, '%')</if>
            <if test="shortCnName != null  and shortCnName != ''"> and short_cn_name like concat('%', #{shortCnName}, '%')</if>
            <if test="redirectPath != null  and redirectPath != ''"> and redirect_path = #{redirectPath}</if>
            <if test="serverPath != null  and serverPath != ''"> and server_path = #{serverPath}</if>
            <if test="loginAuth != null "> and login_auth = #{loginAuth}</if>
            <if test="allowImgUrl != null  and allowImgUrl != ''"> and allow_img_url = #{allowImgUrl}</if>
            <if test="unAllowImgUrl != null  and unAllowImgUrl != ''"> and un_allow_img_url = #{unAllowImgUrl}</if>
            <if test="sort != null "> and sort = #{sort}</if>
        </where>
    </select>

    <select id="selectSysClientById" parameterType="Long" resultMap="SysClientResult">
            <include refid="selectSysClientVo"/>
            where id = #{id}
    </select>

    <insert id="insertSysClient" parameterType="com.jkr.project.system.domain.SysClient" useGeneratedKeys="true"
            keyProperty="id">
        insert into sys_client
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="clientAppId != null">client_app_id,</if>
            <if test="clientSecret != null">client_secret,</if>
            <if test="type != null">type,</if>
            <if test="enName != null">en_name,</if>
            <if test="cnName != null">cn_name,</if>
            <if test="shortCnName != null">short_cn_name,</if>
            <if test="redirectPath != null">redirect_path,</if>
            <if test="serverPath != null">server_path,</if>
            <if test="loginAuth != null">login_auth,</if>
            <if test="allowImgUrl != null">allow_img_url,</if>
            <if test="unAllowImgUrl != null">un_allow_img_url,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="clientAppId != null">#{clientAppId},</if>
            <if test="clientSecret != null">#{clientSecret},</if>
            <if test="type != null">#{type},</if>
            <if test="enName != null">#{enName},</if>
            <if test="cnName != null">#{cnName},</if>
            <if test="shortCnName != null">#{shortCnName},</if>
            <if test="redirectPath != null">#{redirectPath},</if>
            <if test="serverPath != null">#{serverPath},</if>
            <if test="loginAuth != null">#{loginAuth},</if>
            <if test="allowImgUrl != null">#{allowImgUrl},</if>
            <if test="unAllowImgUrl != null">#{unAllowImgUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysClient" parameterType="com.jkr.project.system.domain.SysClient">
        update sys_client
        <trim prefix="SET" suffixOverrides=",">
            <if test="clientAppId != null">client_app_id = #{clientAppId},</if>
            <if test="clientSecret != null">client_secret = #{clientSecret},</if>
            <if test="type != null">type = #{type},</if>
            <if test="enName != null">en_name = #{enName},</if>
            <if test="cnName != null">cn_name = #{cnName},</if>
            <if test="shortCnName != null">short_cn_name = #{shortCnName},</if>
            <if test="redirectPath != null">redirect_path = #{redirectPath},</if>
            <if test="serverPath != null">server_path = #{serverPath},</if>
            <if test="loginAuth != null">login_auth = #{loginAuth},</if>
            <if test="allowImgUrl != null">allow_img_url = #{allowImgUrl},</if>
            <if test="unAllowImgUrl != null">un_allow_img_url = #{unAllowImgUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysClientById" parameterType="Long">
        UPDATE sys_client SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') WHERE id = #{id}
    </delete>

    <delete id="deleteSysClientByIds" parameterType="String">
        UPDATE sys_client SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>