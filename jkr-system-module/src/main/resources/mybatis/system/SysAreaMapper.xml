<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysAreaMapper">

    <resultMap type="com.jkr.project.system.domain.SysArea" id="SysAreaResult">
        <result property="areaId"    column="area_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentIds"    column="parent_ids"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="lng"    column="lng"    />
        <result property="lat"    column="lat"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysAreaVo">
        select area_id, parent_id, parent_ids, name, code, type, lng, lat, del_flag, create_by, create_time, update_by, update_time, remark from sys_area
    </sql>

    <select id="selectSysAreaList" parameterType="com.jkr.project.system.domain.SysArea" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        <where>
            del_flag = '1'
            <if test="parentId != null  and parentId != ''"> and parent_id = #{parentId}</if>
            <if test="parentIds != null  and parentIds != ''"> and parent_ids = #{parentIds}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="lng != null  and lng != ''"> and lng = #{lng}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="codePrefix != null  and codePrefix != ''"> and code like concat('', #{codePrefix}, '%')</if>
            <if test="null != typeList and typeList.size() > 0">
                and type in
                <foreach item="item" collection="typeList" separator="," open="(" close=")" index="">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY code ASC
    </select>

    <select id="selectSysAreaByAreaId" parameterType="String" resultMap="SysAreaResult">
        <include refid="selectSysAreaVo"/>
        where area_id = #{areaId}
    </select>

    <insert id="insertSysArea" parameterType="com.jkr.project.system.domain.SysArea" useGeneratedKeys="true" keyProperty="areaId">
        insert into sys_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="areaId != null">area_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="parentIds != null">parent_ids,</if>
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="type != null">type,</if>
            <if test="lng != null">lng,</if>
            <if test="lat != null">lat,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="areaId != null">#{areaId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="parentIds != null">#{parentIds},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="type != null">#{type},</if>
            <if test="lng != null">#{lng},</if>
            <if test="lat != null">#{lat},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysArea" parameterType="com.jkr.project.system.domain.SysArea">
        update sys_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="parentIds != null">parent_ids = #{parentIds},</if>
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="type != null">type = #{type},</if>
            <if test="lng != null">lng = #{lng},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where area_id = #{areaId}
    </update>

    <delete id="deleteSysAreaByAreaId" parameterType="String">
        update sys_area set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where area_id = #{areaId} or parent_ids like concat('%', #{areaId}, '%')
    </delete>

    <delete id="deleteSysAreaByAreaIds" parameterType="String">
        update sys_area set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where area_id in
        <foreach item="areaId" collection="array" open="(" separator="," close=")">
            #{areaId}
        </foreach>
    </delete>

    <select id="findTreeList" resultMap="SysAreaResult" parameterType="com.jkr.project.system.domain.SysArea">
        SELECT
            a.area_id,a.parent_id,a.name,a.code,a.type,a.remark,dic.dict_label AS typeValue,
            IF ( COUNT( ch.area_id ) > 0, 'true', 'false' ) AS 'hasChild'
        FROM sys_area a
        LEFT JOIN sys_area ch ON a.area_id = ch.parent_id AND ch.del_flag = '1'
        LEFT JOIN sys_dict_data dic ON a.type=dic.dict_value AND dic.dict_type='sys_area_type'
        WHERE
            a.del_flag = '1'
          AND a.parent_id = #{parentId}
        GROUP BY
            a.area_id
        ORDER BY
            a.code
    </select>
</mapper>
