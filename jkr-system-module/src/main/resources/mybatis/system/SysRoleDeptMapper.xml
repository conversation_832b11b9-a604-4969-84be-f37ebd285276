<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysRoleDeptMapper">

    <resultMap type="com.jkr.project.system.domain.SysRoleDept" id="SysRoleDeptResult">
        <result property="roleId" column="role_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <delete id="deleteRoleDeptByRoleId" parameterType="Long">
		delete from sys_role_dept where role_id=#{roleId}
	</delete>

    <select id="selectCountRoleDeptByDeptId" resultType="Integer">
	    select count(1) from sys_role_dept where dept_id=#{deptId}
	</select>

    <delete id="deleteRoleDept" parameterType="Long">
        delete from sys_role_dept where role_id in
        <foreach collection="array" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <insert id="batchRoleDept">
        insert into sys_role_dept(role_id, dept_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.deptId})
        </foreach>
    </insert>

</mapper> 