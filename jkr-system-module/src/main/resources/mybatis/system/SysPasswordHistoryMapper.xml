<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysPasswordHistoryMapper">

    <resultMap type="com.jkr.project.system.domain.SysPasswordHistory" id="SysPasswordHistoryResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="password" column="password"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectSysPasswordHistoryVo">
        select id, user_id, password from sys_password_history
    </sql>

    <select id="selectSysPasswordHistoryList" parameterType="com.jkr.project.system.domain.SysPasswordHistory"
            resultMap="SysPasswordHistoryResult">
        <include refid="selectSysPasswordHistoryVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
        </where>
    </select>

    <insert id="insertSysPasswordHistory" parameterType="com.jkr.project.system.domain.SysPasswordHistory">
        insert into sys_password_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="password != null and password != ''">password,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="password != null and password != ''">#{password},</if>
        </trim>
    </insert>

</mapper>