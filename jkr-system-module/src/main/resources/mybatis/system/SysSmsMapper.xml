<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysSmsMapper">

    <resultMap type="com.jkr.project.system.domain.SysSms" id="SysSmsResult">
        <result property="smsId"    column="sms_id"    />
        <result property="phone"    column="phone"    />
        <result property="code"    column="code"    />
        <result property="expiryTime"    column="expiry_time"    />
        <result property="businessCode"    column="business_code"    />
        <result property="ip"    column="ip"    />
        <result property="sendFlag"    column="send_flag"    />
        <result property="codeStatus"    column="code_status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysSmsVo">
        select sms_id, phone, code, expiry_time, business_code, ip, send_flag, code_status, del_flag, create_by, create_time, update_by, update_time, remark from sys_sms
    </sql>

    <select id="selectSysSmsList" parameterType="com.jkr.project.system.domain.SysSms" resultMap="SysSmsResult">
        <include refid="selectSysSmsVo"/>
        <where>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="expiryTime != null "> and expiry_time = #{expiryTime}</if>
            <if test="businessCode != null  and businessCode != ''"> and business_code = #{businessCode}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="sendFlag != null  and sendFlag != ''"> and send_flag = #{sendFlag}</if>
            <if test="codeStatus != null  and codeStatus != ''"> and code_status = #{codeStatus}</if>
        </where>
    </select>

    <select id="selectSysSmsBySmsId" parameterType="String" resultMap="SysSmsResult">
            <include refid="selectSysSmsVo"/>
            where sms_id = #{smsId}
    </select>

    <insert id="insertSysSms" parameterType="com.jkr.project.system.domain.SysSms">
        insert into sys_sms
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="smsId != null">sms_id,</if>
            <if test="phone != null">phone,</if>
            <if test="code != null">code,</if>
            <if test="expiryTime != null">expiry_time,</if>
            <if test="businessCode != null">business_code,</if>
            <if test="ip != null">ip,</if>
            <if test="sendFlag != null">send_flag,</if>
            <if test="codeStatus != null">code_status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="smsId != null">#{smsId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="code != null">#{code},</if>
            <if test="expiryTime != null">#{expiryTime},</if>
            <if test="businessCode != null">#{businessCode},</if>
            <if test="ip != null">#{ip},</if>
            <if test="sendFlag != null">#{sendFlag},</if>
            <if test="codeStatus != null">#{codeStatus},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysSms" parameterType="com.jkr.project.system.domain.SysSms">
        update sys_sms
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null">phone = #{phone},</if>
            <if test="code != null">code = #{code},</if>
            <if test="expiryTime != null">expiry_time = #{expiryTime},</if>
            <if test="businessCode != null">business_code = #{businessCode},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="sendFlag != null">send_flag = #{sendFlag},</if>
            <if test="codeStatus != null">code_status = #{codeStatus},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sms_id = #{smsId}
    </update>

    <delete id="deleteSysSmsBySmsId" parameterType="String">
        UPDATE sys_sms SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') WHERE sms_id = #{smsId}
    </delete>


    <select id="searchSendInfoByIpAndPhone" resultType="map">
        SELECT
            COUNT(id) allCnt,
            COUNT(IF(a.phone = #{phone},1,NULL)) AS samePhoneCnt
        FROM sys_sms a
        WHERE a.state=1
          and a.phone =#{phone}
          AND DATE_FORMAT(a.create_time,'%Y-%m-%d') <![CDATA[ = ]]>DATE_FORMAT(NOW(),'%Y-%m-%d')
          AND a.send_flag=1
        ORDER BY create_time DESC
        limit 1
    </select>

    <select id="searchSmsCode" resultType="com.jkr.project.system.domain.SysSms">
        <include refid="selectSysSmsVo"/>
        <where>
            send_flag = 1
            <if test="smsId != null  and smsId != ''"> and sms_id = #{smsId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="expiryTime!=null and expiryTime!=''">
                and expiry_time <![CDATA[ > ]]> #{expiryTime}
            </if>
        </where>
        order by create_time desc
        LIMIT 1
    </select>
</mapper>