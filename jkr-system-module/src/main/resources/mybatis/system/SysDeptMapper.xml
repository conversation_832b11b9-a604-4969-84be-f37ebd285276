<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysDeptMapper">

    <resultMap type="com.jkr.project.system.domain.SysDept" id="SysDeptResult">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="areaCode" column="area_code"/>
        <result property="areaName" column="area_name"/>
        <result property="areaLevel" column="area_level"/>
        <result property="deptType" column="dept_type"/>
        <result property="leader" column="leader"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="supervisionFlag" column="supervision_flag"/>
        <result property="delFlag" column="del_flag"/>
        <result property="parentName" column="parent_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDeptVo">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.area_code,d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time,d.supervision_flag
        from sys_dept d
    </sql>

    <select id="selectDeptList" parameterType="com.jkr.project.system.domain.SysDept" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where d.del_flag = '1'
        <if test="deptId != null and deptId != 0">
            AND dept_id = #{deptId}
        </if>
        <if test="parentId != null and parentId != 0">
            AND parent_id = #{parentId}
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND instr(area_code,#{areaCode})
        </if>
        <if test="deptName != null and deptName != ''">
            AND dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="supervisionFlag != null and supervisionFlag != ''">
            AND supervision_flag = #{supervisionFlag}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by d.parent_id, d.order_num
    </select>
    <select id="selectEntDeptList" parameterType="com.jkr.project.system.domain.SysDept" resultMap="SysDeptResult">
        select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.area_code,d.leader, d.phone, d.email, d.status, d.del_flag, d.create_by, d.create_time,d.supervision_flag, d.area_name,d.area_level,d.dept_type
        from sys_dept d
        where d.del_flag = '1'
        <if test="deptId != null and deptId != 0">
            AND d.dept_id = #{deptId}
        </if>
        <if test="parentId != null and parentId != 0">
            AND d.parent_id = #{parentId}
        </if>
        <if test="areaLevel != null and areaLevel != ''">
            AND d.area_level = #{areaLevel}
        </if>
          <if test="deptType != null and deptType != ''">
            AND d.dept_type = #{deptType}
        </if>
        <if test="areaCode != null and areaCode != ''">
            AND instr(d.area_code,#{areaCode})
        </if>
        <if test="areaName != null and areaName != ''">
            AND instr(d.area_name,#{areaName})
        </if>
        <if test="deptName != null and deptName != ''">
            AND d.dept_name like concat('%', #{deptName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND d.status = #{status}
        </if>
        <if test="leader != null and leader != ''">
            AND d.leader like concat('%', #{leader}, '%')
        </if>
        <if test="phone != null and phone != ''">
            AND d.phone like concat('%', #{phone}, '%')
        </if>
        <if test="supervisionFlag != null and supervisionFlag != ''">
            AND d.supervision_flag = #{supervisionFlag}
        </if>
        <if test="beginCreateDate != null and beginCreateDate != '' ">
            AND
            <![CDATA[ DATE_FORMAT(d.create_time,'%Y-%m-%d') >=DATE_FORMAT(#{beginCreateDate},'%Y-%m-%d') ]]>
        </if>
        <if test="endCreateDate != null and endCreateDate != '' ">
            AND
            <![CDATA[ DATE_FORMAT(d.create_time,'%Y-%m-%d') <=DATE_FORMAT(#{endCreateDate},'%Y-%m-%d') ]]>
        </if>
        <if test="loginAreaCode !=null and loginAreaCode !=''">
            AND d.area_code LIKE concat(#{loginAreaCode},'%')
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptListByRoleId" resultType="Long">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id =
            rd.dept_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.order_num
    </select>

    <select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		select d.dept_id, d.parent_id, d.ancestors, d.dept_name, d.order_num, d.area_code,d.leader, d.phone, d.email, d.status,d.supervision_flag,d.area_name,d.area_level,d.dept_type,d.create_by,d.create_time,
			(select dept_name from sys_dept where dept_id = d.parent_id) parent_name
		from sys_dept d
		where d.dept_id = #{deptId}
	</select>

    <select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from sys_user where dept_id = #{deptId} and del_flag = '1'
	</select>

    <select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from sys_dept
		where del_flag = '1' and parent_id = #{deptId} limit 1
	</select>

    <select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select * from sys_dept where find_in_set(#{deptId}, ancestors)
	</select>

    <select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		select count(*) from sys_dept where status = 0 and del_flag = '1' and find_in_set(#{deptId}, ancestors)
	</select>

    <select id="checkDeptNameUnique" resultMap="SysDeptResult">
        <include refid="selectDeptVo"/>
        where dept_name=#{deptName}
        and parent_id = #{parentId}
        <if test="deptId!= null and deptId != ''">
            AND dept_id != #{deptId}
        </if>
         and del_flag = '1'
          limit 1
    </select>

    <insert id="insertDept" parameterType="com.jkr.project.system.domain.SysDept">
        insert into sys_dept(
        <if test="deptId != null and deptId != 0">dept_id,</if>
        <if test="parentId != null and parentId != 0">parent_id,</if>
        <if test="deptName != null and deptName != ''">dept_name,</if>
        <if test="ancestors != null and ancestors != ''">ancestors,</if>
        <if test="orderNum != null">order_num,</if>
        <if test="areaCode != null and areaCode != ''">area_code,</if>
        <if test="leader != null and leader != ''">leader,</if>
        <if test="phone != null and phone != ''">phone,</if>
        <if test="email != null and email != ''">email,</if>
        <if test="status != null">status,</if>
        <if test="supervisionFlag != null">supervision_flag,</if>
        <if test="createBy != null and createBy != ''">create_by,</if>
        create_time
        )values(
        <if test="deptId != null and deptId != 0">#{deptId},</if>
        <if test="parentId != null and parentId != 0">#{parentId},</if>
        <if test="deptName != null and deptName != ''">#{deptName},</if>
        <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
        <if test="orderNum != null">#{orderNum},</if>
        <if test="areaCode != null and areaCode != ''">#{areaCode},</if>
        <if test="leader != null and leader != ''">#{leader},</if>
        <if test="phone != null and phone != ''">#{phone},</if>
        <if test="email != null and email != ''">#{email},</if>
        <if test="status != null">#{status},</if>
        <if test="supervisionFlag != null">#{supervisionFlag},</if>
        <if test="createBy != null and createBy != ''">#{createBy},</if>
        sysdate()
        )
    </insert>

    <update id="updateDept" parameterType="com.jkr.project.system.domain.SysDept">
        update sys_dept
        <set>
            <if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
            <if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
            <if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="areaCode != null and areaCode != ''">area_code = #{areaCode},</if>
            <if test="areaName != null and areaName != ''">area_name = #{areaName},</if>
            <if test="areaLevel != null and areaLevel != ''">area_level = #{areaLevel},</if>
            <if test="deptType != null and deptType != ''">dept_type = #{deptType},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="supervisionFlag != null and supervisionFlag != ''">supervision_flag = #{supervisionFlag},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where dept_id = #{deptId}
    </update>

    <update id="updateDeptChildren" parameterType="java.util.List">
        update sys_dept set ancestors =
        <foreach collection="depts" item="item" index="index"
                 separator=" " open="case dept_id" close="end">
            when #{item.deptId} then #{item.ancestors}
        </foreach>
        where dept_id in
        <foreach collection="depts" item="item" index="index"
                 separator="," open="(" close=")">
            #{item.deptId}
        </foreach>
    </update>

    <update id="updateDeptStatusNormal" parameterType="Long">
        update sys_dept set status = '0' where dept_id in
        <foreach collection="array" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </update>

    <delete id="deleteDeptById" parameterType="Long">
		update sys_dept set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where dept_id = #{deptId}
	</delete>

</mapper>
