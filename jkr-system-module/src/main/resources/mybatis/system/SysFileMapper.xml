<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysFileMapper">

    <resultMap type="com.jkr.project.system.domain.SysFile" id="SysFileResult">
        <result property="fileId"     column="file_id"    />
        <result property="tableId"    column="table_id" />
        <result property="tableName"  column="table_name"/>
        <result property="fieldType"  column="field_type"/>
        <result property="name"       column="name"    />
        <result property="realName"   column="real_name"/>
        <result property="path"       column="path"    />
        <result property="type"       column="type"/>
        <result property="size"       column="size"    />
        <result property="sourceType" column="source_type"/>
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"   column="create_by"    />
        <result property="createTime" column="create_time"    />
        <result property="updateBy"   column="update_by"    />
        <result property="updateTime" column="update_time"    />
        <result property="remark"     column="remark"    />
    </resultMap>

    <sql id="selectSysFileVo">
        select file_id, table_id,table_name,field_type, name, path,  type, size, source_type, del_flag, create_by, create_time, update_by, update_time, remark from sys_file
    </sql>

    <select id="selectSysFileList" parameterType="com.jkr.project.system.domain.SysFile" resultMap="SysFileResult">
        <include refid="selectSysFileVo"/>
        <where>
            del_flag = '1'
            <if test="tableId != null  and tableId != ''"> and table_id = #{tableId}</if>
            <if test="tableName != null  and tableName != ''"> and table_name = #{tableName}</if>
            <if test="fieldType != null  and fieldType != ''"> and field_type = #{fieldType}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="size != null "> and size = #{size}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="sourceType != null and sourceType != ''">and source_type = #{sourceType}</if>
        </where>
    </select>

    <select id="selectSysFileById" parameterType="Long" resultMap="SysFileResult">
            <include refid="selectSysFileVo"/>
            where file_id = #{fileId}
    </select>

    <insert id="insertSysFile" parameterType="com.jkr.project.system.domain.SysFile" useGeneratedKeys="true" keyProperty="fileId">
        insert into sys_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableId != null">table_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="name != null">name,</if>
            <if test="realName != null">real_name,</if>
            <if test="fieldType != null">field_type,</if>
            <if test="path != null and path != ''">path,</if>
            <if test="type != null">type,</if>
            <if test="size != null">size,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tableId != null">#{tableId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="name != null">#{name},</if>
            <if test="realName != null">#{realName},</if>
            <if test="fieldType != null">#{fieldType},</if>
            <if test="path != null and path != ''">#{path},</if>
            <if test="type != null">#{type},</if>
            <if test="size != null">#{size},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>

    <update id="updateSysFile" parameterType="com.jkr.project.system.domain.SysFile">
        update sys_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="tableId != null">table_id = #{tableId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="fieldType != null">field_type = #{fieldType},</if>
            <if test="path != null and path != ''">path = #{path},</if>
            <if test="type != null">type = #{type},</if>
            <if test="size != null">size = #{size},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteSysFileById" parameterType="Long">
        update sys_file set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where file_id = #{fileId}
    </delete>

    <delete id="deleteSysFileByIds" parameterType="String">
        update sys_file set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where file_id in
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>

    <!-- 根据业务表Id逻辑删除附件 QuGe -->
    <update id="removeByTableId">
        UPDATE sys_file SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') WHERE table_id = #{tableId}
    </update>
    <!-- 根据业务表Id和图片业务类型逻辑删除附件 ZhangLing -->
    <update id="removeByTableIdAndFieldType">
        UPDATE sys_file SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') WHERE table_id = #{tableId} AND type = #{fieldType}
    </update>
</mapper>
