<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysWechatMapper">

    <resultMap type="com.jkr.project.system.domain.SysWechat" id="WechatResult">
            <result property="id"    column="id"    />
            <result property="name"    column="name"    />
            <result property="wechatAppId"    column="wechat_app_id"    />
            <result property="wechatAppSecret"    column="wechat_app_secret"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectWechatVo">
        select id, name, wechat_app_id, wechat_app_secret, create_by, create_time, update_by, update_time, remark from sys_wechat
    </sql>

    <select id="selectWechatList" parameterType="com.jkr.project.system.domain.SysWechat" resultMap="WechatResult">
        <include refid="selectWechatVo"/>
        <where>
                        <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
                        <if test="wechatAppId != null  and wechatAppId != ''"> and wechat_app_id = #{wechatAppId}</if>
                        <if test="wechatAppSecret != null  and wechatAppSecret != ''"> and wechat_app_secret = #{wechatAppSecret}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectWechatById" parameterType="Long" resultMap="WechatResult">
            <include refid="selectWechatVo"/>
            where id = #{id}
    </select>

    <insert id="insertWechat" parameterType="com.jkr.project.system.domain.SysWechat">
        insert into sys_wechat
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="name != null">name,</if>
                    <if test="wechatAppId != null">wechat_app_id,</if>
                    <if test="wechatAppSecret != null">wechat_app_secret,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="name != null">#{name},</if>
                    <if test="wechatAppId != null">#{wechatAppId},</if>
                    <if test="wechatAppSecret != null">#{wechatAppSecret},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateWechat" parameterType="com.jkr.project.system.domain.SysWechat">
        update sys_wechat
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">name = #{name},</if>
                    <if test="wechatAppId != null">wechat_app_id = #{wechatAppId},</if>
                    <if test="wechatAppSecret != null">wechat_app_secret = #{wechatAppSecret},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="logicRemoveByIds" parameterType="String">
        update sys_wechat set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="logicRemoveById" parameterType="Long">
        update sys_wechat set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id = #{id}
    </update>

    <delete id="deleteWechatById" parameterType="Long">
        delete from sys_wechat where id = #{id}
    </delete>

    <delete id="deleteWechatByIds" parameterType="String">
        delete from sys_wechat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
