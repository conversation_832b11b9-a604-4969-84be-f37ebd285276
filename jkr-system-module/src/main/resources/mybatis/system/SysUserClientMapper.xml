<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.system.mapper.SysUserClientMapper">
    <resultMap type="com.jkr.project.system.domain.SysUserClient" id="SysUserClientResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="clientLoginName"    column="client_login_name"    />
        <result property="clientId"    column="client_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysUserClientVo">
        select
            id,
            user_id,
            client_login_name,
            client_id,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time,
            remark
        from
            sys_user_client
    </sql>

    <select id="selectSysUserClientList" parameterType="com.jkr.project.system.domain.SysUserClient" resultMap="SysUserClientResult">
        <include refid="selectSysUserClientVo"/>
        <where>
            del_flag = '1'
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="clientLoginName != null  and clientLoginName != ''"> and client_login_name like concat('%', #{clientLoginName}, '%')</if>
            <if test="clientId != null  and clientId != ''"> and client_id = #{clientId}</if>
        </where>
    </select>

    <select id="selectSysUserClientById" parameterType="Long" resultMap="SysUserClientResult">
        <include refid="selectSysUserClientVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysUserClient" parameterType="com.jkr.project.system.domain.SysUserClient">
        insert into sys_user_client
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="clientLoginName != null">client_login_name,</if>
            <if test="clientId != null and clientId != ''">client_id,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="clientLoginName != null">#{clientLoginName},</if>
            <if test="clientId != null and clientId != ''">#{clientId},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysUserClient" parameterType="com.jkr.project.system.domain.SysUserClient">
        update sys_user_client
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="clientLoginName != null">client_login_name = #{clientLoginName},</if>
            <if test="clientId != null and clientId != ''">client_id = #{clientId},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysUserClientById" parameterType="Long">
        UPDATE sys_user_client SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') WHERE id = #{id}
    </delete>

    <delete id="deleteSysUserClientByIds" parameterType="String">
        UPDATE sys_user_client SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <!-- 根据对象删除数据 -->
    <delete id="deleteSysUserClientByEntity" >
        UPDATE sys_user_client
        SET del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','')
        WHERE del_flag = 1
        <if test="userId != null and userId != ''"> and user_id = #{userId}</if>
        <if test="clientId != null and clientId != ''">and client_id = #{clientId}</if>
    </delete>
    <!-- 查询用户是否授权应用系统 -->
    <select id="hasAuthByUser"  resultType="int">
        select count(1) from sys_user_client uc
        inner join sys_client c on uc.client_id = c.id
        where
            c.del_flag = '1'
          and uc.del_flag = '1'
          and uc.user_id = #{userId}
          and c.client_app_id = #{clientAppId}
          and c.client_secret = #{clientSecret}
        limit 1
    </select>
</mapper>