<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.monitor.mapper.SysDocumentMdMapper">

    <resultMap type="com.jkr.project.monitor.domain.SysDocumentMd" id="SysDocumentMdResult">
            <result property="mdId"    column="md_id"    />
            <result property="dirId"    column="dir_id"    />
            <result property="render"    column="render"    />
            <result property="value"    column="value"    />
            <result property="status"    column="status"    />
            <result property="title"    column="title"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="remark"    column="remark"    />
            <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <sql id="selectSysDocumentMdVo">
        select md_id, dir_id, render, value, status, title,
               create_by, create_time, update_by, update_time, del_flag,
               tenant_id, remark, tenant_id
        from sys_document_md
    </sql>



    <select id="selectSysDocumentMdList" parameterType="com.jkr.project.monitor.domain.SysDocumentMd" resultMap="SysDocumentMdResult">
        <include refid="selectSysDocumentMdVo"/>
        <where>
            <if test="dirId != null "> and dir_id = #{dirId}</if>
            <if test="render != null  and render != ''"> and render = #{render}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{creator}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updater}</if>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectSysDocumentMdAndDirList" parameterType="com.jkr.project.monitor.domain.SysDocumentMd" resultType="com.jkr.project.monitor.domain.SysDocumentMd">
        select
            m.md_id as mdId,
            m.dir_id as dirId,
            m.render as render,
            m.value as value,
            m.status as status,
            m.title as title,
            m.create_by as createBy,
            m.create_time as createTime,
            m.update_by as updateBy,
            m.update_time as updateTime,
            m.del_flag as delFlag,
            m.tenant_id as mdId,
            m.remark as remark,
            m.tenant_id as tenantId,
            d.dir_type as dirType,
            d.dir_name as dirName
        from sys_document_md m
        left join sys_document_dir d on m.dir_id = d.dir_id and d.del_flag = '1'
        <where>
            <if test="dirId != null "> and m.dir_id = #{dirId}</if>
            <if test="render != null  and render != ''"> and m.render = #{render}</if>
            <if test="value != null  and value != ''"> and m.value = #{value}</if>
            <if test="status != null  and status != ''"> and m.status = #{status}</if>
            <if test="title != null  and title != ''"> and m.title = #{title}</if>
            <if test="createBy != null  and createBy != ''"> and m.create_by = #{creator}</if>
            <if test="updateBy != null  and updateBy != ''"> and m.update_by = #{updater}</if>
            <if test="delFlag != null "> and m.del_flag = #{delFlag}</if>
            <if test="tenantId != null "> and m.tenant_id = #{tenantId}</if>
            and m.del_flag = '1'
        </where>
        order by d.dir_type, m.md_id
    </select>

    <select id="selectSysDocumentMdById" parameterType="Long" resultMap="SysDocumentMdResult">
            <include refid="selectSysDocumentMdVo"/>
            where md_id = #{mdId}
    </select>

    <insert id="insertSysDocumentMd" parameterType="com.jkr.project.monitor.domain.SysDocumentMd" useGeneratedKeys="true" keyProperty="mdId">
        insert into sys_document_md
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="dirId != null">dir_id,</if>
                    <if test="render != null">render,</if>
                    <if test="value != null">value,</if>
                    <if test="status != null">status,</if>
                    <if test="title != null">title,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="delFlag != null">del_flag,</if>
                    <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="dirId != null">#{dirId},</if>
                    <if test="render != null">#{render},</if>
                    <if test="value != null">#{value},</if>
                    <if test="status != null">#{status},</if>
                    <if test="title != null">#{title},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="delFlag != null">#{delFlag},</if>
                    <if test="tenantId != null">#{tenantId},</if>
        </trim>
    </insert>

    <update id="updateSysDocumentMd" parameterType="com.jkr.project.monitor.domain.SysDocumentMd">
        update sys_document_md
        <trim prefix="SET" suffixOverrides=",">
                    <if test="dirId != null">dir_id = #{dirId},</if>
                    <if test="render != null">render = #{render},</if>
                    <if test="value != null">value = #{value},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="title != null">title = #{title},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="delFlag != null">del_flag = #{delFlag},</if>
                    <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where md_id = #{mdId}
    </update>

    <update id="logicRemoveByMdIds" parameterType="String">
        update sys_document_md set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where md_id in
        <foreach item="mdId" collection="array" open="(" separator="," close=")">
            #{mdId}
        </foreach>
    </update>

    <update id="logicRemoveByMdId" parameterType="Long">
        update sys_document_md set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where md_id = #{mdId}
    </update>

    <delete id="deleteSysDocumentMdByMdId" parameterType="Long">
        delete from sys_document_md where md_id = #{mdId}
    </delete>

    <delete id="deleteSysDocumentMdByMdIds" parameterType="String">
        delete from sys_document_md where md_id in
        <foreach item="mdId" collection="array" open="(" separator="," close=")">
            #{mdId}
        </foreach>
    </delete>
</mapper>