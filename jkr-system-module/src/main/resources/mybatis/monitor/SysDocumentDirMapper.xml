<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jkr.project.monitor.mapper.SysDocumentDirMapper">

    <resultMap type="com.jkr.project.monitor.domain.SysDocumentDir" id="SysDocumentDirResult">
            <result property="dirId"    column="dir_id"    />
            <result property="parentId"    column="parent_id"    />
            <result property="ancestors"    column="ancestors"    />
            <result property="dirName"    column="dir_name"    />
            <result property="orderNum"    column="order_num"    />
            <result property="status"    column="status"    />
            <result property="dirType"    column="dir_type"    />
            <result property="createBy"    column="create_by"    />
            <result property="createTime"    column="create_time"    />
            <result property="updateBy"    column="update_by"    />
            <result property="updateTime"    column="update_time"    />
            <result property="delFlag"    column="del_flag"    />
            <result property="tenantId"    column="tenant_id"    />
            <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysDocumentDirVo">
        select dir_id, parent_id, ancestors, dir_name, order_num, status, dir_type, create_by, create_time, update_by, update_time, del_flag, tenant_id, remark from sys_document_dir
    </sql>

    <select id="selectSysDocumentDirList" parameterType="com.jkr.project.monitor.domain.SysDocumentDir" resultMap="SysDocumentDirResult">
        <include refid="selectSysDocumentDirVo"/>
        <where>
                        <if test="parentId != null "> and parent_id = #{parentId}</if>
                        <if test="ancestors != null  and ancestors != ''"> and ancestors = #{ancestors}</if>
                        <if test="dirName != null  and dirName != ''"> and dir_name like concat('%', #{dirName}, '%')</if>
                        <if test="orderNum != null "> and order_num = #{orderNum}</if>
                        <if test="status != null  and status != ''"> and status = #{status}</if>
                        <if test="dirType != null  and dirType != ''"> and dir_type = #{dirType}</if>
                        <if test="tenantId != null "> and tenant_id = #{tenantId}</if>
            and del_flag = '1'
        </where>
    </select>

    <select id="selectSysDocumentDirById" parameterType="Long" resultMap="SysDocumentDirResult">
            <include refid="selectSysDocumentDirVo"/>
            where dir_id = #{dirId}
    </select>

    <select id="selectChildrenDirById"  resultMap="SysDocumentDirResult">
        <include refid="selectSysDocumentDirVo"/>
        <where>
            del_flag = '1'
            and find_in_set(#{dirId}, ancestors)
        </where>
    </select>

    <select id="checkDirNameUnique" parameterType="com.jkr.project.monitor.domain.SysDocumentDir" resultMap="SysDocumentDirResult">
        <include refid="selectSysDocumentDirVo"/>
        where
        <if test="dirId != null and dirId != '' "> and dir_id != #{dirId}</if>
        <if test="parentId != null and parentId != '' "> and parent_id = #{parentId}</if>
        <if test="dirName != null  and dirName != ''"> and dir_name = #{dirName}</if>
        del_flag = '1'
        limit 1
    </select>

    <insert id="insertSysDocumentDir" parameterType="com.jkr.project.monitor.domain.SysDocumentDir" useGeneratedKeys="true" keyProperty="dirId">
        insert into sys_document_dir
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="parentId != null">parent_id,</if>
                    <if test="ancestors != null">ancestors,</if>
                    <if test="dirName != null">dir_name,</if>
                    <if test="orderNum != null">order_num,</if>
                    <if test="status != null">status,</if>
                    <if test="dirType != null">dir_type,</if>
                    <if test="createBy != null">create_by,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateBy != null">update_by,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="delFlag != null and delFlag != ''">del_flag,</if>
                    <if test="tenantId != null">tenant_id,</if>
                    <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="parentId != null">#{parentId},</if>
                    <if test="ancestors != null">#{ancestors},</if>
                    <if test="dirName != null">#{dirName},</if>
                    <if test="orderNum != null">#{orderNum},</if>
                    <if test="status != null">#{status},</if>
                    <if test="dirType != null">#{dirType},</if>
                    <if test="createBy != null">#{createBy},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateBy != null">#{updateBy},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
                    <if test="tenantId != null">#{tenantId},</if>
                    <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSysDocumentDir" parameterType="com.jkr.project.monitor.domain.SysDocumentDir">
        update sys_document_dir
        <trim prefix="SET" suffixOverrides=",">
                    <if test="parentId != null">parent_id = #{parentId},</if>
                    <if test="ancestors != null">ancestors = #{ancestors},</if>
                    <if test="dirName != null">dir_name = #{dirName},</if>
                    <if test="orderNum != null">order_num = #{orderNum},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="dirType != null">dir_type = #{dirType},</if>
                    <if test="createBy != null">create_by = #{createBy},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateBy != null">update_by = #{updateBy},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
                    <if test="tenantId != null">tenant_id = #{tenantId},</if>
                    <if test="remark != null">remark = #{remark},</if>
        </trim>
        where dir_id = #{dirId}
    </update>

    <update id="logicRemoveByDirIds" parameterType="String">
        update sys_document_dir set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where dir_id in
        <foreach item="dirId" collection="array" open="(" separator="," close=")">
            #{dirId}
        </foreach>
    </update>

    <update id="logicRemoveByDirId" parameterType="Long">
        update sys_document_dir set del_flag = REPLACE(unix_timestamp(current_timestamp(3)),'.','') where dir_id = #{dirId}
    </update>

    <delete id="deleteSysDocumentDirByDirId" parameterType="Long">
        delete from sys_document_dir where dir_id = #{dirId}
    </delete>

    <delete id="deleteSysDocumentDirByDirIds" parameterType="String">
        delete from sys_document_dir where dir_id in
        <foreach item="dirId" collection="array" open="(" separator="," close=")">
            #{dirId}
        </foreach>
    </delete>
</mapper>