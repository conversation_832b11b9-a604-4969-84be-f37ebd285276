package com.jkr.framework.web.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;

/**
 * Entity基类
 *
 * <AUTHOR>
 */
@Data
public class BaseEntity implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 搜索值
	 */
	@JsonIgnore
	@TableField(exist = false)
	private String searchValue;

	/**
	 * 创建者
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 更新者
	 */
	private String updateBy;

	/**
	 * 更新时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * 备注
	 */
	@Size(min = 0, max = 500, message = "备注长度不能超过500个字符")
	private String remark;

	/**
	 * 请求参数
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	@TableField(exist = false)
	private Map<String, Object> params = new HashMap<>();

	/**
	 * 当前记录起始索引
	 */
	private Integer pageNum;

	/**
	 * 每页显示记录数
	 */
	private Integer pageSize;
}
