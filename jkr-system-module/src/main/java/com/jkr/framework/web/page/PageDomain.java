package com.jkr.framework.web.page;

import com.jkr.common.utils.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public final class PageDomain {

	/**
	 * 当前记录起始索引
	 */
	private Integer pageNum;

	/**
	 * 每页显示记录数
	 */
	private Integer pageSize;

	/**
	 * 排序列
	 */
	private String orderByColumn;

	/**
	 * 排序的方向desc或者asc
	 */
	private String isAsc = "asc";

	/**
	 * 分页参数合理化
	 */
	private Boolean reasonable = true;

	public String getOrderBy() {
		if (StringUtils.isEmpty(orderByColumn)) {
			return "";
		}
		return StringUtils.toUnderScoreCase(orderByColumn) + " " + isAsc;
	}

}
