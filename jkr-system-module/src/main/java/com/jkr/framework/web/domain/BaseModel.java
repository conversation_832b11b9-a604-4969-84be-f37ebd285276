package com.jkr.framework.web.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.github.yitter.idgen.YitIdHelper;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 业务基类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BaseModel extends BaseEntity {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 删除标识
     */
    private String delFlag;

    /**
     * 新增初始化方法
     */
    public void insertInit(String createBy) {
        if (null == this.id) {
            this.id = YitIdHelper.nextId();
        }
        super.setCreateBy(StringUtils.isNotEmpty(createBy) ? createBy : "");
        super.setCreateTime(DateUtils.getNowDate());
        this.delFlag = "1";
    }

    /**
     * 更新初始化方法
     */
    public void updateInit(String updateBy) {
        super.setUpdateBy(StringUtils.isNotEmpty(updateBy) ? updateBy : "");
        super.setUpdateTime(DateUtils.getNowDate());
    }

}
