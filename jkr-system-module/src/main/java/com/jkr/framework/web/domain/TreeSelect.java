package com.jkr.framework.web.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jkr.common.constant.UserConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.project.system.domain.SysDept;
import com.jkr.project.system.domain.SysMenu;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * Treeselect树结构实体类
 *
 * <AUTHOR>
 */
@Data
public class TreeSelect implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 节点ID
	 */
	private Long id;

	/**
	 * 节点名称
	 */
	private String label;

	/**
	 * 节点禁用
	 */
	private boolean disabled = false;

	/**
	 * 子节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<TreeSelect> children;

	public TreeSelect() {

	}

	public TreeSelect(SysDept dept) {
		this.id = dept.getDeptId();
		this.label = dept.getDeptName();
		this.disabled = StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus());
		this.children = dept.getChildren().stream().map(TreeSelect::new).toList();
	}

	public TreeSelect(SysMenu menu) {
		this.id = menu.getMenuId();
		this.label = menu.getMenuName();
		this.children = menu.getChildren().stream().map(TreeSelect::new).toList();
	}


}
