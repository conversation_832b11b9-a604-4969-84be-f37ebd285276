package com.jkr.framework.web.page;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public final class TableDataInfo implements Serializable {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 总记录数
	 */
	private long total;

	/**
	 * 列表数据
	 */
	private List<?> rows = List.of();

	/**
	 * 消息状态码
	 */
	private int code;

	/**
	 * 消息内容
	 */
	private String msg;

	/**
	 * 分页
	 *
	 * @param list  列表数据
	 * @param total 总记录数
	 */
	public TableDataInfo(List<?> list, int total) {
		this.rows = list;
		this.total = total;
	}


}