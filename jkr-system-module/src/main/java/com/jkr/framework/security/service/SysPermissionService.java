package com.jkr.framework.security.service;

import com.jkr.common.constant.UserConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.project.system.domain.SysRole;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.ISysMenuService;
import com.jkr.project.system.service.ISysRoleService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@Component
public class SysPermissionService {
	@Resource
	private ISysRoleService roleService;

	@Resource
	private ISysMenuService menuService;

	/**
	 * 获取角色数据权限
	 *
	 * @param user 用户信息
	 * @return 角色权限信息
	 */
	public Set<String> getRolePermission(SysUser user) {
		Set<String> roles = new HashSet<String>();
		// 管理员拥有所有权限
		if (user.isAdmin()) {
			roles.add("admin");
		} else {
			roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
		}
		return roles;
	}

	/**
	 * 获取菜单数据权限
	 *
	 * @param user 用户信息
	 * @return 菜单权限信息
	 */
	public Set<String> getMenuPermission(SysUser user) {
		Set<String> perms = new HashSet<String>();
		// 管理员拥有所有权限
		if (user.isAdmin()) {
			perms.add("*:*:*");
		} else {
			List<SysRole> roles = user.getRoles();
			if (!CollectionUtils.isEmpty(roles)) {
				// 设置permissions属性，以便数据权限匹配权限
				for (SysRole role : roles) {
					if (StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL)) {
						Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getRoleId());
						role.setPermissions(rolePerms);
						perms.addAll(rolePerms);
					}
				}
			} else {
				perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
			}
		}
		return perms;
	}
}
