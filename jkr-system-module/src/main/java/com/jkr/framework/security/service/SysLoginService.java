package com.jkr.framework.security.service;

import com.jkr.common.constant.*;
import com.jkr.common.core.text.Convert;
import com.jkr.common.enums.ConfigKeyEnum;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.exception.sms.SmsException;
import com.jkr.common.exception.user.*;
import com.jkr.common.utils.*;
import com.jkr.common.utils.ip.IpUtils;
import com.jkr.framework.manager.AsyncManager;
import com.jkr.framework.manager.factory.AsyncFactory;
import com.jkr.framework.redis.RedisCache;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.security.context.AuthenticationContextHolder;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.*;
import jakarta.annotation.Resource;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
	@Resource
	private TokenService tokenService;

	@Resource
	private AuthenticationManager authenticationManager;

	@Resource
	private RedisCache redisCache;

	@Resource
	private ISysUserService userService;

	@Resource
	private ISysConfigService configService;

	@Resource
	private ISysPasswordHistoryService sysPasswordHistoryService;

	@Resource
	private ISysSmsService sysSmsService;
	/**
	 * 登录验证，并检查是否为首次登录系统
	 *
	 * @param username 用户名
	 * @param password 密码
	 * @param code     验证码
	 * @param uuid     唯一标识
	 * @return 结果
	 */
	public AjaxResult login(String username, String password, String code, String uuid) {
		// 图形验证码校验
		validateCaptcha(username, code, uuid);
		// 短信验证码校验
		//validateSmsCode(username, code);
		// 登录前置校验
		loginPreCheck(username, password);
		// 用户验证
		Authentication authentication = null;
		try {
			UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
			AuthenticationContextHolder.setContext(authenticationToken);
			// 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
			authentication = authenticationManager.authenticate(authenticationToken);
		} catch (Exception e) {
			if (e instanceof BadCredentialsException) {
				AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
				throw new UserPasswordNotMatchException();
			} else {
				AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
				throw new ServiceException(e.getMessage());
			}
		} finally {
			AuthenticationContextHolder.clearContext();
		}
		AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
		LoginUser loginUser = (LoginUser) authentication.getPrincipal();
		recordLoginInfo(loginUser.getUserId());
		// 检查密码
		SysUser sysUser = loginUser.getUser();
		Integer defaultPassword = sysUser.getDefaultPassword();
//		SysPasswordHistory history = new SysPasswordHistory();
//		history.setUserId(loginUser.getUserId());
//		List<SysPasswordHistory> historyList = sysPasswordHistoryService.selectSysPasswordHistoryList(history);
		if (defaultPassword == null || "0".equals(defaultPassword.toString())) {
			// 首次登录，前端强制修改密码
			return new AjaxResult(HttpStatus.FORCE_CHANGED_PASSWORD, "首次登录，强制修改密码",tokenService.createToken(loginUser));
		} else {
			// 生成token
			return AjaxResult.success(tokenService.createToken(loginUser));
		}
	}

	/**
	 * 校验验证码
	 *
	 * @param username 用户名
	 * @param code     验证码
	 * @param uuid     唯一标识
	 * @return 结果
	 */
	public void validateCaptcha(String username, String code, String uuid) {
		boolean captchaEnabled = configService.selectCaptchaEnabled();
		if (captchaEnabled) {
			String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
			String captcha = redisCache.getCacheObject(verifyKey);
			if (captcha == null) {
				AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
				throw new CaptchaExpireException();
			}
			redisCache.deleteObject(verifyKey);
			if (!code.equalsIgnoreCase(captcha)) {
				AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
				throw new CaptchaException();
			}
		}
	}
	/**
	 * @title validateSmsCode
	 * @Description: 验证短信验证码
	 * @param loginName
	 * @param code
	 * @return void
	 * <AUTHOR>
	 * @date 2025/1/17 17:17
	 */
	public void validateSmsCode(String loginName, String code) {
		boolean smsEnabled = Convert.toBool(configService.selectDataForSmsConfig().get(ConfigKeyEnum.SYS_ACCOUNT_SMS_ENABLED.getKey()));
		if (smsEnabled) {
			SysUser sysUser = userService.selectUserByUserNameOrMobile(loginName);
			String phone=sysUser.getPhone();
			//mysql 验证并修改
			sysSmsService.checkSmsCode(phone,code);
			//redis 验证并删除
			String verifyKey = CacheConstants.SMS_CODE_KEY + StringUtils.nvl(phone, "");
			String smsCodeCache = redisCache.getCacheObject(verifyKey);
			if (smsCodeCache == null) {
				throw new SmsException();
			}
			redisCache.deleteObject(verifyKey);

		}
	}

	/**
	 * 登录前置校验
	 *
	 * @param username 用户名
	 * @param password 用户密码
	 */
	public void loginPreCheck(String username, String password) {
		// 用户名或密码为空 错误
		if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
			AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
			throw new UserNotExistsException();
		}
		// 密码如果不在指定范围内 错误
		if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
				|| password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
			AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
			throw new UserPasswordNotMatchException();
		}
		// 用户名不在指定范围内 错误
		if (username.length() < UserConstants.USERNAME_MIN_LENGTH
				|| username.length() > UserConstants.USERNAME_MAX_LENGTH) {
			AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
			throw new UserPasswordNotMatchException();
		}
		// IP黑名单校验
		String blackStr = configService.selectConfigByKey(SysConfigConstants.SYS_LOGIN_BLACK_IP_LIST);
		if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
			AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
			throw new BlackListException();
		}
	}

	/**
	 * 记录登录信息
	 *
	 * @param userId 用户ID
	 */
	public void recordLoginInfo(Long userId) {
		SysUser sysUser = new SysUser();
		sysUser.setUserId(userId);
		sysUser.setLoginIp(IpUtils.getIpAddr());
		sysUser.setLoginDate(DateUtils.getNowDate());
		userService.updateUserProfile(sysUser);
	}
}
