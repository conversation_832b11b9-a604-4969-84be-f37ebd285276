package com.jkr.framework.security;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用户登录对象
 *
 * <AUTHOR>
 */
@Data
public class LoginBody {
	/**
	 * 用户名
	 */
	@NotBlank(message = "登录名不能为空")
	private String username;

	/**
	 * 用户密码
	 */
	@NotBlank(message = "登录密码不能为空")
	private String password;

	/**
	 * 图形验证码
	 */
	private String code;
	/**
	 * 短信验证码
	 */
	private String smsCode;

	/**
	 * 唯一标识
	 */
	private String uuid;

}
