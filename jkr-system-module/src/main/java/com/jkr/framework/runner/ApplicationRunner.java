package com.jkr.framework.runner;

import com.jkr.project.system.service.ISysAreaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 应用启动后执行服务
 * <AUTHOR>
 */
@Slf4j
@Component  //被 spring 容器管理
@Order(10)   //如果多个自定义的 ApplicationRunner  ，用来标明执行的顺序
public class ApplicationRunner implements org.springframework.boot.ApplicationRunner {
    @Autowired
    private ISysAreaService sysAreaService;

    @Override
    public void run(ApplicationArguments args) throws Exception {

    }
}
