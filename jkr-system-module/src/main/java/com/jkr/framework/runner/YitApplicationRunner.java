package com.jkr.framework.runner;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import com.jkr.common.exception.GlobalException;
import com.jkr.framework.handler.WorkerIdHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 应用启动后执行分配worker id
 *
 * <AUTHOR>
 */
@Slf4j
@Order(0)
@Component
@RequiredArgsConstructor
public class YitApplicationRunner implements ApplicationRunner {
    private final WorkerIdHandler workerIdHandler;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String workerId = workerIdHandler.getWorkerId();
        if (workerId == null) {
            throw new GlobalException("workerId 分配失败！");
        }
        IdGeneratorOptions options = new IdGeneratorOptions(Short.parseShort(workerId));
        YitIdHelper.setIdGenerator(options);
        log.info("雪花id生成器设置完成");
    }
}
