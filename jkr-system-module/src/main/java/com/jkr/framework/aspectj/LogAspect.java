package com.jkr.framework.aspectj;

import com.alibaba.fastjson2.JSON;
import com.jkr.common.enums.HttpMethod;
import com.jkr.common.filter.PropertyPreExcludeFilter;
import com.jkr.common.utils.*;
import com.jkr.common.utils.ip.IpUtils;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessStatus;
import com.jkr.framework.manager.AsyncManager;
import com.jkr.framework.manager.factory.AsyncFactory;
import com.jkr.framework.security.LoginUser;
import com.jkr.project.monitor.domain.SysOperLog;
import com.jkr.project.system.domain.SysUser;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.equalsAny;

/**
 * 操作日志记录处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class LogAspect {
	/**
	 * 排除敏感属性字段
	 */
	private static final String[] EXCLUDE_PROPERTIES = {"password", "oldPassword", "newPassword", "confirmPassword", "phone", "email"};
	private static final Logger log = LoggerFactory.getLogger(LogAspect.class);
	/**
	 * 计算操作消耗时间
	 */
	private static final ThreadLocal<Long> TIME_THREADLOCAL = new NamedThreadLocal<>("Cost Time");

	/**
	 * 处理请求前执行
	 */
	@Before(value = "@annotation(controllerLog)")
	public void boBefore(JoinPoint joinPoint, Log controllerLog) {
		TIME_THREADLOCAL.set(System.currentTimeMillis());
	}

	/**
	 * 处理完请求后执行
	 *
	 * @param joinPoint 切点
	 */
	@AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
	public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
		handleLog(joinPoint, controllerLog, null, jsonResult);
	}

	/**
	 * 拦截异常操作
	 *
	 * @param joinPoint 切点
	 * @param e         异常
	 */
	@AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
	public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
		handleLog(joinPoint, controllerLog, e, null);
	}

	/**
	 * 日志处理
	 *
	 * @param joinPoint     切面
	 * @param controllerLog 注解
	 * @param e             异常
	 * @param jsonResult    方法返回值
	 */
	protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult) {
		try {
			// 获取当前的用户
			LoginUser loginUser = SecurityUtils.getLoginUser();
			// *========数据库日志=========*//
			SysOperLog operLog = new SysOperLog();
			operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
			// 请求的地址
			String ip = IpUtils.getIpAddr();
			operLog.setOperIp(ip);
			operLog.setOperUrl(StringUtils.substring(ServletUtils.getRequest().getRequestURI(), 0, 255));
			// 记录用户信息
			if (loginUser != null) {
				operLog.setOperName(loginUser.getUsername());
				SysUser currentUser = loginUser.getUser();
				if (StringUtils.isNotNull(currentUser) && StringUtils.isNotNull(currentUser.getDept())) {
					operLog.setDeptName(currentUser.getDept().getDeptName());
				}
			}
			// 记录异常信息
			if (e != null) {
				operLog.setStatus(BusinessStatus.FAIL.ordinal());
				operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
			}
			// 设置方法名称
			String className = joinPoint.getTarget().getClass().getName();
			String methodName = joinPoint.getSignature().getName();
			operLog.setMethod(className + "." + methodName + "()");
			// 设置请求方式
			operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
			// 处理设置注解上的参数
			getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult);
			// 设置消耗时间
			operLog.setCostTime(System.currentTimeMillis() - TIME_THREADLOCAL.get());
			// 保存数据库
			AsyncManager.me().execute(AsyncFactory.recordOper(operLog));
		} catch (Exception exp) {
			// 记录本地异常日志
			log.error("异常信息:{}", exp.getMessage());
		} finally {
			TIME_THREADLOCAL.remove();
		}
	}

	/**
	 * 获取注解中对方法的描述信息 用于Controller层注解
	 *
	 * @param log     日志
	 * @param operLog 操作日志
	 */
	public void getControllerMethodDescription(JoinPoint joinPoint, Log log, SysOperLog operLog, Object jsonResult) {
		// 设置action动作
		operLog.setBusinessType(log.businessType().ordinal());
		// 设置标题
		operLog.setTitle(log.title());
		// 设置操作人类别
		operLog.setOperatorType(log.operatorType().ordinal());
		// 是否需要保存request，参数和值
		if (log.isSaveRequestData()) {
			// 获取参数的信息，传入到数据库中。
			setRequestValue(joinPoint, operLog, log.excludeParamNames());
		}
		// 是否需要保存response，参数和值
		if (log.isSaveResponseData() && StringUtils.isNotNull(jsonResult)) {
			operLog.setJsonResult(StringUtils.substring(JSON.toJSONString(jsonResult), 0, 2000));
		}
	}

	/**
	 * 获取请求的参数，放到log中
	 *
	 * @param operLog 操作日志
	 */
	private void setRequestValue(JoinPoint joinPoint, SysOperLog operLog, String[] excludeParamNames) {
		String requestMethod = operLog.getRequestMethod();
		Map<?, ?> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
		if (StringUtils.isEmpty(paramsMap) && equalsAny(requestMethod, HttpMethod.PUT.name(), HttpMethod.POST.name(), HttpMethod.DELETE.name())) {
			String params = argsArrayToString(joinPoint.getArgs(), excludeParamNames);
			operLog.setOperParam(StringUtils.substring(params, 0, 2000));
		} else {
			operLog.setOperParam(StringUtils.substring(JSON.toJSONString(paramsMap, excludePropertyPreFilter(excludeParamNames)), 0, 2000));
		}
	}

	/**
	 * 参数拼装
	 */
	private String argsArrayToString(Object[] paramsArray, String[] excludeParamNames) {
		StringBuilder params = new StringBuilder();
		if (paramsArray != null) {
			for (Object o : paramsArray) {
				if (StringUtils.isNotNull(o) && !isFilterObject(o)) {
					try {
						String jsonObj = JSON.toJSONString(o, excludePropertyPreFilter(excludeParamNames));
						params.append(jsonObj).append(" ");
					} catch (Exception e) {
						log.error(e.getMessage(), e);
					}
				}
			}
		}
		return params.toString().trim();
	}

	/**
	 * 忽略敏感属性
	 */
	public PropertyPreExcludeFilter excludePropertyPreFilter(String[] excludeParamNames) {
		return new PropertyPreExcludeFilter().addExcludes(ArrayUtils.addAll(EXCLUDE_PROPERTIES, excludeParamNames));
	}

	/**
	 * 判断是否需要过滤的对象。
	 *
	 * @param o 对象信息。
	 * @return 如果是需要过滤的对象，则返回true；否则返回false。
	 */
	public boolean isFilterObject(final Object o) {
		// 检查null
		if (o == null) {
			return false;
		}
		// 判断特定类型
		if (o instanceof MultipartFile || o instanceof HttpServletRequest
				|| o instanceof HttpServletResponse || o instanceof BindingResult) {
			return true;
		}
		// 检查是否为数组
		if (o.getClass().isArray()) {
			return o.getClass().getComponentType().isAssignableFrom(MultipartFile.class);
		}
		// 检查是否为集合
		if (o instanceof Collection<?>) {
			return containsMultipartFile((Collection<?>) o);
		}
		// 检查是否为Map
		if (o instanceof Map<?, ?>) {
			return containsMultipartFile(((Map<?, ?>) o).values());
		}
		return false;

	}

	/**
	 * 检查集合中是否包含 MultipartFile 类型的元素
	 */
	private boolean containsMultipartFile(Collection<?> collection) {
		for (Object value : collection) {
			if (value instanceof MultipartFile) {
				return true;
			}
		}
		return false;
	}
}
