package com.jkr.framework.aspectj.lang.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.jkr.framework.aspectj.lang.enums.DesensitizedType;
import com.jkr.framework.config.SensitiveJsonSerializer;

import java.lang.annotation.*;

/**
 * 数据脱敏注解
 *
 * <AUTHOR>
 * @see com.jkr.framework.aspectj.DesensitizeAspect
 * @see com.jkr.framework.desensitize.annotation.ApiDesensitize
 * @see com.jkr.framework.desensitize.annotation.BeanDesensitize
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@JacksonAnnotationsInside
@JsonSerialize(using = SensitiveJsonSerializer.class)
@Deprecated
public @interface Sensitive {
	DesensitizedType desensitizedType();
}
