package com.jkr.framework.aspectj;

import cn.hutool.core.util.ReflectUtil;
import com.jkr.common.utils.spring.SpringExpressionUtils;
import com.jkr.framework.desensitize.annotation.ApiDesensitize;
import com.jkr.framework.desensitize.annotation.BeanDesensitize;
import com.jkr.framework.web.domain.BaseEntity;
import com.jkr.framework.web.page.TableDataInfo;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

/**
 * DesensitizeAspect 脱敏
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-19 10:19
 */
@Aspect
@Component
public class DesensitizeAspect {
	@Pointcut("@annotation(org.springframework.web.bind.annotation.GetMapping)")
	public void getMapping() {
	}

	@Pointcut("@annotation(org.springframework.web.bind.annotation.PostMapping)")
	public void postMapping() {
	}

	@Pointcut("@annotation(org.springframework.web.bind.annotation.DeleteMapping)")
	public void deleteMapping() {
	}

	@Pointcut("@annotation(org.springframework.web.bind.annotation.PutMapping)")
	public void putMapping() {
	}

	@Around("getMapping()||postMapping()||deleteMapping()||putMapping()")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		Signature signature = joinPoint.getSignature();
		MethodSignature methodSignature = (MethodSignature) signature;
		Method method = methodSignature.getMethod();
		ApiDesensitize annotation = method.getAnnotation(ApiDesensitize.class);
		// 方法未使用ApiNoDesensitize注解
		if (annotation != null) {
			Object result = joinPoint.proceed();
			if (result instanceof TableDataInfo) {
				List<?> list = ((TableDataInfo) result).getRows();
				if (list != null && !list.isEmpty()) {
					for (Object object : list) {
						if (object instanceof BaseEntity) {
							desensitizeFields(object);
						}
					}
				}
			}
			return result;
		}
		return joinPoint.proceed();
	}

	/**
	 * 对象数据脱敏
	 *
	 * @param object 返回值对象实例
	 * @return 脱敏后的数据对象
	 * @throws IllegalAccessException
	 */
	private Object desensitizeFields(Object object) throws IllegalAccessException {
		if (object == null) {
			return null;
		}

		// 获取类的所有字段
		Field[] fields = object.getClass().getDeclaredFields();

		for (Field field : fields) {
			field.setAccessible(true);
			// 检查字段上是否有脱敏注解
			if (field.isAnnotationPresent(BeanDesensitize.class)) {
				BeanDesensitize sensitive = field.getAnnotation(BeanDesensitize.class);
				String value = (String) field.get(object);
				if (value != null) {
					field.set(object, desensitize(value, sensitive));
				}
			}
		}

		return object;
	}

	public String desensitize(String origin, BeanDesensitize annotation) {
		// 1. 判断是否禁用脱敏
		if (getDisable(annotation)) {
			return origin;
		}
		// 2. 执行脱敏
		int prefixKeep = getHeader(annotation);
		int suffixKeep = getTail(annotation);
		String replacer = getReplace(annotation);
		int length = origin.length();

		// 情况一：原始字符串长度小于等于保留长度，则原始字符串全部替换
		if (prefixKeep >= length || suffixKeep >= length) {
			return buildReplacerByLength(replacer, length);
		}

		// 情况二：原始字符串长度小于等于前后缀保留字符串长度，则原始字符串全部替换
		if ((prefixKeep + suffixKeep) >= length) {
			return buildReplacerByLength(replacer, length);
		}

		// 情况三：原始字符串长度大于前后缀保留字符串长度，则替换中间字符串
		int interval = length - prefixKeep - suffixKeep;
		return origin.substring(0, prefixKeep) +
				buildReplacerByLength(replacer, interval) +
				origin.substring(prefixKeep + interval);
	}

	private boolean getDisable(BeanDesensitize annotation) {
		try {
			// 首先检查角色设置
			String disable = ReflectUtil.invoke(annotation, "disable");
			return parseDisable(disable);
		} catch (Exception e) {
			return false;
		}
	}

	Integer getHeader(BeanDesensitize annotation) {
		return annotation.header();
	}

	Integer getTail(BeanDesensitize annotation) {
		return annotation.tail();
	}

	String getReplace(BeanDesensitize annotation) {
		return annotation.replace();
	}

	private boolean parseDisable(String spel) {
		Object object = SpringExpressionUtils.parseExpression(spel);
		return Boolean.TRUE.equals(object);
	}

	/**
	 * 根据长度循环构建替换符
	 *
	 * @param replacer 替换符
	 * @param length   长度
	 * @return 构建后的替换符
	 */
	private String buildReplacerByLength(String replacer, int length) {
		StringBuilder builder = new StringBuilder();
		for (int i = 0; i < length; i++) {
			builder.append(replacer);
		}
		return builder.toString();
	}
}
