package com.jkr.framework.desensitize.annotation;

import java.lang.annotation.*;

/**
 * Java Bean 属性脱敏注解，
 * 当接口未使用ApiNoDesensitize时根据注解对返回值进行脱敏处理
 *
 * <AUTHOR>
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface BeanDesensitize {
	/**
	 * 头部保留长度
	 */
	int header() default 0;

	/**
	 * 尾部保留长度
	 */
	int tail() default 0;

	/**
	 * 替换字符，默认为*
	 */
	String replace() default "*";

	/**
	 * 数据脱敏失效配置，当接口未使用ApiNoDesensitize注解时生效
	 * 支持 Spring 未EL 表达式，若返回 true 则跳过脱敏
	 */
	String disable() default "";
}
