package com.jkr.framework.encrypt.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 秘钥构造器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/6 16:15
 */
@Slf4j
public class KeyEncoder {

    /**
     * 将 key 进行 Base64 编码并转换为十六进制字符串
     *
     * @param key
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025年02月06日 16:18:21
     */
    public static String changeKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new IllegalArgumentException("Key cannot be null or empty");
        }
        // Base64 编码
        String encodeBase64 = Base64.getEncoder().encodeToString(key.getBytes(StandardCharsets.UTF_8));
        // Base64 转换为十六进制
        return base64ToHex(encodeBase64);
    }

    /**
     * 将 Base64 字符串转换为十六进制字符串
     *
     * @param base64
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025年02月06日 16:19:25
     */
    private static String base64ToHex(String base64) {
        // 解码 Base64 字符串为字节数组
        byte[] bytes = Base64.getDecoder().decode(base64);
        // 将字节数组转换为十六进制字符串
        StringBuilder hex = new StringBuilder();
        for (byte b : bytes) {
            hex.append(String.format("%02x", b));
        }
        return hex.toString();
    }

    /**
     * 获取32位固定长度的秘钥
     *
     * @param key 秘钥值
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025年02月06日 16:25:15
     */
    public static String get32CharsKey(String key) {
        String input = changeKey(key);
        int zerosToAdd = 32 - input.length();
        if (zerosToAdd == 0) {
            return input;
        }
        StringBuilder sb = new StringBuilder(input.length() + zerosToAdd);
        sb.append(input);
        for (int i = 0; i < zerosToAdd; i++) {
            sb.append('0');
        }
        return sb.substring(0, 32);
    }

    // 测试
    public static void main(String[] args) {
        String key = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjUxYzllM2YwLWQ0OTItNGM2My04ODBkLTkyNTNjMWJlZWU4NSJ9.kLNdhYjgCNB82VHlumAcnuZCWNx2_lruj5ji7_TPzfty5ZAvdnYKmh3x8pGTSaSR2-4Uf5NAR_lNE_mPWTO6zg";
        String encryptedKey = changeKey(key);
        log.info("Encrypted Key: " + encryptedKey);
        log.info("key值是否相等: " + "65794a68624763694f694a49557a5578".equals(get32CharsKey(encryptedKey)));
    }
}