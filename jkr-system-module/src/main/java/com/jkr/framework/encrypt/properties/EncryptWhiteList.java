package com.jkr.framework.encrypt.properties;

/**
 * 加密请求白名单
 *
 * <AUTHOR>
 * @date 2025年02月06日 09:07:22
 */
public class EncryptWhiteList {

    /**
     * 不进行解密的接口链接(@RequestParam传参方式)(项目名/接口地址)
     */
    public static final String[] WHITE_PARAM_URIS = {
            "/minio/upload",
            "/minio/delete",
            "/minio/download",
            "/minio/baseinfo",
            "/system/user/profile/avatar",
            "/system/user/importData",
            "/tool/gen/batchGenCode",
            "/tool/gen/importTable",
            "/sse/connect",
            "/common/download/resource",
            "/system/user/export",

            "/system/config/export",
            "/monitor/job/export",
            "/monitor/logininfor/export",
            "/monitor/operlog/export",
            "/system/dict/data/export",
            "/system/dict/type/export",
            "/system/post/export",
            "/system/role/export",
            "/monitor/jobLog/export",



    };

    /**
     * 不进行解密的接口链接(@RequestBody传参方式)
     */
    public static final String[] WHITE_BODY_URIS = {
    };
}
