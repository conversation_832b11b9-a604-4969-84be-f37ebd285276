package com.jkr.framework.encrypt.filter;

import com.jkr.common.constant.Constants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.encrypt.properties.EncryptProperties;
import com.jkr.framework.encrypt.servlet.ParameterRequestWrapper;
import com.jkr.framework.encrypt.sm.SM4Util;
import com.jkr.framework.encrypt.util.KeyEncoder;
import jakarta.annotation.Resource;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.jkr.common.utils.ServletUtils.urlDecode;
import static com.jkr.framework.encrypt.properties.EncryptWhiteList.WHITE_PARAM_URIS;

/**
 * request参数解密
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RequestParameterDecryptFilter implements Filter {

    // 令牌自定义标识
    @Value("${token.header}")
    private String header;
    @Resource
    private EncryptProperties encryptProperties;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        ParameterRequestWrapper wrapper = new ParameterRequestWrapper(httpServletRequest);
        String uri = httpServletRequest.getRequestURI();
        List<String> list = new ArrayList<>(16);
        for (String str : Arrays.asList(WHITE_PARAM_URIS)) {
            StringBuilder sb = new StringBuilder();
            sb.append(encryptProperties.getServerName()).append(str);
            list.add(sb.toString());
        }
        if (list.contains(uri)) {
            filterChain.doFilter(wrapper, servletResponse);
            return;
        }
        if (Boolean.parseBoolean(encryptProperties.getEnabled())) {
            String hexKey = httpServletRequest.getHeader(header);
            if (StringUtils.isNotEmpty(hexKey) && hexKey.startsWith(Constants.TOKEN_PREFIX)) {
                hexKey = hexKey.replace(Constants.TOKEN_PREFIX, "");
            } else {
                hexKey = SM4Util.DEFAULT_HEX_KEY;
            }
            Map<String, String[]> paramMap = wrapper.getParameterMap();
            if (paramMap != null && !paramMap.isEmpty()) {
                for (Map.Entry<String, String[]> me : paramMap.entrySet()) {
                    String[] values = me.getValue();
                    if (values != null && values.length > 0) {
                        if (values[0] != null && !"".equals(values[0].trim())) {
                            try {
                                wrapper.addParameter(me.getKey(), urlDecode(SM4Util.decrypt(values[0], KeyEncoder.get32CharsKey(hexKey))));
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        }
        filterChain.doFilter(wrapper, servletResponse);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
