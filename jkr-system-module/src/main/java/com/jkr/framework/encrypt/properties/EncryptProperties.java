package com.jkr.framework.encrypt.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "jkr.framework.encrypt")
public class EncryptProperties {
    /**
     * 项目路径
     */
    @Value(("${server.servlet.context-path}"))
    private String serverName;
    /**
     * 是否启用SM2加密、解密
     */
    @Value("${com.jkr.encrypt.enabled: false}")
    private String enabled;

}
