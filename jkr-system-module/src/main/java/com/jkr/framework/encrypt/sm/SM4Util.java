package com.jkr.framework.encrypt.sm;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.engines.SM4Engine;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.util.encoders.Hex;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * SM4 加解密的工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/6 9:00
 */
@Slf4j
public class SM4Util {

    // 块大小
    private static final int BLOCK_SIZE = 16;
    // 无 token 时默认秘钥
    public static final String DEFAULT_HEX_KEY = "44f7ac5ab2742014d611a2b969503a7b";

    /**
     * 加密
     *
     * <AUTHOR>
     * @date 2025年02月06日 16:48:20
     * @param plainText 待加密数据
     * @param hexKey 秘钥
     * @return java.lang.String
     */
    public static String encryptEcbPkcs5Padding(String plainText, String hexKey) throws Exception {
        byte[] keyBytes = Hex.decode(hexKey);
        byte[] input = plainText.getBytes(StandardCharsets.UTF_8);
        // 对输入数据进行 PKCS5Padding 填充
        byte[] paddedInput = pkcs5Padding(input, BLOCK_SIZE);
        // 加密模式
        SM4Engine sm4 = new SM4Engine();
        sm4.init(true, new KeyParameter(keyBytes));

        byte[] encrypted = new byte[paddedInput.length];
        for (int i = 0; i < paddedInput.length; i += BLOCK_SIZE) {
            sm4.processBlock(paddedInput, i, encrypted, i);
        }
        return Hex.toHexString(encrypted);
    }

    /**
     * PKCS5Padding 填充
     *
     * @param input      输入数据
     * @param blockSize  分组大小
     * @return 填充后的数据
     */
    private static byte[] pkcs5Padding(byte[] input, int blockSize) {
        int paddingLength = blockSize - (input.length % blockSize);
        byte[] padded = new byte[input.length + paddingLength];
        System.arraycopy(input, 0, padded, 0, input.length);
        for (int i = input.length; i < padded.length; i++) {
            padded[i] = (byte) paddingLength;
        }
        return padded;
    }

    /**
     * 解密
     *
     * <AUTHOR>
     * @date 2025年02月06日 16:48:37
     * @param cipherText 待解密数据
     * @param hexKey 秘钥
     * @return java.lang.String
     */
    public static String decrypt(String cipherText, String hexKey) throws Exception {
        byte[] keyBytes = Hex.decode(hexKey);
        byte[] encrypted = Hex.decode(cipherText);

        SM4Engine sm4 = new SM4Engine();
        // 解密模式
        sm4.init(false, new KeyParameter(keyBytes));

        byte[] decrypted = new byte[encrypted.length];
        for (int i = 0; i < encrypted.length; i += BLOCK_SIZE) {
            sm4.processBlock(encrypted, i, decrypted, i);
        }
        return new String(decrypted, StandardCharsets.UTF_8).trim();
    }

    // 随机生成 SM4 密钥
//    public static String generateKey() {
//        byte[] key = new byte[BLOCK_SIZE];
//        new SecureRandom().nextBytes(key);
//        return Hex.toHexString(key);
//    }
//
//
//    public static void main(String[] args) throws Exception {
//        log.info("加密后:{}", encryptEcbPkcs5Padding("你好！zifuchau^还", DEFAULT_HEX_KEY));
//        log.info("解密后:{}", decrypt(encryptEcbPkcs5Padding("你好！zifuchau^还", DEFAULT_HEX_KEY),DEFAULT_HEX_KEY));
//    }

}
