package com.jkr.framework.encrypt.util;

import com.jkr.common.constant.Constants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.encrypt.properties.EncryptProperties;
import com.jkr.framework.encrypt.sm.SM4Util;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

import static com.jkr.framework.encrypt.properties.EncryptWhiteList.WHITE_BODY_URIS;


/**
 * 解密request body
 *
 * <AUTHOR>
 * @date 2025年02月06日 09:05:23
 */
@Slf4j
@RestControllerAdvice
@EnableConfigurationProperties(EncryptProperties.class)
public class DecryptRequest extends RequestBodyAdviceAdapter {
    // 令牌自定义标识
    @Value("${token.header}")
    private String header;
    @Resource
    private EncryptProperties encryptProperties;


    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        String[] controllerUrl = null;
        String[] methodUrl = null;
        if (null != methodParameter.getContainingClass().getAnnotation(RequestMapping.class)) {
            controllerUrl = methodParameter.getContainingClass().getAnnotation(RequestMapping.class).value();
        }
        if (null != methodParameter.getMethodAnnotation(RequestMapping.class)) {
            methodUrl = methodParameter.getMethodAnnotation(RequestMapping.class).value();
        }
        List<String> list = Arrays.asList(WHITE_BODY_URIS);
        String url = (null != controllerUrl && controllerUrl.length > 0 ? controllerUrl[0] : "") + (null != methodUrl && methodUrl.length > 0 ? methodUrl[0] : "");

        return Boolean.parseBoolean(encryptProperties.getEnabled()) && !list.contains(url);
    }

    @Override
    public HttpInputMessage beforeBodyRead(final HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        String bodyStr = StreamUtils.copyToString(inputMessage.getBody(), Charset.defaultCharset());
        // 获取请求头中的token作为密钥，若无则启用默认秘钥值
        String hexKey = inputMessage.getHeaders().getFirst(header);
        if (StringUtils.isNotEmpty(hexKey) && hexKey.startsWith(Constants.TOKEN_PREFIX)) {
            hexKey = hexKey.replace(Constants.TOKEN_PREFIX, "");
        } else {
            hexKey = SM4Util.DEFAULT_HEX_KEY;
        }
        try {
            String decrypt = SM4Util.decrypt(bodyStr, KeyEncoder.get32CharsKey(hexKey));
            final ByteArrayInputStream bais = new ByteArrayInputStream(decrypt.getBytes(StandardCharsets.UTF_8));
            return new HttpInputMessage() {
                @Override
                public InputStream getBody() throws IOException {
                    return bais;
                }

                @Override
                public HttpHeaders getHeaders() {
                    return inputMessage.getHeaders();
                }
            };
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return super.beforeBodyRead(inputMessage, parameter, targetType, converterType);
    }
}
