package com.jkr.framework.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.framework.aspectj.lang.annotation.Sensitive;
import com.jkr.framework.aspectj.lang.enums.DesensitizedType;
import com.jkr.framework.security.LoginUser;

import java.io.IOException;
import java.util.Objects;

/**
 * 数据脱敏序列化过滤
 *
 * <AUTHOR>
 * @see com.jkr.framework.aspectj.DesensitizeAspect
 * @see com.jkr.framework.desensitize.annotation.ApiDesensitize
 * @see com.jkr.framework.desensitize.annotation.BeanDesensitize
 */
@Deprecated
public class SensitiveJsonSerializer extends JsonSerializer<String> implements ContextualSerializer {
	private DesensitizedType desensitizedType;

	@Override
	public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
		if (desensitization()) {
			gen.writeString(desensitizedType.desensitizer().apply(value));
		} else {
			gen.writeString(value);
		}
	}

	@Override
	public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property)
			throws JsonMappingException {
		Sensitive annotation = property.getAnnotation(Sensitive.class);
		if (Objects.nonNull(annotation) && Objects.equals(String.class, property.getType().getRawClass())) {
			this.desensitizedType = annotation.desensitizedType();
			return this;
		}
		return prov.findValueSerializer(property.getType(), property);
	}

	/**
	 * 是否需要脱敏处理
	 */
	private boolean desensitization() {
		try {
			LoginUser securityUser = SecurityUtils.getLoginUser();
			// 管理员不脱敏
			return !securityUser.getUser().isAdmin();
		} catch (Exception e) {
			return true;
		}
	}
}
