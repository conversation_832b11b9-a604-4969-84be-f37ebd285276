package com.jkr.framework.config;

import io.minio.MinioClient;
import io.minio.errors.MinioException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Minio配置
 *
 * <AUTHOR>
 * @since 2020/6/9 0009
 **/
@Slf4j
@Configuration
public class MinioConfig {

    @Value("${com.jkr.minio.endPoint}")
    private String endPoint;

    @Value("${com.jkr.minio.accessKey}")
    private String accessKey;

    @Value("${com.jkr.minio.secretKey}")
    private String secretKey;
    @Value("${com.jkr.minio.bucketName}")
    private String bucketName;

    /**
     * 初始化minioClient 初始化过程对存储桶名称进行验证
     *
     * <AUTHOR>
     * @date 2024年02月27日 13:59:57
     * @return io.minio.MinioClient
     */
    @Bean
    public MinioClient minioClient() throws Exception {
        MinioClient client = MinioClient.builder()
                .endpoint(endPoint)
                .credentials(accessKey,secretKey)
                .build();
        //存储桶名称验证
        if (StringUtils.isBlank(bucketName)) {
            log.error("存储桶名称缺失，请在yaml文件中进行完善！");
            throw new MinioException("存储桶名称缺失，请在yaml文件中进行完善！");
        }
        log.info("MINIO:地址={},存储桶名={}",endPoint,bucketName);
        return client;
    }
}
