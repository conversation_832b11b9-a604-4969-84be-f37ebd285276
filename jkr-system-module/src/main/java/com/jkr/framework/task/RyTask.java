package com.jkr.framework.task;

import com.jkr.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Slf4j
@Component("ryTask")
public class RyTask {
	public void ryMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i) {
		log.info(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
	}

	public void ryParams(String params) {
		log.info("执行有参方法：{}", params);
	}

	public void ryNoParams() {
		log.info("执行无参方法");
	}
}
