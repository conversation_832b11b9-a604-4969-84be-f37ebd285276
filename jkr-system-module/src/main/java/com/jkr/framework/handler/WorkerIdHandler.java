package com.jkr.framework.handler;

import com.jkr.common.exception.GlobalException;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * IdHandler
 *
 * <AUTHOR>
 * @since 5/8 星期四 11:32
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkerIdHandler {
    private final RedissonClient redissonClient;
    private final RedisTemplate<String, Object> redisTemplate;
    // worker id 过期时间 秒
    @Value("${yitter.ttl:86400}")
    private Long ttl;
    // 心跳时间 秒
    @Value("${yitter.heartbeat:86300}")
    private Long heartbeat;
    @Value("${server.servlet.context-path:jkr-api}")
    private String contextPath;
    @Value("${server.port:8080}")
    private Integer port;


    private static final String WORKER_MAC_PREFIX = "worker:mac:";
    // worker id 最大值
    private static final Integer WORKER_MAX_ID = 64;

    private String macAddress;
    @Getter
    private String workerId;
    private ScheduledExecutorService heartbeatExecutor;

    /**
     * 初始化方法。
     *
     * @PostConstruct 注解表示该方法在依赖注入完成后被自动调用。
     */
    @PostConstruct
    public void init() {
        // 检查心跳和数据过期时长，如果心跳时长小于过期时长，则抛出异常。
        if (heartbeat < ttl) {
            heartbeat = ttl > 10 ? (ttl - 10) : (ttl - 1);
        }
        this.macAddress = getMacAddress();
        allocateWorkerId();
        startHeartbeat();
    }

    /**
     * 在对象销毁之前执行的方法。
     * <p>
     * 如果存在心跳执行器（heartbeatExecutor），则立即停止它。
     *
     * @PreDestroy 注解表明该方法是一个生命周期方法，它将在对象销毁之前自动调用。
     */
    @PreDestroy
    public void destroy() {
        if (heartbeatExecutor != null) {
            heartbeatExecutor.shutdownNow();
        }
    }

    /**
     * 获取本机的MAC地址
     *
     * @return 返回本机的MAC地址，格式为"XX-XX-XX-XX-XX-XX"
     * @throws RuntimeException 如果获取MAC地址失败，则抛出运行时异常
     */
    private static String getMacAddress() {
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                if (ni.isLoopback() || !ni.isUp()) {
                    continue;
                }
                byte[] macBytes = ni.getHardwareAddress();
                if (macBytes != null && macBytes.length > 0) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < macBytes.length; i++) {
                        sb.append(String.format("%02x%s", macBytes[i], (i < macBytes.length - 1) ? ":" : ""));
                    }
                    return sb.toString();
                }
            }
            throw new GlobalException("未找到有效的MAC地址");
        } catch (Exception e) {
            log.error("获取mac地址失败", e);
            throw new GlobalException("获取mac地址失败");
        }
    }

    /**
     * 为指定的MAC地址分配一个workerId
     *
     * @return 为该MAC地址分配的workerId
     * @throws RuntimeException 如果分配workerId失败，则抛出RuntimeException异常
     */
    private void allocateWorkerId() {
        RLock lock = redissonClient.getLock("worker:alloc:lock");
        try {
            lock.lock(1, TimeUnit.SECONDS);
            // 检查是否已为mac分配了workerId
            Object existingId = redisTemplate.opsForValue().get(WORKER_MAC_PREFIX + contextPath.substring(1) + ":" + port + ":" + macAddress);
            if (existingId != null) {
                log.info("已分配workerId:{} for mac {}", existingId, macAddress);
                workerId = (String) existingId;
                return;
            }
            // 获取所有已分配的workerId
            Set<String> workerIdSet = new HashSet<>(8);
            Set<String> keys = redisTemplate.keys(WORKER_MAC_PREFIX + contextPath.substring(1) + ":" + port + ":*");
            if (!keys.isEmpty()) {
                for (String key : keys) {
                    log.info("key:{}", key);
                    Object id = redisTemplate.opsForValue().get(key);
                    if (id != null) {
                        log.info("已分配workerId:{}", id);
                        workerIdSet.add(id.toString());
                    }
                }
            }
            for (String s : workerIdSet) {
                log.info("已分配workerId:{}", s);
            }
            // 分配新的workerId
            for (int i = 1; i <= WORKER_MAX_ID; i++) {
                if (!workerIdSet.contains(i + "")) {
                    // 保存 mac：worker id 映射
                    workerId = i + "";
                    redisTemplate.opsForValue().set(WORKER_MAC_PREFIX + contextPath.substring(1) + ":" + port + ":" + macAddress, workerId, ttl, TimeUnit.SECONDS);
                    log.info("分配workerId:{} for mac {}", i, macAddress);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("分配workerId失败：{}", e.getMessage(), e);
            throw new GlobalException("分配workerId失败");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 开始心跳机制
     * <p>
     * 当 workerId 不为空时，启动一个定时任务，定期更新 workerId 和 mac 地址在 Redis 中的过期时间。
     * 如果更新过程中出现异常，会记录错误日志。
     */
    private void startHeartbeat() {
        if (workerId == null) {
            return;
        }
        heartbeatExecutor = Executors.newSingleThreadScheduledExecutor();
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            // 检查是否已为mac分配了workerId
            Object existingId = redisTemplate.opsForValue().get(WORKER_MAC_PREFIX + contextPath.substring(1) + ":" + port + ":" + macAddress);
            if (existingId == null) {
                log.error("未找到mac：{}的worker id", macAddress);
                throw new GlobalException("未找到mac：{}的worker id" + macAddress);
            }
            // 续期 mac：worker id 映射
            redisTemplate.expire(WORKER_MAC_PREFIX + contextPath.substring(1) + ":" + port + ":" + macAddress, ttl, TimeUnit.SECONDS);
            log.info("续期 workerId：{} for mac：{}", workerId, macAddress);

        }, heartbeat, heartbeat, TimeUnit.SECONDS);
    }

}
