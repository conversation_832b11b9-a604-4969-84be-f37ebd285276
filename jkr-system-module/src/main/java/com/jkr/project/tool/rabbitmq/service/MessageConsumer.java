package com.jkr.project.tool.rabbitmq.service;

import com.jkr.project.tool.rabbitmq.config.RabbitMQConfig;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

/**
 * 消息消费者
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/5 14:33
 */
@Service
public class MessageConsumer {

//    @RabbitListener(queues = RabbitMQConfig.QUEUE_NAME)
    public void receiveMessage(String message) {
        System.out.println("Received message: " + message);
    }
}