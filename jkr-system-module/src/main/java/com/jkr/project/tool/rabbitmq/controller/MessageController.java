package com.jkr.project.tool.rabbitmq.controller;

import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.tool.rabbitmq.service.MessageProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.jkr.framework.web.domain.AjaxResult.success;

/**
 * MessageController
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/5 14:39
 */
@RestController
public class MessageController {

    @Autowired
    private MessageProducer messageProducer;

    @PostMapping("/send/{message}")
    public AjaxResult sendMessage(@PathVariable String message) {
        messageProducer.sendMessage(message);
        return success("Message sent: " + message);
    }



}