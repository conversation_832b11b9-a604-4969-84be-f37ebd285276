package com.jkr.project.tool.sse.listener;

import com.alibaba.fastjson2.JSON;
import com.jkr.project.tool.sse.servicce.SseService;
import com.jkr.project.tool.sse.vo.MessageVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Service;

/**
 * RedisMessageListener Redis消息监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-16 13:37
 */
@Slf4j
@Service
public class RedisMessageListener implements MessageListener {
	@Resource
	private SseService sseService;

	@Override
	public void onMessage(Message message, byte[] pattern) {
		String payload = new String(message.getBody());
		try {
			MessageVo vo = JSON.parseObject(payload, MessageVo.class);
			String clientId = vo.getClientId();
			if (clientId != null && !clientId.trim().isEmpty()) {
				sseService.sendEvent(clientId, vo);
			} else {
				sseService.sendEvent(vo);
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
}
