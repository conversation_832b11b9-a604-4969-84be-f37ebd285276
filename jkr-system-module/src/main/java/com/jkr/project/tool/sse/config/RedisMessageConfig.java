package com.jkr.project.tool.sse.config;

import com.jkr.project.tool.sse.listener.RedisMessageListener;
import com.jkr.project.tool.sse.servicce.SseService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import java.util.concurrent.Executors;

/**
 * RedisMessageConfig
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-16 13:44
 */
//@Configuration zwd 20250509 启动报错，暂时删除此注解
public class RedisMessageConfig {

	@Bean
	public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory connectionFactory) {
		RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setSubscriptionExecutor(Executors.newFixedThreadPool(4));
        container.setMaxSubscriptionRegistrationWaitingTime(120000); // 120秒超时
        container.addMessageListener(redisMessageListener(), new ChannelTopic(SseService.CHANNEL_NAME));
        container.setErrorHandler(e -> {
            System.err.println("Redis消息监听错误: " + e.getMessage());
            e.printStackTrace();
        });
		return container;
	}

	@Bean
	public RedisMessageListener redisMessageListener() {
		return new RedisMessageListener();
	}
}
