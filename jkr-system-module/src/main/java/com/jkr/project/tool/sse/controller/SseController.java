package com.jkr.project.tool.sse.controller;

import com.jkr.framework.web.controller.BaseController;
import com.jkr.project.tool.sse.servicce.SseService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE 控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-15 15:18
 */
@RestController
public class SseController extends BaseController {
	@Resource
	private SseService sseService;

	/**
	 * 创建SseEmitter链接
	 *
	 * @param userId 用户id
	 * @return SseEmitter实例
	 */
	@GetMapping(value = "/sse/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
	public SseEmitter connect(@RequestParam String userId, HttpServletResponse response) {
		// 响应头设置
		response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
//		response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
		response.setHeader(HttpHeaders.CONTENT_TYPE, "text/event-stream");
		response.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache");
		response.setHeader(HttpHeaders.CONNECTION, "keep-alive");

		return sseService.connect(userId);
	}
}
