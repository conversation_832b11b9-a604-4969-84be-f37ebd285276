package com.jkr.project.tool.rabbitmq.config;


import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQConfig
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/5 14:33
 */
@Configuration
public class RabbitMQConfig {

    public static final String QUEUE_NAME = "myQueue";

    @Bean
    public Queue myQueue() {
        // true表示持久化
        return new Queue(QUEUE_NAME, true);
    }
}