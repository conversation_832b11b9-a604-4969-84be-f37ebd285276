package com.jkr.project.tool.sse.servicce;

import com.alibaba.fastjson2.JSON;
import com.jkr.common.constant.HttpStatus;
import com.jkr.project.system.mapper.SysNoticeMapper;
import com.jkr.project.tool.sse.vo.MessageVo;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
/**
 * 建议使用send(SseEventBuilder builder)推送消息
 * 在构造SseEventBuilder时，如无特殊需求，请不要调用name(String eventName)方法
 * 一旦调用了name方法，前端必须添加eventName的监听，否则浏览器接收不到消息
 * {@code
 * // java
 * SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
 * emitter.send(SseEmitter.event()
 * .id(String.valueOf(HttpStatus.SUCCESS))
 * .name("wahaha")
 * .data(sseMessage, MediaType.APPLICATION_JSON));
 * }
 * // js
 * eventSource.addEventListener("wahaha", (event) => {
 * const data = JSON.parse(event.data);
 * console.log("wahaha收到消息内容是:", data);
 * });
 * 参考
 * https://blog.csdn.net/Leisurelyc/article/details/127907419
 */

/**
 * SseService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-15 16:41
 */
@Slf4j
@Service
public class SseService {
	private final Map<String, SseEmitter> emitterMap = new ConcurrentHashMap<>();
	public static final String CHANNEL_NAME = "sse-channel";
	@Resource
	private RedisTemplate<String, String> redisTemplate;
	@Resource
	private SysNoticeMapper sysNoticeMapper;

	/**
	 * 创建客户端
	 *
	 * @param clientId 客户端ID
	 * @return SseEmitter实例
	 */
	public SseEmitter connect(String clientId) {
		SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);

		emitter.onCompletion(() -> deleteSseEmitter(clientId));
		emitter.onTimeout(() -> deleteSseEmitter(clientId));
		emitter.onError((e) -> {
			deleteSseEmitter(clientId);
			log.error(e.getMessage(), e);
		});

		emitterMap.put(clientId, emitter);
		log.info("【{}】上线，创建SseEmitter链接", clientId);
		// TODO 演示代码， 用户上线后推送消息，如无此需求，请注释或删除此处代码；或请根据实际需要处理 zwd 20250120
		MessageVo vo = new MessageVo();
		vo.setClientId(clientId);
		vo.setName("notice");
		vo.setData(sysNoticeMapper.selectCount() + "");
		publish(vo);
		return emitter;
	}

	/**
	 * 清理SseEmitter
	 *
	 * @param clientId 客户端ID
	 */
	public void deleteSseEmitter(String clientId) {
		try {
			log.error("【{}】被移除", clientId);
			emitterMap.remove(clientId);
			redisTemplate.getConnectionFactory().getConnection().close();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 推送消息
	 *
	 * @param messageVo 消息vo实例
	 */
	public void sendEvent(MessageVo messageVo) {
		for (Map.Entry<String, SseEmitter> me : emitterMap.entrySet()) {
			try {
				me.getValue().send(SseEmitter.event()
						.id(String.valueOf(HttpStatus.SUCCESS))
						.data(messageVo, MediaType.APPLICATION_JSON));
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				me.getValue().complete();
				deleteSseEmitter(me.getKey());
			}
		}
	}

	/**
	 * SseEmitter群发消息
	 *
	 * @param message 消息
	 */
	public void sendEvent(String message) {
		for (Map.Entry<String, SseEmitter> me : emitterMap.entrySet()) {
			try {
				me.getValue().send(SseEmitter.event()
						.id(String.valueOf(HttpStatus.SUCCESS))
						.data(message));
			} catch (Exception e) {
				log.error(e.getMessage(), e);
				me.getValue().complete();
				deleteSseEmitter(me.getKey());
			}
		}
	}

	/**
	 * SseEmitter向指定客户端推送消息
	 *
	 * @param clientId 客户ID
	 * @param message  消息vo实例
	 */
	public void sendEvent(String clientId, MessageVo message) {
		SseEmitter emitter = emitterMap.get(clientId);
		if (emitter == null) {
			emitterMap.remove(clientId);
			log.error("未找到【{}】对应的客户端连接", clientId);
			return;
		}
		try {
			emitter.send(SseEmitter.event()
					.id(String.valueOf(HttpStatus.SUCCESS))
					.data(message, MediaType.APPLICATION_JSON));
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			emitter.complete();
		}
	}

	/**
	 * 初始化Redis监听器。
	 * <p>
	 * 在Spring容器创建bean之后，通过@PostConstruct注解的方法会自动被调用。
	 * 此方法用于订阅Redis的特定频道，以便接收消息。
	 */
	@PostConstruct
	public void redisListener() {
		try {
			Objects.requireNonNull(redisTemplate.getConnectionFactory()).getConnection().subscribe((message, pattern) -> {
				try {
					// 此处与RedisMessageListener.onMessage 有某种关系，代码逻辑是一样的
					String msg = new String(message.getBody(), StandardCharsets.UTF_8);
					MessageVo vo = JSON.parseObject(msg, MessageVo.class);
					String clientId = vo.getClientId();
					if (clientId != null && !clientId.trim().isEmpty()) {
						sendEvent(clientId, vo);
					} else {
						sendEvent(vo);
					}
				} catch (Exception e) {
					log.error("处理Redis消息时发生错误: {}", e.getMessage(), e);
				}
			}, CHANNEL_NAME.getBytes(StandardCharsets.UTF_8));
		} catch (Exception e) {
			log.error("订阅Redis频道时发生错误: {}", e.getMessage(), e);
		}
	}

	/**
	 * 向指定的Redis频道发布消息。
	 *
	 * @param message 要发布的消息内容。
	 *                <p>
	 *                该方法使用RedisTemplate将消息转换并发送到名为"sse-channel"的Redis频道。
	 *                消息将被广播给所有订阅了该频道的客户端。
	 */
	public void publish(MessageVo message) {
		redisTemplate.convertAndSend(CHANNEL_NAME, message);
	}

}
