package com.jkr.project.tool.sse.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * MessageVo
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-15 17:14
 */
@Data
//@JsonInclude
public class MessageVo {
	private String clientId;
	/**
	 * 消息类型，主要用来区分消息
	 */
	private String name;
	/**
	 * 消息
	 */
	private String data;

	public String getPushTime() {
		LocalDateTime dateTime = LocalDateTime.now();
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return formatter.format(dateTime);
	}

	public MessageVo() {
	}

//	@JsonCreator
//	public MessageVo(
//			@JsonProperty("clientId") String clientId,
//			@JsonProperty("name") String name,
//			@JsonProperty("data") String data) {
//		this.clientId = clientId;
//		this.name = name;
//		this.data = data;
//	}
}
