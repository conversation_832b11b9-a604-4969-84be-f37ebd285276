package com.jkr.project.monitor.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jkr.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 管理后台 - 文档目录 Request DO
 *
 * <AUTHOR>
 */
@TableName("sys_document_dir")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDocumentDir extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /**
     * 目录ID
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dirId;

    /**
     * 父级目录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 目录名称
     */
    private String dirName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 目录状态:0正常,1停用
     */
    private String status;


    /**
     * 目录类型：1：接口文档；2：帮助文档
     */
    private String dirType;

    /**
     * 删除标志（1代表存在 时间戳代表删除）
     */
    private String delFlag;

    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 父级目录名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 子目录
     */
    @TableField(exist = false)
    private List<SysDocumentDir> children = new ArrayList<SysDocumentDir>();
}
