package com.jkr.project.monitor.controller;

import cn.hutool.core.util.StrUtil;
import com.github.yitter.idgen.YitIdHelper;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.monitor.service.SysDocumentDirService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理后台 - 文档目录管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/documentdir")
@Validated
public class SysDocumentDirController extends BaseController {
    @Resource
    private SysDocumentDirService sysDocumentDirService;

    /**
     * @param param
     * @return CommonResult<List < SysDocumentDir>>
     * @title list
     * @Description: 获得文档目录
     * <AUTHOR>
     * @date 2025/2/18 16:43
     */
    /*@PreAuthorize("@ss.hasPermission('sys:documentdir:query')")*/
    @GetMapping("/list")
    public AjaxResult list(SysDocumentDir param) {
        List<SysDocumentDir> list = sysDocumentDirService.selectSysDocumentDirList(param);
        return success(list);
    }

    /**
     * @param dirId
     * @return CommonResult<List < SysDocumentDir>>
     * @title excludeChild
     * @Description: 查询非指定ID的目录
     * <AUTHOR>
     * @date 2025/2/18 16:43
     */
    /*@PreAuthorize("@ss.hasPermission('sys:documentdir:query')")*/
    @PostMapping("/list/exclude/{dirId}")
    public AjaxResult excludeChild(@PathVariable(value = "dirId", required = false) Long dirId) {
        List<SysDocumentDir> list = sysDocumentDirService.selectSysDocumentDirList(new SysDocumentDir());
        list.removeIf(d -> d.getDirId().intValue() == dirId ||
                ArrayUtils.contains(StrUtil.splitToArray(d.getAncestors(), ","), dirId + ""));
        return success(list);
    }

    /**
     * @param dirId
     * @return CommonResult<SysDocumentDir>
     * @title getInfo
     * @Description: 根据目录ID获取目录信息
     * <AUTHOR>
     * @date 2025/2/18 16:44
     */
    /*@PreAuthorize("@ss.hasPermission('sys:documentdir:query')")*/
    @PostMapping(value = "/info/{dirId}")
    public AjaxResult getInfo(@PathVariable("dirId") Long dirId) {
        return success(sysDocumentDirService.selectSysDocumentDirById(dirId));
    }

    /**
     * @param documentDir
     * @return CommonResult<Integer>
     * @title create
     * @Description: 创建目录
     * <AUTHOR>
     * @date 2025/2/18 16:44
     */
    @Log(title = "文档目录管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SysDocumentDir documentDir) {
        boolean documentDirExist = sysDocumentDirService.checkDirNameUnique(documentDir);
        if (!documentDirExist) {
            return error("新增目录'" + documentDir.getDirName() + "'失败，目录名称已存在");
        }
        documentDir.setDirId(YitIdHelper.nextId());
        return success(sysDocumentDirService.createDocumentDir(documentDir));
    }

    /**
     * @param documentDir
     * @return CommonResult<Boolean>
     * @title edit
     * @Description: 修改目录
     * <AUTHOR>
     * @date 2025/2/18 16:44
     */
    @Log(title = "文档目录管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@Validated @RequestBody SysDocumentDir documentDir) {
        Long dirId = documentDir.getDirId();
        boolean documentDirExist = sysDocumentDirService.checkDirNameUnique(documentDir);
        if (!documentDirExist) {
            return error("修改目录'" + documentDir.getDirName() + "'失败，目录名称已存在");
        } else if (documentDir.getParentId().equals(dirId)) {
            return error("修改目录'" + documentDir.getDirName() + "'失败，上级目录不能是自己");
        }
        int res = sysDocumentDirService.updateDocumentDir(documentDir);

        return success(res > 0);
    }

    /**
     * @param dirId
     * @return CommonResult<Boolean>
     * @title remove
     * @Description: 根据目录ID删除目录信息
     * <AUTHOR>
     * @date 2025/2/18 16:45
     */
    @PostMapping("/remove/{dirId}")
    public AjaxResult remove(@PathVariable("dirId") Long dirId) {
        if (sysDocumentDirService.hasChildByDirId(dirId) > 0) {
            return error("存在下级目录,不允许删除");
        }
        int res = sysDocumentDirService.deleteDirById(dirId);
        return success(res > 0);
    }


}
