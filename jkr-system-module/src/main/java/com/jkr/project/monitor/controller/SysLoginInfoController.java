package com.jkr.project.monitor.controller;

import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.security.service.SysPasswordService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.monitor.domain.SysLoginInfo;
import com.jkr.project.monitor.service.ISysLoginInfoService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLoginInfoController extends BaseController {
	@Autowired
	private ISysLoginInfoService logininforService;

	@Autowired
	private SysPasswordService passwordService;

	@PreAuthorize("@ss.hasPermi('monitor:logininfor:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysLoginInfo logininfor) {
		startPage();
		List<SysLoginInfo> list = logininforService.selectLogininforList(logininfor);
		return getDataTable(list);
	}

	@Log(title = "登录日志", businessType = BusinessType.EXPORT)
	@PreAuthorize("@ss.hasPermi('monitor:logininfor:export')")
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysLoginInfo logininfor) {
		List<SysLoginInfo> list = logininforService.selectLogininforList(logininfor);
		ExcelUtil<SysLoginInfo> util = new ExcelUtil<SysLoginInfo>(SysLoginInfo.class);
		util.exportExcel(response, list, "登录日志");
	}

	@PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
	@Log(title = "登录日志", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{infoIds}")
	public AjaxResult remove(@PathVariable Long[] infoIds) {
		return toAjax(logininforService.deleteLogininforByIds(infoIds));
	}

	@PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
	@Log(title = "登录日志", businessType = BusinessType.CLEAN)
	@PostMapping("/clean")
	public AjaxResult clean() {
		logininforService.cleanLogininfor();
		return success();
	}

	@PreAuthorize("@ss.hasPermi('monitor:logininfor:unlock')")
	@Log(title = "账户解锁", businessType = BusinessType.OTHER)
	@GetMapping("/unlock/{userName}")
	public AjaxResult unlock(@PathVariable("userName") String userName) {
		passwordService.clearLoginRecordCache(userName);
		return success();
	}
}
