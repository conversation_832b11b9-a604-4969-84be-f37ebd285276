package com.jkr.project.monitor.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jkr.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文档内容
 *
 * <AUTHOR>
 * @date 2024-12-19
 */

/**
 * 管理后台 - 文档内容 Request DO
 *
 * <AUTHOR>
 */
@TableName("sys_document_md")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDocumentMd extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 文档主键
     */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long mdId;
    /**
     * 文档目录主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dirId;
    /**
     * 文档内容(base64编码的)
     */
    private String render;
    /**
     * 文档内容
     */
    private String value;

    /** 状态:0正常,1停用 */
    private String status;
    /**
     * 文档标题
     */
    private String title;
    /**
     * 删除标志（1代表存在 时间戳代表删除）
     */
    private String delFlag;

    /** 租户编号 */
    private Long tenantId;
    /**
     * 目录名称
     */
    @TableField(exist = false)
    private String dirName;
    /**
     * 目录类型
     */
    @TableField(exist = false)
    private String dirType;


}
