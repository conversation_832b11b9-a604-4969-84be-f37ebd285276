package com.jkr.project.monitor.service;

import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.monitor.domain.SysDocumentMd;

import java.util.List;

/**
 * DocumentMdService接口定义了文档元数据服务的API
 * 它提供了对文档元数据进行查询、创建、更新和删除操作的方法
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface SysDocumentMdService {

    /**
     * 查询文档元数据列表
     * 此方法用于根据给定的参数查询符合条件的文档元数据记录
     *
     * @param param 包含查询条件的SysDocumentMd对象
     * @return 返回一个SysDocumentMd对象列表，包含查询到的文档元数据记录
     */
    List<SysDocumentMd> selectCbFileMdList(SysDocumentMd param);

    /**
     * 获取文档Markdown格式的分页数据
     *
     * @param param 文档Markdown分页查询请求对象，包含分页参数和查询条件
     * @return 返回一个PageResult对象，其中包含符合查询条件的文档Markdown格式数据
     */
    List<SysDocumentMd> getDocumentMdList(SysDocumentMd param);

    /**
     * @title selectSysDocumentMdAndDirList
     * @Description: 自定义查询，增加目录名称
     * @param queryParam
     * @return java.util.List<com.jkr.project.monitor.domain.SysDocumentMd>
     * <AUTHOR>
     * @date 2025/2/19 14:51
     */
    List<SysDocumentMd> selectSysDocumentMdAndDirList(SysDocumentMd queryParam);



    /**
     * 检查文档元数据在目录中是否存在
     * 此方法用于验证指定的文档元数据是否在目录中已存在
     *
     * @param param 包含检查条件的SysDocumentMd对象
     * @return 返回一个SysDocumentMd对象列表，包含查找到的文档元数据记录
     */
    List<SysDocumentMd> checkCbFileMdIsExistInDir(SysDocumentMd param);

    /**
     * 创建新的文档元数据记录
     * 此方法用于在数据库中添加一条新的文档元数据记录
     *
     * @param param 包含要创建的文档元数据信息的SysDocumentMd对象
     * @return 返回受影响的行数，通常为1表示成功，0表示失败
     */
    int createDocumentMd(SysDocumentMd param);

    /**
     * 根据文档id查询文档信息
     * @param mdId 文档ID
     * @return 文档信息对象
     */
    SysDocumentMd selectSysDocumentMdById(Long mdId);


    /**
     * 更新文档元数据记录
     * 此方法用于更新数据库中已存在的文档元数据记录
     *
     * @param param 包含更新后的文档元数据信息的SysDocumentMd对象
     * @return 返回受影响的行数，通常为1表示成功，0表示失败
     */
    int updateDocumentMd(SysDocumentMd param);

    /**
     * 删除文档元数据记录
     * 此方法用于从数据库中删除指定的文档元数据记录
     *
     * @param param 包含要删除的文档元数据信息的SysDocumentMd对象
     * @return 返回受影响的行数，通常为1表示成功，0表示失败
     */
    int deleteDocumentMd(SysDocumentMd param);
}
