package com.jkr.project.monitor.controller;

import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.monitor.domain.SysJobLog;
import com.jkr.project.monitor.service.ISysJobLogService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调度日志操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/jobLog")
public class SysJobLogController extends BaseController {
	@Autowired
	private ISysJobLogService jobLogService;

	/**
	 * 查询定时任务调度日志列表
	 */
	@PreAuthorize("@ss.hasPermi('monitor:job:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysJobLog sysJobLog) {
		startPage();
		List<SysJobLog> list = jobLogService.selectJobLogList(sysJobLog);
		return getDataTable(list);
	}

	/**
	 * 导出定时任务调度日志列表
	 */
	@PreAuthorize("@ss.hasPermi('monitor:job:export')")
	@Log(title = "任务调度日志", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysJobLog sysJobLog) {
		List<SysJobLog> list = jobLogService.selectJobLogList(sysJobLog);
		ExcelUtil<SysJobLog> util = new ExcelUtil<SysJobLog>(SysJobLog.class);
		util.exportExcel(response, list, "调度日志");
	}

	/**
	 * 根据调度编号获取详细信息
	 */
	@PreAuthorize("@ss.hasPermi('monitor:job:query')")
	@GetMapping(value = "/{jobLogId}")
	public AjaxResult getInfo(@PathVariable Long jobLogId) {
		return success(jobLogService.selectJobLogById(jobLogId));
	}


	/**
	 * 删除定时任务调度日志
	 */
	@PreAuthorize("@ss.hasPermi('monitor:job:remove')")
	@Log(title = "定时任务调度日志", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{jobLogIds}")
	public AjaxResult remove(@PathVariable Long[] jobLogIds) {
		return toAjax(jobLogService.deleteJobLogByIds(jobLogIds));
	}

	/**
	 * 清空定时任务调度日志
	 */
	@PreAuthorize("@ss.hasPermi('monitor:job:remove')")
	@Log(title = "调度日志", businessType = BusinessType.CLEAN)
	@PostMapping("/clean")
	public AjaxResult clean() {
		jobLogService.cleanJobLog();
		return success();
	}
}
