package com.jkr.project.monitor.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.redis.RedisCache;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.monitor.domain.SysUserOnline;
import com.jkr.project.system.service.ISysUserOnlineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线用户监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/online")
public class SysUserOnlineController extends BaseController {
	@Autowired
	private ISysUserOnlineService userOnlineService;

	@Autowired
	private RedisCache redisCache;

	@PreAuthorize("@ss.hasPermi('monitor:online:list')")
	@GetMapping("/list")
	public TableDataInfo list(String ipaddr, String userName) {
		Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
		List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
		for (String key : keys) {
			String JSONStr = JSON.toJSONString(redisCache.getCacheObject(key));
			LoginUser user = JSONObject.parseObject(JSONStr, LoginUser.class);
			//LoginUser user =JSONObject.parseObject(redisCache.getCacheObject(key), LoginUser.class);
			if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName)) {
				userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
			} else if (StringUtils.isNotEmpty(ipaddr)) {
				userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user));
			} else if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser())) {
				userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
			} else {
				userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
			}
		}
		Collections.reverse(userOnlineList);
		userOnlineList.removeAll(Collections.singleton(null));
		//按登录时间倒序排序
		return getDataTable(userOnlineList.stream().sorted(Comparator.comparing(SysUserOnline::getLoginTime).reversed()).collect(Collectors.toList()));
	}

	/**
	 * 强退用户
	 */
	@PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
	@Log(title = "在线用户", businessType = BusinessType.FORCE)
	@PostMapping("/forceLogout/{tokenId}")
	public AjaxResult forceLogout(@PathVariable String tokenId) {
		redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
		return success();
	}
}
