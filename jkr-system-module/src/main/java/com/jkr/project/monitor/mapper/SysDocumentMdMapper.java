package com.jkr.project.monitor.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jkr.framework.mybatis.query.LambdaQueryWrapperX;
import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.monitor.domain.SysDocumentMd;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档Markdown映射器接口，继承自BaseMapperX，用于定义针对DocumentMdDO实体的数据库操作方法
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface SysDocumentMdMapper extends BaseMapper<SysDocumentMd> {

    /**
     * 选择Markdown格式文档列表
     * 此方法根据查询参数筛选出符合条件的文档列表，主要关注未删除、状态为"0"的文档
     * 可以根据mdId、标题、目录类型等进行精确或模糊查询，并支持复杂的目录ID查询逻辑
     *
     * @param queryParam 查询参数，包含可选的mdId、标题、目录类型、目录ID等筛选条件
     * @return 返回符合条件的DocumentMdDO实体列表
     */
//    default List<SysDocumentMd> selectDocumentMdList(SysDocumentMd queryParam) {
//        // 构建查询条件，基础条件为未删除且状态为"0"
//        LambdaQueryWrapperX<SysDocumentMd> wrapper = new LambdaQueryWrapperX<SysDocumentMd>()
//                .eq(SysDocumentMd::getDelFlag, "1")
//                .eq(SysDocumentMd::getStatus, "0")
//                .eqIfPresent(SysDocumentMd::getMdId, queryParam.getMdId())
//                .likeIfPresent(SysDocumentMd::getTitle, queryParam.getTitle())
//                .eqIfPresent(SysDocumentMd::getDirType, queryParam.getDirType());
//        // 动态条件：如果目录ID存在且不为0，则添加目录ID相关查询条件
//        if (queryParam.getDirId() != null && queryParam.getDirId() != 0) {
//            wrapper.and(w -> w.eq(SysDocumentMd::getDirId, queryParam.getDirId())
//                    .or()
//                    .apply("u.dir_id IN (SELECT t.dir_id FROM infra_document_dir t WHERE FIND_IN_SET({0}, t.ancestors))",
//                            queryParam.getDirId()));
//        }
//        // 排序：先按目录类型升序，再按mdId升序
//        wrapper.orderByAsc(SysDocumentMd::getDirType)
//                .orderByAsc(SysDocumentMd::getMdId);
//        // 执行查询并返回结果
//        return selectList(wrapper);
//    }

    /**
     * 选择Markdown格式文档列表
     * 此方法根据查询参数筛选出符合条件的文档列表，主要关注未删除、状态为"0"的文档
     * 可以根据mdId、标题、目录类型等进行精确或模糊查询，并支持复杂的目录ID查询逻辑
     *
     * @title selectSysDocumentMdList
     * @param queryParam 查询参数，包含可选的mdId、标题、目录类型、目录ID等筛选条件
     * @return java.util.List<com.jkr.project.monitor.domain.SysDocumentMd>
     * <AUTHOR>
     * @date 2025/2/19 14:23
     */
    List<SysDocumentMd> selectSysDocumentMdList(SysDocumentMd queryParam);

    /**
     * @title selectSysDocumentMdAndDirList
     * @Description: 自定义查询，增加目录名称
     * @param queryParam
     * @return java.util.List<com.jkr.project.monitor.domain.SysDocumentMd>
     * <AUTHOR>
     * @date 2025/2/19 14:51
     */
    List<SysDocumentMd> selectSysDocumentMdAndDirList(SysDocumentMd queryParam);



    /**
     * 根据文档id查询文档信息
     * @param mdId 文档ID
     * @return 文档信息对象
     */
    SysDocumentMd selectSysDocumentMdById(@Param("mdId") Long mdId);
    /**
     * 检查Markdown格式文档在指定目录中是否存在
     * 此方法用于确认在指定目录中是否存在未删除、状态为"0"的文档，主要用于文档存在性校验
     *
     * @param param 包含目录ID的查询参数
     * @return 返回查询到的DocumentMdDO实体列表，如果列表为空，表示没有找到符合条件的文档
     */
    default List<SysDocumentMd> checkDocumentMdIsExistInDir(SysDocumentMd param) {
        // 构建查询条件，主要根据目录ID、未删除和状态为"0"进行查询
        return selectList(new LambdaQueryWrapper<SysDocumentMd>()
                .eq(SysDocumentMd::getDirId, param.getDirId())
                .eq(SysDocumentMd::getDelFlag, "1")
                .eq(SysDocumentMd::getStatus, "0"));
    }


}
