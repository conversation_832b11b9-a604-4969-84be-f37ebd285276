package com.jkr.project.monitor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.jkr.common.constant.UserConstants;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.StringUtils;
import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.monitor.mapper.SysDocumentDirMapper;
import com.jkr.project.monitor.service.SysDocumentDirService;
import com.jkr.project.system.domain.SysMenu;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文档目录管理的操作定义实现类
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class SysDocumentDirServiceImpl implements SysDocumentDirService {

    @Autowired
    private SysDocumentDirMapper sysDocumentDirMapper;

    /**
     * 根据目录ID检查是否存在子目录
     * @param dirId 目录ID
     * @return 存在子目录则返回1，否则返回0
     */
    @Override
    public int hasChildByDirId(Long dirId) {
        return sysDocumentDirMapper.hasChildByDirId(dirId);
    }

    /**
     * 查询文档目录列表
     * @param param 查询参数
     * @return 文档目录列表
     */
    @Override
    public List<SysDocumentDir> selectSysDocumentDirList(SysDocumentDir param) {
        return sysDocumentDirMapper.selectSysDocumentDirList(param);
    }

    /**
     * 根据目录ID查询目录信息
     * @param dirId 目录ID
     * @return 目录信息
     */
    @Override
    public SysDocumentDir selectSysDocumentDirById(Long dirId) {
        return sysDocumentDirMapper.selectSysDocumentDirById(dirId);
    }

    /**
     * 根据目录ID查询子目录列表
     * @param dirId 目录ID
     * @return 子目录列表
     */
    @Override
    public List<SysDocumentDir> selectChildrenDirById(Long dirId) {
        return sysDocumentDirMapper.selectChildrenDirById(dirId);
    }

    /**
     * 检查目录名称是否唯一
     * @param sysDocumentDir 目录名称
     * @return 如果目录名称唯一则返回null，否则返回重复的目录信息
     */
    @Override
    public boolean checkDirNameUnique(SysDocumentDir sysDocumentDir) {
        SysDocumentDir info = sysDocumentDirMapper.checkDirNameUnique(sysDocumentDir);
        if (StringUtils.isNotNull(info)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }


    /**
     * 创建文档目录
     * @param param 目录参数
     * @return 插入结果
     * @throws ServiceException 如果父目录状态不为正常，则抛出服务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createDocumentDir(SysDocumentDir param) {
        SysDocumentDir info = sysDocumentDirMapper.selectSysDocumentDirById(param.getParentId());
        if (null == info && param.getParentId() != 0) {
            throw new ServiceException("父节点不存在，请重新选择");
        }
        // 如果父节点不为正常状态,则不允许新增子节点
        if (param.getParentId() != 0 && !"0".equals(info.getStatus())) {
            throw new ServiceException("目录停用，不允许新增");
        }
        if (param.getParentId() == 0){
            param.setAncestors(param.getParentId().toString());
        }else{
            param.setAncestors(info.getAncestors() + "," + param.getParentId());
        }
        return sysDocumentDirMapper.insert(param);
    }

    /**
     * 更新文档目录
     * @param param 目录参数
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDocumentDir(SysDocumentDir param) {
        SysDocumentDir newParentDir = sysDocumentDirMapper.selectSysDocumentDirById(param.getParentId());
        SysDocumentDir oldDir = sysDocumentDirMapper.selectSysDocumentDirById(param.getDirId());
        if (ObjUtil.isNotNull(newParentDir) && ObjUtil.isNotNull(oldDir)) {
            String newAncestors = newParentDir.getAncestors() + "," + newParentDir.getDirId();
            String oldAncestors = oldDir.getAncestors();
            param.setAncestors(newAncestors);
            updateDirChildren(param.getDirId(), newAncestors, oldAncestors);
        }
        if(0 == param.getParentId()){
            String newAncestors = "0";
            String oldAncestors = oldDir.getAncestors();
            param.setAncestors(newAncestors);
            updateDirChildren(param.getDirId(), newAncestors, oldAncestors);
        }
        return sysDocumentDirMapper.updateById(param);
    }

    /**
     * 更新目录的子目录祖先信息
     * @param dirId 目录ID
     * @param newAncestors 新的祖先信息
     * @param oldAncestors 旧的祖先信息
     */
    public void updateDirChildren(Long dirId, String newAncestors, String oldAncestors) {
        List<SysDocumentDir> children = sysDocumentDirMapper.selectChildrenDirById(dirId);
        for (SysDocumentDir child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (CollUtil.isNotEmpty(children)) {
            sysDocumentDirMapper.updateDirChildren(children);
        }
    }

    /**
     * 批量更新目录的子目录祖先信息
     * @param dirs 目录列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDirChildren(List<SysDocumentDir> dirs) {
        sysDocumentDirMapper.updateDirChildren(dirs);
    }

    /**
     * 批量更新目录状态为正常
     * @param dirIds 目录ID数组
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDirStatusNormal(Long[] dirIds) {
        return sysDocumentDirMapper.updateDirStatusNormal(dirIds);
    }

    /**
     * 根据目录ID删除目录
     * @param dirId 目录ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDirById(Long dirId) {
        return sysDocumentDirMapper.deleteById(dirId);
    }


    /**
     * 构建目录树
     * @param cbFileDirList 目录列表
     * @return 目录树列表
     */
    public List<SysDocumentDir> buildDirTree(List<SysDocumentDir> cbFileDirList) {
        List<SysDocumentDir> returnList = new ArrayList<SysDocumentDir>();
        List<Long> tempList = cbFileDirList.stream().map(SysDocumentDir::getDirId).collect(Collectors.toList());
        for (SysDocumentDir dir : cbFileDirList) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dir.getParentId()))
            {
                recursionFn(cbFileDirList, dir);
                returnList.add(dir);
            }
        }
        if (returnList.isEmpty()) {
            returnList = cbFileDirList;
        }
        return returnList;
    }

    /**
     * 递归构建目录树
     * @param list 目录列表
     * @param t 当前目录
     */
    private void recursionFn(List<SysDocumentDir> list, SysDocumentDir t) {
        // 得到子节点列表
        List<SysDocumentDir> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDocumentDir tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 获取子目录列表
     * @param list 目录列表
     * @param t 当前目录
     * @return 子目录列表
     */
    private List<SysDocumentDir> getChildList(List<SysDocumentDir> list, SysDocumentDir t) {
        List<SysDocumentDir> tlist = new ArrayList<SysDocumentDir>();
        Iterator<SysDocumentDir> it = list.iterator();
        while (it.hasNext()) {
            SysDocumentDir n = (SysDocumentDir) it.next();
            if (ObjUtil.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDirId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子目录
     * @param list 目录列表
     * @param t 当前目录
     * @return 如果有子目录则返回true，否则返回false
     */
    private boolean hasChild(List<SysDocumentDir> list, SysDocumentDir t) {
        return getChildList(list, t).size() > 0;
    }
}
