package com.jkr.project.monitor.controller;

import com.jkr.common.constant.CacheConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.monitor.domain.SysCache;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 缓存监控
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/monitor/cache")
public class CacheController {
	private static final List<SysCache> CACHES = new ArrayList<>();

	static {
		CACHES.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
		CACHES.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
		CACHES.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
		CACHES.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
		CACHES.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
		CACHES.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
		CACHES.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
	}

	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@GetMapping()
	public AjaxResult getInfo() throws Exception {
		Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info());
		Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
		Object dbSize = redisTemplate.execute((RedisCallback<Object>) connection -> connection.dbSize());

		Map<String, Object> result = new HashMap<>(3);
		result.put("info", info);
		result.put("dbSize", dbSize);

		List<Map<String, String>> pieList = new ArrayList<>();
		assert commandStats != null;
		commandStats.stringPropertyNames().forEach(key -> {
			Map<String, String> data = new HashMap<>(2);
			String property = commandStats.getProperty(key);
			data.put("name", StringUtils.removeStart(key, "cmdstat_"));
			data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
			pieList.add(data);
		});
		result.put("commandStats", pieList);
		return AjaxResult.success(result);
	}

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@GetMapping("/getNames")
	public AjaxResult cache() {
		return AjaxResult.success(CACHES);
	}

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@GetMapping("/getKeys/{cacheName}")
	public AjaxResult getCacheKeys(@PathVariable String cacheName) {
		Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
		return AjaxResult.success(new TreeSet<>(cacheKeys));
	}

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@GetMapping("/getValue/{cacheName}/{cacheKey}")
	public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey) {
		try {
			String cacheValue = redisTemplate.opsForValue().get(cacheKey);
			SysCache sysCache = new SysCache(cacheName, cacheKey, cacheValue);
			return AjaxResult.success(sysCache);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return AjaxResult.error("该缓存内容不支持查看");
		}
	}

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@PostMapping("/clearCacheName/{cacheName}")
	public AjaxResult clearCacheName(@PathVariable String cacheName) {
		Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
		redisTemplate.delete(cacheKeys);
		return AjaxResult.success();
	}

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@PostMapping("/clearCacheKey/{cacheKey}")
	public AjaxResult clearCacheKey(@PathVariable String cacheKey) {
		redisTemplate.delete(cacheKey);
		return AjaxResult.success();
	}

	@PreAuthorize("@ss.hasPermi('monitor:cache:list')")
	@PostMapping("/clearCacheAll")
	public AjaxResult clearCacheAll() {
		Collection<String> cacheKeys = redisTemplate.keys("*");
		redisTemplate.delete(cacheKeys);
		return AjaxResult.success();
	}
}
