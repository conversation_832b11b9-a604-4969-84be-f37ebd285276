package com.jkr.project.monitor.controller;

import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.domain.Server;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 服务器监控
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/server")
public class ServerController {
	@PreAuthorize("@ss.hasPermi('monitor:server:list')")
	@GetMapping()
	public AjaxResult getInfo() throws Exception {
		Server server = new Server();
		server.copyTo();
		return AjaxResult.success(server);
	}
}
