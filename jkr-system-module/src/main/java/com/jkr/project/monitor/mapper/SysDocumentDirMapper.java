package com.jkr.project.monitor.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jkr.common.constant.UserConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.mybatis.query.LambdaQueryWrapperX;
import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.system.domain.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Arrays;
import java.util.List;

/**
 * 文档目录数据访问层接口，继承自BaseMapperX，用于对SysDocumentDir实体进行数据库操作
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface SysDocumentDirMapper extends BaseMapper<SysDocumentDir> {
    /**
     * 根据目录ID检查是否有子目录
     * @param dirId 目录ID
     * @return 子目录数量
     */
    default int hasChildByDirId(@Param("dirId") Long dirId){
        List<SysDocumentDir> list = selectList(new LambdaQueryWrapperX<SysDocumentDir>()
                .eq(SysDocumentDir::getParentId, dirId)
                .eq(SysDocumentDir::getDelFlag, "1"));
        return CollUtil.isNotEmpty(list)?list.size():0;
    }

    /**
     * 查询目录数据
     * @param param 查询参数封装对象
     * @return 目录列表
     */
    default List<SysDocumentDir> selectSysDocumentDirList(SysDocumentDir param){
        return selectList(new LambdaQueryWrapperX<SysDocumentDir>()
                .eqIfPresent(SysDocumentDir::getDirId, param.getDirId())
                .eqIfPresent(SysDocumentDir::getParentId,param.getParentId())
                .likeIfPresent(SysDocumentDir::getDirName,param.getDirName())
                .eqIfPresent(SysDocumentDir::getStatus,param.getStatus())
                .eqIfPresent(SysDocumentDir::getDirType,param.getDirType())
                .eq(SysDocumentDir::getDelFlag, "1")
                .orderByAsc(SysDocumentDir::getParentId)
                .orderByAsc(SysDocumentDir::getOrderNum));
    }

    /**
     * 根据目录id查询目录信息
     * @param dirId 目录ID
     * @return 目录信息对象
     */
    SysDocumentDir selectSysDocumentDirById(@Param("dirId") Long dirId);

    /**
     * 根据目录id查询所有子目录
     * @param dirId 目录ID
     * @return 子目录列表
     */
    default List<SysDocumentDir> selectChildrenDirById(Long dirId){
        LambdaQueryWrapper<SysDocumentDir> wrapper = new LambdaQueryWrapperX<>();
        wrapper.apply("find_in_set({0}, ancestors)", dirId);
        return selectList(wrapper);
    }

    /**
     * 校验目录名称是否存在
     * @param sysDocumentDir 目录信息
     * @return 如果目录名称唯一则返回null，否则返回存在的目录对象
     */
    public SysDocumentDir checkDirNameUnique(SysDocumentDir sysDocumentDir);

    /**
     * 修改目录关系
     * @param dirs 目录列表
     */
    default void updateDirChildren(List<SysDocumentDir> dirs){
        if(CollUtil.isEmpty(dirs)){
            return;
        }
        dirs.stream().map(dir -> {
            return new LambdaUpdateWrapper<SysDocumentDir>()
                    .set(SysDocumentDir::getAncestors, dir.getAncestors())
                    .eq(SysDocumentDir::getDirId, dir.getDirId());
        }).toList().forEach(this::update);
    }

    /**
     * 修改目录状态为正常
     * @param dirIds 目录ID数组
     * @return 更新的目录数量
     */
    default int updateDirStatusNormal(Long[] dirIds){
        if(CollUtil.isEmpty(Arrays.asList(dirIds))){
            return 0;
        }
        return update(new LambdaUpdateWrapper<SysDocumentDir>()
                .set(SysDocumentDir::getStatus, "0")
                .in(SysDocumentDir::getDirId, Arrays.asList(dirIds)));
    }

}
