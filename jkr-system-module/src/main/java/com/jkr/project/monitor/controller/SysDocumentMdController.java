package com.jkr.project.monitor.controller;

import com.github.yitter.idgen.YitIdHelper;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.monitor.domain.SysDocumentMd;
import com.jkr.project.monitor.service.SysDocumentMdService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 管理后台 - 文档数据管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sys/documentmd")
@Validated
public class SysDocumentMdController extends BaseController {
    @Resource
    private SysDocumentMdService sysDocumentMdService;


    /**
     * @param sysDocument
     * @return CommonResult<PageResult < SysDocumentMd>>
     * @title pageDocumentMds
     * @Description: 获得文档的分页列表
     * <AUTHOR>
     * @date 2025/2/18 16:58
     */
    @GetMapping("/list")
    /*@PreAuthorize("@ss.hasPermission('sys:documentmd:query')")*/
    public TableDataInfo list(@Valid SysDocumentMd sysDocument) {
        List<SysDocumentMd> list = sysDocumentMdService.selectSysDocumentMdAndDirList(sysDocument);
        return getDataTable(list);
    }

    /**
     * @param documentMd
     * @return CommonResult<Integer>
     * @title create
     * @Description: 创建文档
     * <AUTHOR>
     * @date 2025/2/18 16:58
     */
    @PostMapping("/add")
    @Log(title = "文档信息管理", businessType = BusinessType.INSERT)
    public AjaxResult add(@Validated @RequestBody SysDocumentMd documentMd) {
//        List<SysDocumentMd> list = sysDocumentMdService.checkCbFileMdIsExistInDir(documentMd);
//        if (ObjectUtils.isNotEmpty(list)) {
//            return error("新增失败，该目录下已存在接口文档");
//        }
        documentMd.setMdId(YitIdHelper.nextId());
        return success(sysDocumentMdService.createDocumentMd(documentMd));
    }

    /**
     * @param mdId
     * @return CommonResult<SysDocumentDir>
     * @title getInfo
     * @Description: 根据文档ID获取文档信息
     * <AUTHOR>
     * @date 2025/2/18 16:44
     */
    /*@PreAuthorize("@ss.hasPermission('sys:documentdir:query')")*/
    @PostMapping(value = "/info/{mdId}")
    public AjaxResult getInfo(@PathVariable("mdId") Long mdId) {
        return success(sysDocumentMdService.selectSysDocumentMdById(mdId));
    }

    /**
     * @param documentMd
     * @return com.jkr.framework.web.domain.AjaxResult
     * @title edit
     * @Description: 修改文档
     * <AUTHOR>
     * @date 2025/2/18 17:00
     */
    @PostMapping("/edit")
    @Log(title = "文档信息管理", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@Validated @RequestBody SysDocumentMd documentMd) {
        int res = sysDocumentMdService.updateDocumentMd(documentMd);
        return success(res > 0);
    }

    /**
     * @param mdId
     * @return CommonResult<Boolean>
     * @title remove
     * @Description: 删除文档
     * <AUTHOR>
     * @date 2025/2/18 16:58
     */
    @PostMapping("/remove/{mdId}")
    public AjaxResult remove(@PathVariable("mdId") Long mdId) {
        SysDocumentMd param = new SysDocumentMd();
        param.setMdId(mdId);
        int res = sysDocumentMdService.deleteDocumentMd(param);
        return success(res > 0);
    }

}
