package com.jkr.project.monitor.service.impl;


import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.monitor.domain.SysDocumentMd;
import com.jkr.project.monitor.mapper.SysDocumentMdMapper;
import com.jkr.project.monitor.service.SysDocumentMdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;
import java.util.List;
/**
 * 文档元数据进行查询、创建、更新和删除操作的方法
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@Service
public class SysDocumentMdServiceImpl implements SysDocumentMdService {

    @Autowired
    private SysDocumentMdMapper sysDocumentMdMapper;

    /**
     * 查询Markdown文档列表
     *
     * @param param 查询参数
     * @return Markdown文档列表
     */
    @Override
    public List<SysDocumentMd> selectCbFileMdList(SysDocumentMd param) {
        List<SysDocumentMd> cbFileMdList = sysDocumentMdMapper.selectSysDocumentMdAndDirList(param);
        if (ObjectUtils.isNotEmpty(cbFileMdList)) {
            for (int i = 0; i < cbFileMdList.size(); i++) {
                // 解密render字段，以便返回原始内容
                String render = new String(Base64.getDecoder().decode(cbFileMdList.get(i).getRender()));
                cbFileMdList.get(i).setRender(render);
            }
        }
        return cbFileMdList;
    }

    /**
     * 获取Markdown文档分页数据
     *
     * @param param 分页查询请求对象
     * @return 分页结果
     */
    @Override
    public List<SysDocumentMd> getDocumentMdList(SysDocumentMd param) {
        return  sysDocumentMdMapper.selectSysDocumentMdList(param);
    }

    /**
     * @title selectSysDocumentMdAndDirList
     * @Description: 自定义查询，增加目录名称
     * @param queryParam
     * @return java.util.List<com.jkr.project.monitor.domain.SysDocumentMd>
     * <AUTHOR>
     * @date 2025/2/19 14:51
     */
    @Override
    public List<SysDocumentMd> selectSysDocumentMdAndDirList(SysDocumentMd queryParam){
        List<SysDocumentMd> list = sysDocumentMdMapper.selectSysDocumentMdAndDirList(queryParam);
        if (ObjectUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                String render = new String(Base64.getDecoder().decode(list.get(i).getRender()));
                list.get(i).setRender(render);
            }
        }
        return  sysDocumentMdMapper.selectSysDocumentMdAndDirList(queryParam);
    }
    /**
     * 根据文档ID查询目录信息
     * @param mdId 文档ID
     * @return 文档信息
     */
    @Override
    public SysDocumentMd selectSysDocumentMdById(Long mdId) {
        return sysDocumentMdMapper.selectSysDocumentMdById(mdId);
    }
    /**
     * 检查Markdown文档在目录中是否存在
     *
     * @param param 查询参数
     * @return 存在的Markdown文档列表
     */
    @Override
    public List<SysDocumentMd> checkCbFileMdIsExistInDir(SysDocumentMd param) {
        return sysDocumentMdMapper.checkDocumentMdIsExistInDir(param);
    }

    /**
     * 创建Markdown文档
     *
     * @param param 要创建的Markdown文档对象
     * @return 插入结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createDocumentMd(SysDocumentMd param) {
        // render加密，解决表情包插入字符错误问题
        String render = Base64.getEncoder().encodeToString(param.getRender().getBytes());
        param.setRender(render);
        return sysDocumentMdMapper.insert(param);
    }

    /**
     * 更新Markdown文档
     *
     * @param param 要更新的Markdown文档对象
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDocumentMd(SysDocumentMd param) {
        // render加密
        String render = Base64.getEncoder().encodeToString(param.getRender().getBytes());
        param.setRender(render);
        return sysDocumentMdMapper.updateById(param);
    }

    /**
     * 删除Markdown文档
     *
     * @param param 要删除的Markdown文档对象
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDocumentMd(SysDocumentMd param) {
        return sysDocumentMdMapper.deleteById(param);
    }
}

