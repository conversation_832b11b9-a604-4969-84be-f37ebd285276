package com.jkr.project.monitor.service;


import com.jkr.project.monitor.domain.SysDocumentDir;

import java.util.List;

/**
 * DocumentDirService接口定义了与文档目录相关的服务方法
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface SysDocumentDirService {

    /**
     * 根据目录ID检查是否有子目录
     * @param dirId 目录ID
     * @return 子目录数量
     */
    int hasChildByDirId(Long dirId);

    /**
     * 查询目录数据
     * @param param 查询参数封装对象
     * @return 目录列表
     */
    List<SysDocumentDir> selectSysDocumentDirList(SysDocumentDir param);

    /**
     * 根据目录id查询目录信息
     * @param dirId 目录ID
     * @return 目录信息对象
     */
    SysDocumentDir selectSysDocumentDirById(Long dirId);

    /**
     * 根据目录id查询所有子目录
     * @param dirId 目录ID
     * @return 子目录列表
     */
    List<SysDocumentDir> selectChildrenDirById(Long dirId);

    /**
     * 校验目录名称是否存在
     * @param sysDocumentDir 目录信息
     * @return 如果目录名称唯一，则返回null；否则返回重复的目录对象
     */
    boolean checkDirNameUnique(SysDocumentDir sysDocumentDir) ;

    /**
     * 创建文档目录
     * @param param
     * @return
     */
    public int createDocumentDir(SysDocumentDir param);

    /**
     * 更新文档目录
     * @param param
     * @return
     */
    public int updateDocumentDir(SysDocumentDir param);
    /**
     * 修改目录关系
     * @param dirs 目录列表，包含更新后的目录关系
     * @return 更新的目录数量
     */
    void updateDirChildren(List<SysDocumentDir> dirs);

    /**
     * 修改目录状态
     * @param dirIds 目录ID数组，标识要更新的目录
     * @return 更新的目录数量
     */
    int updateDirStatusNormal(Long[] dirIds);

    int deleteDirById(Long dirId);

}
