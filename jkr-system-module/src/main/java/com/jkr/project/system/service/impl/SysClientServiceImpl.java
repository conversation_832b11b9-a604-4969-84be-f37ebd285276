package com.jkr.project.system.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.jkr.common.constant.SsoConstant;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.Md5Util;
import com.jkr.common.utils.RandomUtils;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.system.mapper.SysClientMapper;
import com.jkr.project.system.domain.SysClient;
import com.jkr.project.system.service.ISysClientService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 客户端应用Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class SysClientServiceImpl implements ISysClientService {
    @Autowired
    private SysClientMapper sysClientMapper;
    @Autowired
    private ISysFileService sysFileService;
    /**
     * 平台内系统用户的唯一标志
     */
    public static final String SYS_CLIENT = "sys_client";


    /**
     * 查询客户端应用
     *
     * @param id 客户端应用主键
     * @return 客户端应用
     */
    @Override
    public SysClient selectSysClientById(Long id) {
        SysClient sysClient = sysClientMapper.selectSysClientById(id);
        //查询附件信息
        List<SysFile> sysFileList = sysFileService.findUrlsFileList(new SysFile(id, SYS_CLIENT));
        sysClient.setImgInfoList(sysFileList);

        return sysClient;
    }

    /**
     * 查询客户端应用列表
     *
     * @param sysClient 客户端应用
     * @return 客户端应用
     */
    @Override
    public List<SysClient> selectSysClientList(SysClient sysClient) {
        return sysClientMapper.selectSysClientList(sysClient);
    }

    /**
     * 新增客户端应用
     *
     * @param sysClient 客户端应用
     * @return 结果
     */
    @Override
    @Transactional
    public int insertSysClient(SysClient sysClient) {
        sysClient.setCreateTime(DateUtils.getNowDate());

        int result = sysClientMapper.insertSysClient(sysClient);
        //生成编号与秘钥
        sysClient.setClientAppId(Md5Util.md5(sysClient.getId() + sysClient.getCnName()));
        String code = RandomUtils.createRandom(false, SsoConstant.SALT_LENGTH);
        sysClient.setClientSecret(Md5Util.md5(sysClient.getId() + sysClient.getClientAppId() + code));
        sysClientMapper.updateSysClient(sysClient);
        //生成编号与秘钥
        //保存附件
        sysFileService.insertSysFileListByTableId(sysClient.getImgInfoList(), sysClient.getId());
        return result;
    }

    /**
     * 修改客户端应用
     *
     * @param sysClient 客户端应用
     * @return 结果
     */
    @Override
    public int updateSysClient(SysClient sysClient) {
        sysFileService.insertSysFileListByTableId(sysClient.getImgInfoList(), sysClient.getId());
        sysClient.setUpdateTime(DateUtils.getNowDate());
        return sysClientMapper.updateSysClient(sysClient);
    }

    /**
     * 批量删除客户端应用
     *
     * @param ids 需要删除的客户端应用主键
     * @return 结果
     */
    @Override
    public int deleteSysClientByClientIds(Long[] ids) {
        return sysClientMapper.deleteSysClientByIds(ids);
    }

    /**
     * 删除客户端应用信息
     *
     * @param id 客户端应用主键
     * @return 结果
     */
    @Override
    public int deleteSysClientByClientId(Long id) {
        return sysClientMapper.deleteSysClientById(id);
    }
}
