package com.jkr.project.system.controller;

import com.jkr.common.constant.UserConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysDept;
import com.jkr.project.system.service.ISysDeptService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController {
	@Resource
	private ISysDeptService deptService;

	/**
	 * 获取机构列表
	 */
	@PreAuthorize("@ss.hasPermi('system:dept:list')")
	@GetMapping("/list")
	public AjaxResult list(SysDept dept) {
		List<SysDept> depts = deptService.selectDeptList(dept);
		return success(depts);
	}

	/**
	 * 查询机构列表（排除节点）
	 */
	@PreAuthorize("@ss.hasPermi('system:dept:list')")
	@GetMapping("/list/exclude/{deptId}")
	public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
		List<SysDept> depts = deptService.selectDeptList(new SysDept());
		depts.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
		return success(depts);
	}

	/**
	 * 根据机构编号获取详细信息
	 */
	@PreAuthorize("@ss.hasPermi('system:dept:query')")
	@GetMapping(value = "/info/{deptId}")
	public AjaxResult getInfo(@PathVariable Long deptId) {
		deptService.checkDeptDataScope(deptId);
		return success(deptService.selectDeptById(deptId));
	}

	/**
	 * 新增机构
	 */
	@PreAuthorize("@ss.hasPermi('system:dept:add')")
	@Log(title = "机构管理", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public AjaxResult add(@Validated @RequestBody SysDept dept) {
		if (!deptService.checkDeptNameUnique(dept)) {
			return error("新增机构'" + dept.getDeptName() + "'失败，机构名称已存在");
		}
		dept.setCreateBy(getUsername());
		return toAjax(deptService.insertDept(dept));
	}

	/**
	 * 修改机构
	 */
	@PreAuthorize("@ss.hasPermi('system:dept:edit')")
	@Log(title = "机构管理", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult edit(@Validated @RequestBody SysDept dept) {
		Long deptId = dept.getDeptId();
		deptService.checkDeptDataScope(deptId);
		if (!deptService.checkDeptNameUnique(dept)) {
			return error("修改机构'" + dept.getDeptName() + "'失败，机构名称已存在");
		} else if (deptId.equals(dept.getParentId())) {
			return error("修改机构'" + dept.getDeptName() + "'失败，上级机构不能是自己");
		} else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0) {
			return error("该机构包含未停用的子机构！");
		}
		dept.setUpdateBy(getUsername());
		return toAjax(deptService.updateDept(dept));
	}

	/**
	 * 删除机构
	 */
	@PreAuthorize("@ss.hasPermi('system:dept:remove')")
	@Log(title = "机构管理", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{deptId}")
	public AjaxResult remove(@PathVariable Long deptId) {
		if (deptService.hasChildByDeptId(deptId)) {
			return warn("存在下级机构,不允许删除");
		}
		if (deptService.checkDeptExistUser(deptId)) {
			return warn("机构存在用户,不允许删除");
		}
		deptService.checkDeptDataScope(deptId);
		return toAjax(deptService.deleteDeptById(deptId));
	}
}
