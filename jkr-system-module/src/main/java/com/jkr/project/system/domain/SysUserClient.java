package com.jkr.project.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 系统账号与客户端关联关系对象 sys_user_client
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserClient extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private Long userId;

    /**
     * 客户端系统登录名
     */
    @Excel(name = "客户端系统登录名")
    private String clientLoginName;

    /**
     * 应用ID
     */
    @Excel(name = "应用ID")
    private Long clientId;

    /**
     * 删除标志（默认为0，表示数据可用，所有值为时间戳的表示数据不可用）
     */
    private String delFlag;


    @ApiModelProperty(value = "是否授权")
    @TableField(exist = false)
    private Boolean auth;

    @ApiModelProperty(value = "客户端编号")
    @TableField(exist = false)
    private String clientAppId;

    @ApiModelProperty(value = "客户端密钥")
    @TableField(exist = false)
    private String clientSecret;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("loginName", getClientLoginName())
                .append("clientId", getClientId())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
