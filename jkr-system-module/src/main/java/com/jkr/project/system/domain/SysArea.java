package com.jkr.project.system.domain;

import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 区域管理对象 sys_area
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysArea extends BaseModel {
    private static final long serialVersionUID = 1L;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 父级编号
     */
    @Excel(name = "父级编号")
    private String parentId;

    /**
     * 所有父级编号
     */
    @Excel(name = "所有父级编号")
    private String parentIds;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 区域编码
     */
    @Excel(name = "区域编码")
    private String code;

    /**
     * 区域类型
     */
    @Excel(name = "区域类型")
    private String type;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String lng;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String lat;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    /**
     * 区域类型集合
     */
    private List<String> typeList;
    /**
     * 编号前缀
     */
    private String codePrefix;
    /**
     * 子集合
     */
    private List<SysArea> children;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("areaId", getAreaId())
                .append("parentId", getParentId())
                .append("parentIds", getParentIds())
                .append("name", getName())
                .append("code", getCode())
                .append("type", getType())
                .append("lng", getLng())
                .append("lat", getLat())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
