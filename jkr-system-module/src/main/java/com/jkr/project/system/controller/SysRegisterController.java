package com.jkr.project.system.controller;

import com.jkr.common.constant.SysConfigConstants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.security.RegisterBody;
import com.jkr.framework.security.service.SysRegisterService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 注册验证
 *
 * <AUTHOR>
 */
@RestController
public class SysRegisterController extends BaseController {
	@Autowired
	private SysRegisterService registerService;

	@Autowired
	private ISysConfigService configService;

	/**
	 * 注册功能
	 *
	 * @date 2025年01月15日 15:32:09
	 * @param user
	 * @return com.jkr.framework.web.domain.AjaxResult
	 */
	@PostMapping("/register")
	public AjaxResult register(@RequestBody RegisterBody user) {
		if (!("true".equals(configService.selectConfigByKey(SysConfigConstants.SYS_ACCOUNT_REGISTER_USER)))) {
			return error("当前系统没有开启注册功能！");
		}
		String msg = registerService.register(user);
		return StringUtils.isEmpty(msg) ? success() : error(msg);
	}
}
