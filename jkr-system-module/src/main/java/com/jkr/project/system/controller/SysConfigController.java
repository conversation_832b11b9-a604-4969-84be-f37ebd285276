package com.jkr.project.system.controller;

import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Anonymous;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.SysConfig;
import com.jkr.project.system.service.ISysConfigService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 参数配置 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/config")
public class SysConfigController extends BaseController {
	@Autowired
	private ISysConfigService configService;

	/**
	 * 获取参数配置列表
	 */
	@PreAuthorize("@ss.hasPermi('system:config:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysConfig config) {
		startPage();
		List<SysConfig> list = configService.selectConfigList(config);
		return getDataTable(list);
	}

	@Log(title = "参数管理", businessType = BusinessType.EXPORT)
	@PreAuthorize("@ss.hasPermi('system:config:export')")
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysConfig config) {
		List<SysConfig> list = configService.selectConfigList(config);
		ExcelUtil<SysConfig> util = new ExcelUtil<SysConfig>(SysConfig.class);
		util.exportExcel(response, list, "参数数据");
	}

	/**
	 * 根据参数编号获取详细信息
	 */
	@PreAuthorize("@ss.hasPermi('system:config:query')")
	@GetMapping(value = "/info/{configId}")
	public AjaxResult getInfo(@PathVariable Long configId) {
		return success(configService.selectConfigById(configId));
	}

	/**
	 * 根据参数键名查询参数值
	 */
	@GetMapping(value = "/configKey/{configKey}")
	public AjaxResult getConfigKey(@PathVariable String configKey) {
		return success(configService.selectConfigByKey(configKey));
	}

	/**
	 * 新增参数配置
	 */
	@PreAuthorize("@ss.hasPermi('system:config:add')")
	@Log(title = "参数管理", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public AjaxResult add(@Validated @RequestBody SysConfig config) {
		if (!configService.checkConfigKeyUnique(config)) {
			return error("新增参数'" + config.getConfigName() + "'失败，参数键名已存在");
		}
		config.setCreateBy(getUsername());
		return toAjax(configService.insertConfig(config));
	}

	/**
	 * 修改参数配置
	 */
	@PreAuthorize("@ss.hasPermi('system:config:edit')")
	@Log(title = "参数管理", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult edit(@Validated @RequestBody SysConfig config) {
		if (!configService.checkConfigKeyUnique(config)) {
			return error("修改参数'" + config.getConfigName() + "'失败，参数键名已存在");
		}
		config.setUpdateBy(getUsername());
		return toAjax(configService.updateConfig(config));
	}

	/**
	 * 删除参数配置
	 */
	@PreAuthorize("@ss.hasPermi('system:config:remove')")
	@Log(title = "参数管理", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{configIds}")
	public AjaxResult remove(@PathVariable Long[] configIds) {
		configService.deleteConfigByIds(configIds);
		return success();
	}

	/**
	 * 刷新参数缓存
	 */
	@PreAuthorize("@ss.hasPermi('system:config:remove')")
	@Log(title = "参数管理", businessType = BusinessType.CLEAN)
	@PostMapping("/refreshCache")
	public AjaxResult refreshCache() {
		configService.resetConfigCache();
		return success();
	}

	/**
	 * 获取网站配置信息
	 *
	 * @return com.jkr.framework.web.domain.AjaxResult
	 * <AUTHOR>
	 * @date 2025年01月16日 09:13:11
	 */
	@Anonymous
	@GetMapping("/data/for/website")
	public AjaxResult dataForWebsite(SysConfig config) {
		return success(configService.selectDataForWebsite(config));
	}

	/**
	 * 获取短信验证码配置
	 *
	 * @return com.jkr.framework.web.domain.AjaxResult
	 * <AUTHOR>
	 * @date 2025年01月16日 09:13:11
	 */
	@Anonymous
	@GetMapping("/data/for/smsConfig")
	public AjaxResult dataForSmsConfig() {
		return success(configService.selectDataForSmsConfig());
	}
}
