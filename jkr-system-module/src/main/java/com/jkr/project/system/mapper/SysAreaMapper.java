package com.jkr.project.system.mapper;

import com.jkr.project.system.domain.SysArea;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 区域管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Mapper
public interface SysAreaMapper {
	/**
	 * 查询区域管理
	 *
	 * @param areaId 区域管理主键
	 * @return 区域管理
	 */
	public SysArea selectSysAreaByAreaId(String areaId);

	/**
	 * 查询区域管理列表
	 *
	 * @param sysArea 区域管理
	 * @return 区域管理集合
	 */
	public List<SysArea> selectSysAreaList(SysArea sysArea);

	/**
	 * 新增区域管理
	 *
	 * @param sysArea 区域管理
	 * @return 结果
	 */
	public int insertSysArea(SysArea sysArea);

	/**
	 * 修改区域管理
	 *
	 * @param sysArea 区域管理
	 * @return 结果
	 */
	public int updateSysArea(SysArea sysArea);

	/**
	 * 删除区域管理
	 *
	 * @param areaId 区域管理主键
	 * @return 结果
	 */
	public int deleteSysAreaByAreaId(String areaId);

	/**
	 * 批量删除区域管理
	 *
	 * @param areaIds 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteSysAreaByAreaIds(String[] areaIds);

	public List<SysArea> findTreeList(SysArea area);
}
