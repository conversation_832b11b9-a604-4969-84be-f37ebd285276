package com.jkr.project.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jkr.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件对象 sys_file
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFile extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 菜单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(value = "file_id")
    private Long fileId;
    /**
     * 业务表id
     */
    private Long tableId;

    /**
     * 业务表名称
     */
    private String tableName;

    /**
     * 字段类型（用于区分相同表名中 不同类型的附件）
     */
    private String fieldType;

    /**
     * 文件存储时名称
     */
    private String name;

    /**
     * 文件真实名称
     */
    private String realName;

    /**
     * 文件存储路径
     */
    private String path;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件大小（kb）
     */
    private Integer size;

    /** 上传文件来源: 0:minio;1:七牛云,默认0 */
    private String sourceType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private Integer delFlag;

    /**
     * 文件存储路径
     */
    @TableField(exist = false)
    private String url;
    /**
     * 文件存储路径
     */
    @TableField(exist = false)
    private String remark;

    @TableField(exist = false)
    private String thumbUrl;

    public SysFile() {

    }

    public SysFile(Long tableId, String tableName) {
        this.tableId = tableId;
        this.tableName = tableName;
    }
}
