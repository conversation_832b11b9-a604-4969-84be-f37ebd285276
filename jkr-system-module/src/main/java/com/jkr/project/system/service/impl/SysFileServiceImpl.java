package com.jkr.project.system.service.impl;

import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.file.FileUtils;
import com.jkr.common.utils.file.MinioUtil;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.mapper.SysFileMapper;
import com.jkr.project.system.service.ISysFileService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Service
public class SysFileServiceImpl implements ISysFileService {
	@Autowired
	private SysFileMapper sysFileMapper;
	@Autowired
	private MinioUtil minioUtil;
	@Autowired
	private MinioClient minioClient;

	@Value("${com.jkr.minio.bucketName}")
	private String bucketName;
	@Value("${qiniu.domain}")
	private String qiniuDomain;
	/**
	 * 查询文件
	 *
	 * @param fileId 文件主键
	 * @return 文件
	 */
	@Override
	public SysFile selectSysFileById(Long fileId) {
		SysFile sysFile = sysFileMapper.selectSysFileById(fileId);
		if (StringUtils.equals(sysFile.getSourceType(), "0")) {// minio
			sysFile.setUrl(minioUtil.presignedGetObject(sysFile.getPath(), null));
			sysFile.setThumbUrl(minioUtil.presignedGetObject(sysFile.getPath(), null));
		} else {
			sysFile.setUrl(qiniuDomain + sysFile.getPath());
			sysFile.setThumbUrl(qiniuDomain + sysFile.getPath());
		}
		return sysFile;
	}

	/**
	 * 查询文件列表
	 *
	 * @param sysFile 文件
	 * @return 文件
	 */
	@Override
	public List<SysFile> selectSysFileList(SysFile sysFile) {
		return sysFileMapper.selectSysFileList(sysFile);
	}

	/**
	 * 新增文件
	 *
	 * @param sysFile 文件
	 * @return 结果
	 */
	@Override
	public int insertSysFile(SysFile sysFile) {
                sysFile.setCreateTime(DateUtils.getNowDate());
			return sysFileMapper.insertSysFile(sysFile);
	}
	/**
	 * 新增文件
	 *
	 * @param sysFileList 文件
	 * @return 结果
	 */
	@Override
	public void insertSysFileList(List<SysFile> sysFileList) {
		if(!CollectionUtils.isEmpty(sysFileList)) {
			for (SysFile sysFile : sysFileList) {
				sysFile.setCreateTime(DateUtils.getNowDate());
				sysFileMapper.insertSysFile(sysFile);
			}
		}
	}

	/**
	 * <AUTHOR>
	 * @Date: 2020/7/24 15:51
	 * @Description: 保存附件列表
	 * @param list
	 * @param tableId
	 * @return
	 */
	public void insertSysFileListByTableId(List<SysFile> list, Long tableId) {
		if (null != list && !list.isEmpty()) {
			for (SysFile sysFile : list) {
				sysFile.setTableId(tableId);
				sysFile.setCreateTime(DateUtils.getNowDate());
				sysFileMapper.insertSysFile(sysFile);
			}

		}
	}
	/**
	 * 修改文件
	 *
	 * @param sysFile 文件
	 * @return 结果
	 */
	@Override
	public int updateSysFile(SysFile sysFile) {
                sysFile.setUpdateTime(DateUtils.getNowDate());
		return sysFileMapper.updateSysFile(sysFile);
	}

	/**
	 * 批量删除文件
	 *
	 * @param ids 需要删除的文件主键
	 * @return 结果
	 */
	@Override
	public int deleteSysFileByIds(Long[] ids) {
		return sysFileMapper.deleteSysFileByIds(ids);
	}

	/**
	 * 删除文件信息
	 *
	 * @param fileId 文件主键
	 * @return 结果
	 */
	@Override
	public int deleteSysFileById(Long fileId) {
		return sysFileMapper.deleteSysFileById(fileId);
	}


	/**
	 * 查询相关list 并将路径传到前台
	 *
	 * @param sf
	 * @return java.util.List<com.jkr.project.system.domain.SysFile>
	 * <AUTHOR>
	 * @date 2020年09月16日 13:55:48
	 * @since 1.0.0
	 */
	@Override
    public List<SysFile> findBase64FileList(SysFile sf) {
		List<SysFile> sysFileList = sysFileMapper.selectSysFileList(sf);
		if(sysFileList!=null && !sysFileList.isEmpty()){
			for (SysFile sysFile : sysFileList) {
				try (InputStream in = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(sysFile.getPath()).build())) {
					sysFile.setUrl("data:" + sysFile.getType() + ";base64," + FileUtils.getBase64FromInputStream(in));
				} catch (Exception e) {
					log.error(e.getMessage(), e);
					return null;
				}
			}
		}
		return sysFileList;
	}

	/**
	 *
	 * 根据业务表Id和附件业务类型逻辑删除附件
	 *
	 * <AUTHOR>
	 * @date 2022年01月07日 13:25:07
	 * @param tableId 业务表Id
	 * @return : void
	 */
	public void removeByTableIdAndFieldType(Long tableId, String fieldType) {
		this.sysFileMapper.removeByTableIdAndFieldType(tableId, fieldType);
	}


	/**
	 * 查询相关list 并将路径传到前台
	 *
	 * @param sf
	 * @return java.util.List<com.jkr.project.system.domain.SysFile>
	 * <AUTHOR>
	 * @date 2022/1/5 16:31
	 */
	@Override
    public List<SysFile> findUrlsFileList(SysFile sf) {
		List<SysFile> sysFileList = sysFileMapper.selectSysFileList(sf);
		if (sysFileList != null && !sysFileList.isEmpty()) {
			for (SysFile sysFile : sysFileList) {
				if (StringUtils.equals(sysFile.getSourceType(), "0")) {// minio
					sysFile.setUrl(minioUtil.presignedGetObject(sysFile.getPath(), null));
				} else {
					sysFile.setUrl(qiniuDomain + sysFile.getPath());
				}
			}
		}
		return sysFileList;
	}

	@Override
	public void copyAttachment(Long tblId, String tblName, Long tblIdNew, String tblNameNew) {
		SysFile attachment = new SysFile();
		attachment.setTableId(tblId);
		attachment.setTableName(tblName);
		List<SysFile> list = sysFileMapper.selectSysFileList(attachment);
		for (SysFile att : list) {
			att.setTableId(tblIdNew);
			att.setTableName(tblNameNew);
			insertSysFile(att);
		}
	}

	@Override
	public List<SysFile> addListNew(List<SysFile> sysFileList) {
		if(!CollectionUtils.isEmpty(sysFileList)) {
			for (SysFile sysFile : sysFileList) {
				sysFile.setCreateTime(DateUtils.getNowDate());
				sysFile.setUrl(minioUtil.presignedGetObject(sysFile.getPath(), null));
				sysFile.setThumbUrl(minioUtil.presignedGetObject(sysFile.getPath(), null));
				sysFileMapper.insert(sysFile);
			}
		}
		return sysFileList;
	}

	/**
	 * 查询当前id、tableName、fieldType的文件list
	 *
	 * @param id        编号
	 * @param tableName 文件对应表名
	 * @param fieldType 文件类型
	 * @return java.util.List<com.jkr.project.system.domain.SysFile>
	 * <AUTHOR>
	 * @date 2021年12月28日 09:50:37
	 */
	public List<SysFile> findUrlFilesList(Long id, String tableName, String fieldType) {
		SysFile sf = new SysFile();
		sf.setTableId(id);
		sf.setTableName(tableName);
		sf.setFieldType(fieldType);
		return this.findUrlsFileList(sf);
	}

}
