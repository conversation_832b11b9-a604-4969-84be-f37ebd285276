package com.jkr.project.system.mapper;

import java.util.List;
import java.util.Map;

import com.jkr.project.system.domain.SysSms;
import org.apache.ibatis.annotations.Mapper;

/**
 * 短信记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Mapper
public interface SysSmsMapper {
    /**
     * 查询短信记录
     *
     * @param smsId 短信记录主键
     * @return 短信记录
     */
    public SysSms selectSysSmsBySmsId(String smsId);

    /**
     * 查询短信记录列表
     *
     * @param sysSms 短信记录
     * @return 短信记录集合
     */
    public List<SysSms> selectSysSmsList(SysSms sysSms);

    /**
     * 新增短信记录
     *
     * @param sysSms 短信记录
     * @return 结果
     */
    public int insertSysSms(SysSms sysSms);

    /**
     * 修改短信记录
     *
     * @param sysSms 短信记录
     * @return 结果
     */
    public int updateSysSms(SysSms sysSms);

    /**
     * 删除短信记录
     *
     * @param smsId 短信记录主键
     * @return 结果
     */
    public int deleteSysSmsBySmsId(String smsId);


    /**
     * @title: searchSendInfoByIpAndPhone
     * @author: lty
     * @date: 2025年1月17日 下午4:04:52
     * @description: 通ip或手机号 获取发送短信记录条数 严格控制相同ip或手机号发送短信数量
     * @param: @param sysSms
     * @param: @return
     * @return: List<Map < String, Object>>
     */
    public List<Map<String, Object>> searchSendInfoByIpAndPhone(SysSms sysSms);



    /**
     * @title searchSmsCode
     * @Description: 获取短信记录
     * @param sysSms
     * @return com.jkr.project.system.domain.SysSms
     * <AUTHOR>
     * @date 2025/1/17 16:22
     */
    public SysSms searchSmsCode(SysSms sysSms);
}
