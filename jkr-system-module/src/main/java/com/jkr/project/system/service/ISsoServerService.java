package com.jkr.project.system.service;

import com.jkr.framework.web.domain.AjaxResult;
import org.springframework.web.bind.annotation.RequestParam;

public interface ISsoServerService {
    /**
     * @Title: appAuthByAppId
     * @author: <PERSON><PERSON><PERSON><PERSON>
     * @date: 2020/12/17/0017 10:42
     * @Description: 为业务系统授权
     * @Param: [token] ssoToken
     * @return:com.sx.common.ResponseResult<java.lang.String>
     */
    public AjaxResult appAuthByAppId();

    /**
     * @param loginName
     * @param password
     * @param appId
     * @param appSecret
     * @return com.jkr.framework.web.domain.AjaxResult
     * @title getAppToken
     * @Description: TODO
     * <AUTHOR>
     * @date 2025/2/5 15:54
     */
    public AjaxResult getAppToken(String loginName, String password, String appId, String appSecret) throws Exception;
}
