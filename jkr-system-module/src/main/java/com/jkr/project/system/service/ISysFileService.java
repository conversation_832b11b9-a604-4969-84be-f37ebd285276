package com.jkr.project.system.service;

import java.util.List;

import com.jkr.project.system.domain .SysFile;
import com.jkr.project.system.domain.vo.MinioInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件Service接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface ISysFileService {
	/**
	 * 查询文件
	 *
	 * @param fileId 文件主键
	 * @return 文件
	 */
	public SysFile selectSysFileById(Long fileId);

	/**
	 * 查询文件列表
	 *
	 * @param sysFile 文件
	 * @return 文件集合
	 */
	public List<SysFile> selectSysFileList(SysFile sysFile);

	/**
	 * 新增文件
	 *
	 * @param sysFile 文件
	 * @return 结果
	 */
	public int insertSysFile(SysFile sysFile);
	/**
	 * 批量新增文件
	 *
	 * @param sysFileList 文件
	 * @return 结果
	 */
	public void insertSysFileList(List<SysFile> sysFileList);

	/**
	 * <AUTHOR>
	 * @Date: 2020/7/24 15:51
	 * @Description: 关联业务保存附件
	 * @param list
	 * @param tableId
	 * @return
	 */
	public void insertSysFileListByTableId(List<SysFile> list, Long tableId);

	/**
	 * 修改文件
	 *
	 * @param sysFile 文件
	 * @return 结果
	 */
	public int updateSysFile(SysFile sysFile);

	/**
	 * 批量删除文件
	 *
	 * @param ids 需要删除的文件主键集合
	 * @return 结果
	 */
	public int deleteSysFileByIds(Long[] ids);

	/**
	 * 删除文件信息
	 *
	 * @param fileId 文件主键
	 * @return 结果
	 */
	public int deleteSysFileById(Long fileId);

	/**
	 * 查询相关list 并将路径传到前台
	 *
	 * @param sf
	 * @return java.util.List<com.jkr.project.system.domain.SysFile>
	 * <AUTHOR>
	 * @date 2025年01月16日 13:55:48
	 * @since 1.0.0
	 */
	public List<SysFile> findBase64FileList(SysFile sf);


	/**
	 * 查询相关list 并将路径传到前台
	 *
	 * @param sf
	 * @return java.util.List<com.jkr.project.system.domain.SysFile>
	 * <AUTHOR>
	 * @date 2022/1/5 16:31
	 */
	public List<SysFile> findUrlsFileList(SysFile sf);

	public void copyAttachment(Long tblId, String tblName,Long tblIdNew,String tblNameNew);

	List<SysFile> addListNew(List<SysFile> sysFileList);
}
