package com.jkr.project.system.service.impl;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.jkr.common.utils.DateUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jkr.project.system.mapper.SysUserClientMapper;
import com.jkr.project.system.domain.SysUserClient;
import com.jkr.project.system.service.ISysUserClientService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 系统账号与客户端关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Service
public class SysUserClientServiceImpl implements ISysUserClientService {
	@Autowired
	private SysUserClientMapper sysUserClientMapper;

	/**
	 * 查询系统账号与客户端关联关系
	 *
	 * @param id 系统账号与客户端关联关系主键
	 * @return 系统账号与客户端关联关系
	 */
	@Override
	public SysUserClient selectSysUserClientById(Long id) {
		return sysUserClientMapper.selectSysUserClientById(id);
	}

	/**
	 * 查询系统账号与客户端关联关系列表
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 系统账号与客户端关联关系
	 */
	@Override
	public List<SysUserClient> selectSysUserClientList(SysUserClient sysUserClient) {
		return sysUserClientMapper.selectSysUserClientList(sysUserClient);
	}

	/**
	 * 新增系统账号与客户端关联关系
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 结果
	 */
	@Override
	public int insertSysUserClient(SysUserClient sysUserClient) {
                sysUserClient.setCreateTime(DateUtils.getNowDate());
			return sysUserClientMapper.insertSysUserClient(sysUserClient);
	}

	/**
	 * 修改系统账号与客户端关联关系
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 结果
	 */
	@Override
	public int updateSysUserClient(SysUserClient sysUserClient) {
                sysUserClient.setUpdateTime(DateUtils.getNowDate());
		return sysUserClientMapper.updateSysUserClient(sysUserClient);
	}

	/**
	 * 批量删除系统账号与客户端关联关系
	 *
	 * @param ids 需要删除的系统账号与客户端关联关系主键
	 * @return 结果
	 */
	@Override
	public int deleteSysUserClientByIds(Long[] ids) {
		return sysUserClientMapper.deleteSysUserClientByIds(ids);
	}

	/**
	 * 删除系统账号与客户端关联关系信息
	 *
	 * @param id 系统账号与客户端关联关系主键
	 * @return 结果
	 */
	@Override
	public int deleteSysUserClientById(Long id) {
		return sysUserClientMapper.deleteSysUserClientById(id);
	}

	/**
	 * @MethodName: saveOrUpdate
	 * @Description: 根据授权情况保存或修改表数据
	 * @Param: [entity]
	 * @Return: boolean
	 * @Author: lty
	 * @Date: 10:23
	 **/
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int saveOrUpdate(SysUserClient entity) {
		if(entity.getAuth()){//新增
		return 	insertSysUserClient(entity);
		}else{//删除
			return sysUserClientMapper.deleteSysUserClientByEntity(entity);
			// return ResponseResult.success();
		}
	}

	/**
	 * @title hasAuthByUser
	 * @Description: 查询用户是否授权应用系统
	 * @param sysUserClient 用户id
	 * @return boolean
	 * <AUTHOR>
	 * @date 2025/2/6 15:21
	 */
	@Override
	public boolean hasAuthByUser(SysUserClient sysUserClient){
		int result = sysUserClientMapper.hasAuthByUser(sysUserClient);
		return result > 0;
	}
}
