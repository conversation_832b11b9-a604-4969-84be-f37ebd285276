package com.jkr.project.system.controller;

import java.util.List;
import java.util.Map;

import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.ISysUserService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.system.domain.SysSms;
import com.jkr.project.system.service.ISysSmsService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 短信记录Controller
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@RestController
@RequestMapping("/system/sms")
public class SysSmsController extends BaseController {
    @Autowired
    private ISysSmsService sysSmsService;

    /**
     * 查询短信记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:sms:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysSms sysSms) {
        startPage();
        List<SysSms> list = sysSmsService.selectSysSmsList(sysSms);
        return getDataTable(list);
    }

    /**
     * 导出短信记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:sms:export')")
    @Log(title = "短信记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysSms sysSms) {
        List<SysSms> list = sysSmsService.selectSysSmsList(sysSms);
        ExcelUtil<SysSms> util = new ExcelUtil<SysSms>(SysSms.class);
        util.exportExcel(response, list, "短信记录数据");
    }

    /**
     * 获取短信记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sms:query')")
    @GetMapping(value = "/info/{smsId}")
    public AjaxResult getInfo(@PathVariable("smsId") String smsId) {
        return success(sysSmsService.selectSysSmsBySmsId(smsId));
    }

    /**
     * 新增短信记录
     */
    @PreAuthorize("@ss.hasPermi('system:sms:add')")
    @Log(title = "短信记录", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody SysSms sysSms) {
        return toAjax(sysSmsService.insertSysSms(sysSms));
    }

    /**
     * 修改短信记录
     */
    @PreAuthorize("@ss.hasPermi('system:sms:edit')")
    @Log(title = "短信记录", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody SysSms sysSms) {
        return toAjax(sysSmsService.updateSysSms(sysSms));
    }

    /**
     * 删除短信记录
     */
    @PreAuthorize("@ss.hasPermi('system:sms:remove')")
    @Log(title = "短信记录", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{smsId}")
    public AjaxResult remove(@PathVariable String smsId) {
        return toAjax(sysSmsService.deleteSysSmsBySmsId(smsId));
    }


	/**
	 * 发送验证码
     * @title sendCode
	 * @param loginName
     * @param request 用于获取ip
     * @return com.jkr.framework.web.domain.AjaxResult
	 * <AUTHOR>
	 * @since 2021-08-21
	 */
    @GetMapping("/send/code")
	public AjaxResult sendCode(String loginName,HttpServletRequest request) throws Exception {
		return success(sysSmsService.sendCode(loginName));
	}
}
