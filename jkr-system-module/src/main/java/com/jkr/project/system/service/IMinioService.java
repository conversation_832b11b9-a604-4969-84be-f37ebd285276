package com.jkr.project.system.service;

import com.jkr.project.system.domain.vo.MinioInfo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 服务器文件管理
 * <AUTHOR>
 * @date 2025/1/13
 */
public interface IMinioService {
    MinioInfo upload(MultipartFile file, String filePath);

    boolean delete(String bucketName, String fileName);

    MinioInfo download(String bucketName, String fileName, HttpServletResponse response);

    boolean allDelete(String bucketName, String fileName, Long sysFileId);
}
