package com.jkr.project.system.service.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.github.yitter.idgen.YitIdHelper;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.constant.Constants;
import com.jkr.common.constant.SysConfigConstants;
import com.jkr.common.constant.UserConstants;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import com.jkr.common.utils.bean.BeanValidators;
import com.jkr.common.utils.spring.SpringUtils;
import com.jkr.framework.aspectj.lang.annotation.DataScope;
import com.jkr.framework.redis.RedisCache;
import com.jkr.project.system.domain.*;
import com.jkr.project.system.mapper.*;
import com.jkr.project.system.service.*;
import jakarta.annotation.Resource;
import jakarta.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl implements ISysUserService {
	private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);
	@Resource
	protected Validator validator;
	@Resource
	private SysUserMapper userMapper;
	@Resource
	private SysRoleMapper roleMapper;
	@Resource
	private SysPostMapper postMapper;
	@Resource
	private SysUserRoleMapper userRoleMapper;
	@Resource
	private SysUserPostMapper userPostMapper;
	@Resource
	private ISysConfigService configService;
	@Resource
	private ISysDeptService deptService;
	@Resource
	private ISysPasswordHistoryService sysPasswordHistoryService;
	@Autowired
	private RedisCache redisCache;
	@Resource
	private ISysWechatService sysWechatService;


    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUserList(SysUser user) {
        return userMapper.selectUserList(user);
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectAllocatedList(SysUser user) {
        return userMapper.selectAllocatedList(user);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<SysUser> selectUnallocatedList(SysUser user) {
        return userMapper.selectUnallocatedList(user);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRole> list = roleMapper.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPost> list = postMapper.selectPostsByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(SysPost::getPostName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUser user) {
        SysUser info = userMapper.checkUserNameUnique(user);
        if (StringUtils.isNotNull(info)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkPhoneUnique(SysUser user) {
        SysUser info = userMapper.checkPhoneUnique(user);
        if (StringUtils.isNotNull(info)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return
     */
    @Override
    public boolean checkEmailUnique(SysUser user) {
        SysUser info = userMapper.checkEmailUnique(user);
        if (StringUtils.isNotNull(info)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    @Override
    public void checkUserAllowed(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && user.isAdmin()) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (!SysUser.isAdmin(SecurityUtils.getUserId())) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            List<SysUser> users = SpringUtils.getAopProxy(this).selectUserList(user);
            if (StringUtils.isEmpty(users)) {
                throw new ServiceException("没有权限访问用户数据！");
            }
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertUser(SysUser user) {
        // 新增用户信息
        int rows = userMapper.insertUser(user);
        // 新增用户岗位关联
        insertUserPost(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUser user) {
        return userMapper.insertUser(user) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateUser(SysUser user) {
        Long userId = user.getUserId();
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 新增用户与角色管理
        insertUserRole(user);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPostByUserId(userId);
        // 新增用户与岗位管理
        insertUserPost(user);
        return userMapper.updateUser(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional
    public void insertUserAuth(Long userId, Long[] roleIds) {
        userRoleMapper.deleteUserRoleByUserId(userId);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userMapper.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(SysUser user) {
        return userMapper.updateUser(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(long userId, String userName, String password) {
        // 获取修改前的数据
        SysUser user = selectUserById(userId);
        String oldPassword = user.getPassword();
        // 更新密码
        int row = userMapper.resetUserPwd(userName, password);
        if (row > 0) {
            // 将旧密码保存到历史表
            SysPasswordHistory history = new SysPasswordHistory();
            history.setId(IdWorker.getId());
            history.setUserId(userId);
            history.setPassword(oldPassword);
            sysPasswordHistoryService.insertSysPasswordHistory(history);
        }

        return row;
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(SysUser user) {
        this.insertUserRole(user.getUserId(), user.getRoleIds());
    }

    /**
     * 新增用户岗位信息
     *
     * @param user 用户对象
     */
    public void insertUserPost(SysUser user) {
        Long[] posts = user.getPostIds();
        if (StringUtils.isNotEmpty(posts)) {
            // 新增用户与岗位管理
            List<SysUserPost> list = new ArrayList<SysUserPost>(posts.length);
            for (Long postId : posts) {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                list.add(up);
            }
            userPostMapper.batchUserPost(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(Long userId, Long[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            // 新增用户与角色管理
            List<SysUserRole> list = new ArrayList<SysUserRole>(roleIds.length);
            for (Long roleId : roleIds) {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleMapper.batchUserRole(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.deleteUserRoleByUserId(userId);
        // 删除用户与岗位表
        userPostMapper.deleteUserPostByUserId(userId);
        return userMapper.deleteUserById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(new SysUser(userId));
            checkUserDataScope(userId);
        }
        // 删除用户与角色关联
        userRoleMapper.deleteUserRole(userIds);
        // 删除用户与岗位关联
        userPostMapper.deleteUserPost(userIds);
        return userMapper.deleteUserByIds(userIds);
    }

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @return 结果
     */
    @Override
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SysUser user : userList) {
            try {
                // 验证是否存在这个用户
                SysUser u = userMapper.selectUserByUserName(user.getUserName());
                if (StringUtils.isNull(u)) {
                    BeanValidators.validateWithException(validator, user);
                    deptService.checkDeptDataScope(user.getDeptId());
                    String password = configService.selectConfigByKey(SysConfigConstants.SYS_USER_INIT_PASSWORD);
                    user.setPassword(SecurityUtils.encryptPassword(password));
                    user.setCreateBy(operName);
                    user.setDelFlag(Constants.DEFAULT_DELETE_FLAG);
                    userMapper.insertUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, user);
                    checkUserAllowed(u);
                    checkUserDataScope(u.getUserId());
                    deptService.checkDeptDataScope(user.getDeptId());
                    user.setUserId(u.getUserId());
                    user.setUpdateBy(operName);
                    userMapper.updateUser(user);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getUserName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getUserName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getUserName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 通过手机号查询用户
     *
     * @param userName 手机号
     * @return 用户对象信息
     */
    public SysUser selectUserByUserNameOrMobile(String userName) {
        return userMapper.selectUserByUserNameOrMobile(userName);
    }

    /**
     * 根据用户编号查询授权列表
     *
     * @param userId 用户编号
     * @return 授权列表
     */
    public List<SysAuthUser> selectAuthUserListByUserId(Long userId) {
        return userMapper.selectAuthUserListByUserId(userId);
    }

    /**
     * 通过微信登录code获取openId
     *
     * @param code 微信登录code
     * @return openId
     */
    public String getWxOpenId(String code) {
        String url = "https://api.weixin.qq.com/sns/jscode2session";
        String appid = SecurityUtils.getWechatAppIdFromHeader();
        if (StringUtils.isEmpty(appid)) {
            return "";
        }
        String secret = sysWechatService.getSecretByAppid(appid);
        if (StringUtils.isEmpty(secret)) {
            return "";
        }
        String requestUrl = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("appid", appid)
                .queryParam("secret", secret)
                .queryParam("js_code", code)
                .queryParam("grant_type", "authorization_code")
                .toUriString();
        HttpResponse response = HttpUtil.createPost(requestUrl).execute();
        // 获取 session_key 和 openid
        JSONObject parseObj = JSONUtil.parseObj(response.body());
        return parseObj.getStr("openid");
    }

    /**
     * 获取微信 access_token
     *
     * @return JSONObject
     */
    public String getWxAccessToken() {
        String appid = SecurityUtils.getWechatAppIdFromHeader();
        if (StringUtils.isEmpty(appid)) {
            return "";
        }
        String secret = sysWechatService.getSecretByAppid(appid);
        if (StringUtils.isEmpty(secret)) {
            return "";
        }
        String wxAccessToken=redisCache.getCacheObject(getWeChatAccessTokenCacheKey(appid));
        if(StringUtils.isNotBlank(wxAccessToken)){
            return wxAccessToken;
        }
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential";
        String requestUrl = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("appid", appid)
                .queryParam("secret", secret)
                .toUriString();
        HttpResponse response = HttpUtil.createPost(requestUrl).execute();
        JSONObject parseObj = JSONUtil.parseObj(response.body());
        wxAccessToken=parseObj.getStr("access_token");
        //有效期默认7200，此处设置7000
        redisCache.setCacheObject(getWeChatAccessTokenCacheKey(appid), wxAccessToken, 7000, TimeUnit.SECONDS);
        return wxAccessToken;
    }

    /**
     * 获取微信用户手机号
     *
     * @return JSONObject
     */
    public String getWxUserphonenumber(String accessToken, String code) {
        String url = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";
        String requestUrl = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("access_token", accessToken)
                .toUriString();
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("code", code);
        String result = HttpUtil.post(requestUrl, JSONUtil.toJsonStr(map));
        JSONObject parseObj = JSONUtil.parseObj(result);
        if (!"0".equals(parseObj.get("errcode").toString())) {
            log.error("获取微信手机号请求错误:{}", parseObj);
            throw new ServiceException("获取微信手机号请求错误");
        }
        String phoneInfo = parseObj.get("phone_info").toString();
        JSONObject phoneInfojson = JSONUtil.parseObj(phoneInfo);
        return phoneInfojson.getStr("phoneNumber");
    }

    /**
     * 通过uuid 获取用户信息
     *
     * @return SysUser
     */
    public SysUser selectAuthUserByUuid(String uuid) {
        return userMapper.selectAuthUserByUuid(uuid);
    }

    /**
     * 新增第三方授权信息
     *
     * @param authUser 用户信息
     * @return 结果
     */
    @Transactional
    public int insertAuthUser(SysAuthUser authUser) {
        return userMapper.insertAuthUser(authUser);
    }

    /**
     * 创建wx用户
     *
     * @return SysUser
     */
    @Transactional
    public SysUser createWxUser(SysAuthUser sysAuthUser) {
        SysUser user = new SysUser();
        //默认机构id，此处可根据业务自行调整
        user.setDeptId(100l);
        String userName = YitIdHelper.nextId() + "";
        user.setUserName(userName);
        user.setNickName("微信用户");
        String password = configService.selectConfigByKey(SysConfigConstants.SYS_USER_INIT_PASSWORD);
        user.setPassword(SecurityUtils.encryptPassword(password));
        user.setPhone(sysAuthUser.getPhone());
        //默认角色id，此处可根据业务自行调整
        Long[] roleIds = {2L};
        user.setRoleIds(roleIds);
        this.insertUser(user);

        //创建第三方授权数据
        SysAuthUser auser = new SysAuthUser();
        auser.setUuid(sysAuthUser.getUuid());
        auser.setUserId(user.getUserId());
        auser.setLoginName(userName);
        auser.setSource("wechat");
        auser.setSourceId(configService.getWechatAppIdFromHeaderOrDefault());
        this.insertAuthUser(auser);
        return user;
    }

    @Override
    public SysUser getByPhone(String num) {
        return userMapper.getByPhone(num);
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getWeChatCacheKey(String configKey) {
        return CacheConstants.WECHAT + configKey;
    }
    private String getWeChatAccessTokenCacheKey(String configKey) {
        return CacheConstants.WECHAT + CacheConstants.WX_ACCESS_TOKEN + configKey;
    }
}
