package com.jkr.project.system.service;

import com.jkr.project.system.domain.SysPasswordHistory;

import java.util.List;

/**
 * ISysPasswordHistoryService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-14 18:41
 */
public interface ISysPasswordHistoryService {
	/**
	 * 查询密码历史列表
	 *
	 * @param sysPasswordHistory 密码历史
	 * @return 密码历史集合
	 */
	List<SysPasswordHistory> selectSysPasswordHistoryList(SysPasswordHistory sysPasswordHistory);

	/**
	 * 新增密码历史
	 *
	 * @param sysPasswordHistory 密码历史
	 * @return 结果
	 */
	int insertSysPasswordHistory(SysPasswordHistory sysPasswordHistory);

	/**
	 * 检查新密码是否与历史密码相同
	 *
	 * @param userId      用户ID
	 * @param newPassword 新密码
	 * @return true：与某个历史密码相同，false：没有相同的历史密码
	 */
	boolean passwordCheck(Long userId, String newPassword);
}
