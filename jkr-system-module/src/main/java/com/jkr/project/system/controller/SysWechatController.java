package com.jkr.project.system.controller;

import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.SysWechat;
import com.jkr.project.system.service.ISysWechatService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信小程序信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@RestController
@RequestMapping("/system/wechat")
public class SysWechatController extends BaseController {
	@Autowired
	private ISysWechatService wechatService;

/**
 * 查询微信小程序信息列表
 */
@PreAuthorize("@ss.hasPermi('system:wechat:list')")
@GetMapping("/list")
	public TableDataInfo list(SysWechat wechat) {
		startPage();
		List<SysWechat> list = wechatService.selectWechatList(wechat);
		return getDataTable(list);
	}

	/**
	 * 导出微信小程序信息列表
	 */
	@PreAuthorize("@ss.hasPermi('system:wechat:export')")
	@Log(title = "导出微信小程序信息列表", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysWechat wechat) {
		List<SysWechat> list = wechatService.selectWechatList(wechat);
		ExcelUtil<SysWechat> util = new ExcelUtil<SysWechat>(SysWechat. class);
		util.exportExcel(response, list, "微信小程序信息数据");
	}

	/**
	 * 获取微信小程序信息详细信息
	 */
	@PreAuthorize("@ss.hasPermi('system:wechat:query')")
	@GetMapping(value = "/info/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id) {
		return success(wechatService.selectWechatById(id));
	}

	/**
	 * 新增微信小程序信息
	 */
	@PreAuthorize("@ss.hasPermi('system:wechat:add')")
	@Log(title = "新增微信小程序信息", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@Validated @RequestBody SysWechat wechat) {
		return toAjax(wechatService.insertWechat(wechat));
	}

	/**
	 * 修改微信小程序信息
	 */
	@PreAuthorize("@ss.hasPermi('system:wechat:edit')")
	@Log(title = "修改微信小程序信息", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@Validated @RequestBody SysWechat wechat) {
		return toAjax(wechatService.updateWechat(wechat));
	}

	/**
	 * 删除微信小程序信息
	 */
	@PreAuthorize("@ss.hasPermi('system:wechat:remove')")
	@Log(title = "删除微信小程序信息", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{id}")
	public AjaxResult remove(@PathVariable Long id) {
		return toAjax(wechatService.deleteWechatById(id));
	}

	/**
	 * 批量删除微信小程序信息
	 */
	@PreAuthorize("@ss.hasPermi('system:wechat:batchRemove')")
	@Log(title = "批量删除微信小程序信息", businessType = BusinessType.DELETE)
	@PostMapping("/batchRemove")
	public AjaxResult batchRemove(@RequestBody SysWechat wechat) {
		return toAjax(wechatService.deleteWechatByIds(wechat.getIds()));
	}
}
