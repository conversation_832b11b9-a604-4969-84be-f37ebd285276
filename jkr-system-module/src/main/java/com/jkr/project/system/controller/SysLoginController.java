package com.jkr.project.system.controller;

import com.jkr.common.utils.SecurityUtils;
import com.jkr.framework.security.LoginBody;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.security.service.*;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysMenu;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.ISysMenuService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController {
	@Resource
	private SysLoginService loginService;

	@Resource
	private ISysMenuService menuService;

	@Resource
	private SysPermissionService permissionService;

	@Resource
	private TokenService tokenService;

	/**
	 * 登录方法
	 * 根据密码历史表判断是否首次登录，若是首次登录则在前端强制修改密码
	 *
	 * @param loginBody 登录信息
	 * @return 结果
	 */
	@PostMapping("/login")
	public AjaxResult login(@Validated @RequestBody LoginBody loginBody) {
		return loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid());
	}

	/**
	 * 获取用户信息
	 *
	 * @return 用户信息
	 */
	@GetMapping("/getInfo")
	public AjaxResult getInfo() {
		LoginUser loginUser = SecurityUtils.getLoginUser();
		SysUser user = loginUser.getUser();
		// 角色集合
		Set<String> roles = permissionService.getRolePermission(user);
		// 权限集合
		Set<String> permissions = permissionService.getMenuPermission(user);
		if (!loginUser.getPermissions().equals(permissions)) {
			loginUser.setPermissions(permissions);
			tokenService.refreshToken(loginUser);
		}
		AjaxResult ajax = AjaxResult.success();
		ajax.put("user", user);
		ajax.put("roles", roles);
		ajax.put("permissions", permissions);
		return ajax;
	}

	/**
	 * 获取路由信息
	 *
	 * @return 路由信息
	 */
	@GetMapping("/getRouters")
	public AjaxResult getRouters() {
		Long userId = SecurityUtils.getUserId();
		List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
		return AjaxResult.success(menuService.buildMenus(menus));
	}
}
