package com.jkr.project.system.controller;

import com.jkr.common.constant.SysConfigConstants;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.desensitize.annotation.ApiDesensitize;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.*;
import com.jkr.project.system.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
	@Resource
	private ISysUserService userService;

	@Resource
	private ISysRoleService roleService;

	@Resource
	private ISysDeptService deptService;

	@Resource
	private ISysPostService postService;
	@Resource
	private ISysConfigService configService;

	/**
	 * 获取用户列表
	 */
	@ApiDesensitize
	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysUser user) {
		startPage();
		List<SysUser> list = userService.selectUserList(user);
		return getDataTable(list);
	}

	/**
	 * 用户管理-导出
	 *
	 * @param response
	 * @param user
	 */
	@Log(title = "用户管理", businessType = BusinessType.EXPORT)
	@PreAuthorize("@ss.hasPermi('system:user:export')")
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysUser user) {
		List<SysUser> list = userService.selectUserList(user);
		ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
		util.exportExcel(response, list, "用户数据");
	}

	/**
	 * 用户管理 - 导入
	 *
	 * @param file
	 * @param updateSupport
	 * @return com.jkr.framework.web.domain.AjaxResult
	 */
	@Log(title = "用户管理", businessType = BusinessType.IMPORT)
	@PreAuthorize("@ss.hasPermi('system:user:import')")
	@PostMapping("/importData")
	public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
		ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
		List<SysUser> userList = util.importExcel(file.getInputStream());
		String operName = getUsername();
		String message = userService.importUser(userList, updateSupport, operName);
		return success(message);
	}

	/**
	 * 下载模版
	 *
	 * @param response
	 */
	@PostMapping("/importTemplate")
	public void importTemplate(HttpServletResponse response) {
		ExcelUtil<SysUser> util = new ExcelUtil<>(SysUser.class);
		util.importTemplateExcel(response, "用户数据");
	}

	/**
	 * 根据用户编号获取详细信息
	 */
	@PreAuthorize("@ss.hasPermi('system:user:query')")
	@GetMapping(value = {"/", "/{userId}"})
	public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId) {
		AjaxResult ajax = AjaxResult.success();
		if (StringUtils.isNotNull(userId)) {
			userService.checkUserDataScope(userId);
			SysUser sysUser = userService.selectUserById(userId);
			ajax.put(AjaxResult.DATA_TAG, sysUser);
			ajax.put("postIds", postService.selectPostListByUserId(userId));
			ajax.put("roleIds", sysUser.getRoles().stream().map(SysRole::getRoleId).collect(Collectors.toList()));
		}
		List<SysRole> roles = roleService.selectRoleAll();
		ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
		ajax.put("posts", postService.selectPostAll());
		return ajax;
	}

	/**
	 * 新增用户
	 */
	@PreAuthorize("@ss.hasPermi('system:user:add')")
	@Log(title = "用户管理", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public AjaxResult add(@Validated @RequestBody SysUser user) {
		deptService.checkDeptDataScope(user.getDeptId());
		roleService.checkRoleDataScope(user.getRoleIds());
		if (!userService.checkUserNameUnique(user)) {
			return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
		} else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user)) {
			return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
		} else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
			return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
		}
		user.setCreateBy(getUsername());
		String password = configService.selectConfigByKey(SysConfigConstants.SYS_USER_INIT_PASSWORD);
		user.setPassword(SecurityUtils.encryptPassword(password));
		int row = userService.insertUser(user);
		if (row > 0) {
			return AjaxResult.success(password);
		} else {
			return AjaxResult.error();
		}
	}

	/**
	 * 修改用户
	 */
	@PreAuthorize("@ss.hasPermi('system:user:edit')")
	@Log(title = "用户管理", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult edit(@Validated @RequestBody SysUser user) {
		userService.checkUserAllowed(user);
		userService.checkUserDataScope(user.getUserId());
		deptService.checkDeptDataScope(user.getDeptId());
		roleService.checkRoleDataScope(user.getRoleIds());
		if (!userService.checkUserNameUnique(user)) {
			return error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
		} else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user)) {
			return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
		} else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
			return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
		}
		user.setUpdateBy(getUsername());
		return toAjax(userService.updateUser(user));
	}

	/**
	 * 删除用户
	 */
	@PreAuthorize("@ss.hasPermi('system:user:remove')")
	@Log(title = "用户管理", businessType = BusinessType.DELETE)
	@PostMapping("remove/{userIds}")
	public AjaxResult remove(@PathVariable Long[] userIds) {
		if (ArrayUtils.contains(userIds, getUserId())) {
			return error("当前用户不能删除");
		}
		return toAjax(userService.deleteUserByIds(userIds));
	}

	/**
	 * 重置密码
	 */
	@PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
	@Log(title = "用户管理", businessType = BusinessType.UPDATE)
	@PostMapping("/resetPwd")
	public AjaxResult resetPwd(@RequestBody SysUser user) {
		userService.checkUserAllowed(user);
		userService.checkUserDataScope(user.getUserId());
		String password = configService.selectConfigByKey(SysConfigConstants.SYS_USER_INIT_PASSWORD);
		user.setPassword(SecurityUtils.encryptPassword(password));
		user.setUpdateBy(getUsername());
		user.setDefaultPassword(0);
		int row = userService.resetPwd(user);
		if (row > 0) {
			return AjaxResult.success(password);
		} else {
			return AjaxResult.error("修改失败");
		}
	}

	/**
	 * 状态修改
	 */
	@PreAuthorize("@ss.hasPermi('system:user:edit')")
	@Log(title = "用户管理", businessType = BusinessType.UPDATE)
	@PostMapping("/changeStatus")
	public AjaxResult changeStatus(@RequestBody SysUser user) {
		userService.checkUserAllowed(user);
		userService.checkUserDataScope(user.getUserId());
		user.setUpdateBy(getUsername());
		return toAjax(userService.updateUserStatus(user));
	}

	/**
	 * 根据用户编号获取授权角色
	 */
	@PreAuthorize("@ss.hasPermi('system:user:query')")
	@GetMapping("/authRole/{userId}")
	public AjaxResult authRole(@PathVariable("userId") Long userId) {
		AjaxResult ajax = AjaxResult.success();
		SysUser user = userService.selectUserById(userId);
		List<SysRole> roles = roleService.selectRolesByUserId(userId);
		ajax.put("user", user);
		ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
		return ajax;
	}

	/**
	 * 用户授权角色
	 */
	@PreAuthorize("@ss.hasPermi('system:user:edit')")
	@Log(title = "用户管理", businessType = BusinessType.GRANT)
	@PostMapping("/authRole")
	public AjaxResult insertAuthRole(@RequestBody SysUser sysUser) {
		userService.checkUserDataScope(sysUser.getUserId());
		roleService.checkRoleDataScope(sysUser.getRoleIds());
		userService.insertUserAuth(sysUser.getUserId(), sysUser.getRoleIds());
		return success();
	}

	/**
	 * 获取机构树列表
	 */
	@PreAuthorize("@ss.hasPermi('system:user:list')")
	@GetMapping("/deptTree")
	public AjaxResult deptTree(SysDept dept) {
		return success(deptService.selectDeptTreeList(dept));
	}


}
