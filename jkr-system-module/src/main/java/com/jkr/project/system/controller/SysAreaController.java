package com.jkr.project.system.controller;

import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.SysArea;
import com.jkr.project.system.service.ISysAreaService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 区域管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/system/area")
public class SysAreaController extends BaseController {
	@Autowired
	private ISysAreaService sysAreaService;

	/**
	 * 查询区域管理列表
	 */
	@PreAuthorize("@ss.hasPermi('system:area:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysArea sysArea) {
		List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
		return getDataTable(list);
	}

	/**
	 * 导出区域管理列表
	 */
	@PreAuthorize("@ss.hasPermi('system:area:export')")
	@Log(title = "区域管理", businessType = BusinessType.EXPORT)
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysArea sysArea) {
		List<SysArea> list = sysAreaService.selectSysAreaList(sysArea);
		ExcelUtil<SysArea> util = new ExcelUtil<SysArea>(SysArea.class);
		util.exportExcel(response, list, "区域管理数据");
	}

	/**
	 * 获取区域管理详细信息
	 */
	@PreAuthorize("@ss.hasPermi('system:area:query')")
	@GetMapping(value = "/info/{areaId}")
	public AjaxResult getInfo(@PathVariable("areaId") String areaId) {
		return success(sysAreaService.selectSysAreaByAreaId(areaId));
	}

	/**
	 * 新增区域管理
	 */
	@PreAuthorize("@ss.hasPermi('system:area:add')")
	@Log(title = "区域管理", businessType = BusinessType.INSERT)
	@PostMapping(value = "/add")
	public AjaxResult add(@RequestBody SysArea sysArea) {
		return toAjax(sysAreaService.insertSysArea(sysArea));
	}

	/**
	 * 修改区域管理
	 */
	@PreAuthorize("@ss.hasPermi('system:area:edit')")
	@Log(title = "区域管理", businessType = BusinessType.UPDATE)
	@PostMapping(value = "/edit")
	public AjaxResult edit(@RequestBody SysArea sysArea) {
		return toAjax(sysAreaService.updateSysArea(sysArea));
	}

	/**
	 * 删除区域管理
	 */
	@PreAuthorize("@ss.hasPermi('system:area:remove')")
	@Log(title = "区域管理", businessType = BusinessType.DELETE)
	@PostMapping(value = "/remove/{areaId}")
	public AjaxResult remove(@PathVariable String areaId) {
		return toAjax(sysAreaService.deleteSysAreaByAreaId(areaId));
	}

	/**
	 * 获取区划树形数据
	 */
	@GetMapping("/getSysAreaTreeData")
	public AjaxResult getSysAreaTreeData() {
		return success(sysAreaService.getSysAreaTreeData());
	}
}
