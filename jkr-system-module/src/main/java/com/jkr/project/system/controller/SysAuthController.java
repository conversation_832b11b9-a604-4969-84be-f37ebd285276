package com.jkr.project.system.controller;

import cn.hutool.json.JSONObject;
import com.jkr.common.constant.Constants;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.security.service.SysLoginService;
import com.jkr.framework.security.service.SysPermissionService;
import com.jkr.framework.security.service.TokenService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysAuthUser;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.service.ISysConfigService;
import com.jkr.project.system.service.ISysSmsService;
import com.jkr.project.system.service.ISysUserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 第三方认证授权处理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/system/auth")
public class SysAuthController extends BaseController {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @Resource
    private ISysConfigService configService;

    /**
     * 获取微信openId
     *
     * @param code
     */
    @GetMapping("/wechat/getWxOpenId")
    public AjaxResult wechatGetWxOpenId(String code, HttpServletRequest request) {
        String openId = userService.getWxOpenId(code);
        return success(openId);
    }
    /**
     * 获取微信用户手机号
     *
     * @param code
     */
    @GetMapping("/wechat/getWxUserPhoneNumber")
    public AjaxResult wechatGetWxUserPhoneNumber( String code, HttpServletRequest request) {
        String accessToken=userService.getWxAccessToken();
        String phone=userService.getWxUserphonenumber(accessToken,code).toString();
        return success(phone);
    }
    /**
     * 微信登录
     *
     * @param sysAuthUser
     */
    @PostMapping("/wechat/login")
    public AjaxResult wechatLogin(@RequestBody SysAuthUser sysAuthUser,HttpServletRequest request) {
        if (StringUtils.isNull(sysAuthUser.getUuid())) {
            return AjaxResult.error("缺少微信平台用户唯一ID");
        }
        //校验短信验证码
        sysLoginService.validateSmsCode(sysAuthUser.getPhone(), sysAuthUser.getCode());
        SysUser user;
        SysUser authUser = userService.selectAuthUserByUuid(sysAuthUser.getUuid());
        //判断是否有绑定数据
        if (StringUtils.isNotNull(authUser)) {
            user = userService.selectUserById(authUser.getUserId());
        }else{
            //创建微信用户数据
            user = userService.createWxUser(sysAuthUser);
        }
        //生成token
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
        String token = tokenService.createMobileToken(loginUser);
        JSONObject userInfo = new JSONObject();
        userInfo.put("id", loginUser.getUserId());
        userInfo.put("userTypeCode", "2");
        userInfo.put("openId", sysAuthUser.getUuid());
        userInfo.put("phoneNumber", sysAuthUser.getPhone());
        userInfo.put("name", user.getUserName());
        return success().put(Constants.TOKEN, token).put("userInfo", userInfo);
    }
}
