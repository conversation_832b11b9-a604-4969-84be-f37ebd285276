package com.jkr.project.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.google.gson.Gson;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.*;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import com.qiniu.common.Zone;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.common.QiniuException;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;

/**
 * 服务器文件管理
 * <AUTHOR>
 * @date 2025/1/13
 */
@Service
@Slf4j
public class QiniuService implements com.jkr.project.system.service.IQiniuService {

    @Value("${qiniu.accessKey}")
    private String accessKey;
    @Value("${qiniu.secretKey}")
    private String secretKey;
    @Value("${qiniu.bucket}")
    private String bucket;
    @Value("${web.maxUploadSize}")
    private long maxUploadSize;
    @Autowired
    private ISysFileService fileService;

    @Override
    public boolean delete(String filePath) {
        try {
            Configuration cfg = new Configuration(Zone.zone1());
            Auth auth = Auth.create(accessKey, secretKey);
            BucketManager bucketManager = new BucketManager(auth, cfg);
            try {
                bucketManager.delete(bucket, filePath);
            } catch (QiniuException ex) {
                System.err.println(ex.code());
                System.err.println(ex.response.toString());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public String getQiniuAuthToken() {
        //...生成上传凭证，七牛默认token有效时间为1小时
        String upToken = (String) CacheUtils.get("QiniuAuthToken");
        if(StringUtils.isBlank(upToken)){
            Auth auth = Auth.create(accessKey, secretKey);
            upToken = auth.uploadToken(bucket);
            CacheUtils.put("QiniuAuthToken", upToken);
            return upToken;
        }
        String token= upToken.substring(upToken.lastIndexOf(":")+1,upToken.length());
        String json = Base64Util.decodeData(token);
        JSONObject jsonObject = JSONObject.parseObject(json);
        long qiniuDate = jsonObject.getLong("deadline");
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(new Date());
        cal2.add(Calendar.MINUTE, 20);//当前时间往后推20分钟
        long tempDate = cal2.getTimeInMillis()/1000;
        if(tempDate>qiniuDate) {
            Auth auth = Auth.create(accessKey, secretKey);
            upToken = auth.uploadToken(bucket);
            CacheUtils.put("QiniuAuthToken", upToken);
        }
        return upToken;
    }

    @Override
    public SysFile uploadToQiniu(MultipartFile file, String tblId, String tblName, String type) {
        if (file.getSize() > maxUploadSize) {
            throw new ServiceException("您上传的文件大小已经超出范围");
        }
        //文件路径
        String relativePath = new StringBuffer()
                .append(DateUtils.dateTimeNow("yyyy"))
                .append("-").append(DateUtils.dateTimeNow("MM"))
                .append("-").append(DateUtils.dateTimeNow("dd"))//日期
                .append("/").append(tblName)//表名
                //.append("/").append(tblId)//表ID
                .toString();
        SysFile att = new SysFile();
        Configuration cfg = new Configuration(Zone.zone1());//华北
        //参数
        UploadManager uploadManager = new UploadManager(cfg);
        // 如果不指定 key 值，以文件内容的 hash 值作为文件名
        // 获取原文件名的后缀
        String originalFilename = file.getOriginalFilename();
        String extName = originalFilename.substring(originalFilename.lastIndexOf("."));
        String key = relativePath+"/"+ IdGen.nextId() + extName;
        try {
            byte[] uploadBytes = file.getBytes();
            String upToken = getQiniuAuthToken();
            Response response = uploadManager.put(uploadBytes, key, upToken);
            //解析上传成功的结果
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            att.setTableId(Long.parseLong(tblId));
            att.setTableName(tblName);
            att.setPath(putRet.key);
            att.setName(file.getOriginalFilename());
            att.setRealName(file.getOriginalFilename());
            att.setSize((int) file.getSize());
            att.setType(type);
            att.setSourceType("1");
            fileService.insertSysFile(att);
        } catch (Exception e) {
            log.error("上传文件失败", e);
        }
        return att;
    }

    @Override
    public String uploadCkeditor(MultipartFile file) {
        Configuration cfg = new Configuration(Zone.zone1());//华北
        UploadManager uploadManager = new UploadManager(cfg);
        // 如果不指定 key 值，以文件内容的 hash 值作为文件名
        String originalFilename = file.getOriginalFilename();
        String extName = originalFilename.substring(originalFilename.lastIndexOf("."));
        String key = IdGen.nextId() + extName;
        try {
            byte[] uploadBytes = file.getBytes();
            String upToken = getQiniuAuthToken();
            Response response = uploadManager.put(uploadBytes, key, upToken);
            //解析上传成功的结果
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            return putRet.key;
        } catch (Exception e) {
            log.error("上传文件失败", e);
        }
        return "";
    }

    @Override
    public String uploadQiNiuSeal(MultipartFile file, String extName) {
        Configuration cfg = new Configuration(Zone.zone1());//华北
        UploadManager uploadManager = new UploadManager(cfg);
        // 如果不指定 key 值，以文件内容的 hash 值作为文件名
        String key = extName;
        try {
            byte[] uploadBytes = file.getBytes();
            String upToken = getQiniuAuthToken();
            Response response = uploadManager.put(uploadBytes, key, upToken);
            //解析上传成功的结果
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            return putRet.key;
        } catch (Exception e) {
            log.error("上传文件失败", e);
        }
        return "";
    }
}
