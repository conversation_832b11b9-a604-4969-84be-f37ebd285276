package com.jkr.project.system.service.impl;

import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.FileValidatorUtils;
import com.jkr.common.utils.file.FileUtils;
import com.jkr.project.system.domain.vo.MinioInfo;
import com.jkr.project.system.mapper.SysFileMapper;
import io.minio.*;
import io.minio.http.Method;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;


/**
 * 服务器文件管理
 * <AUTHOR>
 * @date 2025/1/13
 */
@Service
@Slf4j
public class MinioService implements com.jkr.project.system.service.IMinioService {
    private final MinioClient minioClient;
    @Autowired
    private SysFileMapper sysFileMapper;

    @Value("${com.jkr.minio.bucketName}")
    private String bucketName;
    @Autowired
    FileValidatorUtils fileValidatorUtils;

    public MinioService(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    /**
     * 上传文件
     * <AUTHOR>
     * @date 2025/1/13
     * @param file
     * @param filePath
     * @return com.jkr.project.system.domain.vo.MinioInfo
     */
    @Override
    public MinioInfo upload(MultipartFile file, String filePath) {
      /*  try {
            fileValidatorUtils.validateFile(file);
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }*/
        if (StringUtils.isNoneEmpty(bucketName)) {
            try (InputStream in = file.getInputStream()) {
                // bucket检查处理
                if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build())) {
                    minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                }
                // 处理文件名
                String fileName = file.getOriginalFilename();
                fileName = FileUtils.formatFileName(bucketName, filePath + "/" + fileName);
                String fileSize = FileUtils.formatFileSize(in.available());
                //获取上传后的文件名 仅文件 不涵路径
                PutObjectArgs objectArgs = PutObjectArgs.builder().object(fileName)
                        .bucket(bucketName)
                        .stream(in, in.available(), -1).build();
                minioClient.putObject(objectArgs);
                return new MinioInfo(bucketName, fileName, fileSize);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }

    /**
     * 删除文件
     * <AUTHOR>
     * @date 2025/1/13
     * @param bucketName 存储桶名称
     * @param fileName   文件名称
     * @return boolean
     */
    @Override
    public boolean delete(String bucketName, String fileName) {
        try {
            RemoveObjectArgs removeBucketArgs = RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName).build();
            minioClient.removeObject(removeBucketArgs);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }


    /**
     * 文件下载
     * <AUTHOR>
     * @date 2025/1/13
     * @param bucketName
     * @param fileName
     * @param response
     * @return com.jkr.project.system.domain.vo.MinioInfo

     */
    @Override
    public MinioInfo download(String bucketName, String fileName, HttpServletResponse response) {
        InputStream in = null;
        try {
            //获取文件对象 stat原信息
            StatObjectArgs statObjectArgs = StatObjectArgs
                    .builder()
                    .bucket(bucketName)
                    .object(fileName).build();
            StatObjectResponse stat = minioClient.statObject(statObjectArgs);
            GetObjectArgs getObjectArgs = GetObjectArgs
                    .builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .build();
            in = minioClient.getObject(getObjectArgs);
            response.setContentType(stat.contentType());
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            return new MinioInfo(in, response.getOutputStream());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return null;
    }


    /**
     * 删除文件数据
     * <AUTHOR>
     * @date 2025/1/13
     * @param bucketName
     * @param fileName
     * @param sysFileId
     * @return boolean
     */
    @Override
    public boolean allDelete(String bucketName, String fileName, Long sysFileId) {
        try {
            RemoveObjectArgs removeBucketArgs = RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName).build();
            minioClient.removeObject(removeBucketArgs);
            sysFileMapper.deleteSysFileById(sysFileId);
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }
}
