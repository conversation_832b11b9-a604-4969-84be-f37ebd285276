package com.jkr.project.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 微信小程序信息对象 sys_wechat
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_wechat")
public class SysWechat extends BaseModel {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 微信小程序名称
     */
    @Excel(name = "微信小程序名称")
    private String name;

    /**
     * 微信appId
     */
    @Excel(name = "微信appId")
    private String wechatAppId;

    /**
     * 微信secret
     */
    @Excel(name = "微信secret")
    private String wechatAppSecret;

    /**
     * 主键集合
     */
    private List<Long> ids;
}
