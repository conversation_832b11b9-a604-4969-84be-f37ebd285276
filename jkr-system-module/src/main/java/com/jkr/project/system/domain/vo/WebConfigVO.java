/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */
package com.jkr.project.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 网站配置参数
 *
 * <AUTHOR>
 * @date 2025年01月16日 10:30:37
 */
@Data
public class WebConfigVO implements Serializable{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(name = "configKey",value = "配置类型")
    private String configKey;

    @ApiModelProperty(name = "pcCopyright",value = "版权声明")
    private String pcCopyright;

    @ApiModelProperty(name = "pcTitleContent",value = "系统标题文本")
    private String pcTitleContent;

    @ApiModelProperty(name = "pcTopBarTitleContent",value = "系统头部标题文本")
    private String pcTopBarTitleContent;

}
