package com.jkr.project.system.domain;

import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 短信记录对象 sys_sms
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysSms extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Long smsId;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 验证码
     */
    @Excel(name = "验证码")
    private String code;

    /**
     * 过期时间戳
     */
    @Excel(name = "过期时间戳")
    private Integer expiryTime;

    /**
     * 业务编码
     */
    @Excel(name = "业务编码")
    private String businessCode;

    /**
     * 用户ip地址
     */
    @Excel(name = "用户ip地址")
    private String ip;

    /**
     * 发送状态,发送失败0发送成功1
     */
    @Excel(name = "发送状态,发送失败0发送成功1")
    private String sendFlag;

    /**
     * 0未用1已用2失败
     */
    @Excel(name = "0未用1已用2失败")
    private String codeStatus;

    /**
     * 删除标志（默认为0，表示数据可用，所有值为时间戳的表示数据不可用）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("smsId", getSmsId())
                .append("phone", getPhone())
                .append("code", getCode())
                .append("expiryTime", getExpiryTime())
                .append("businessCode", getBusinessCode())
                .append("ip", getIp())
                .append("sendFlag", getSendFlag())
                .append("codeStatus", getCodeStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
