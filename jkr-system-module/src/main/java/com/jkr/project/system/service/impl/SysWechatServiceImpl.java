package com.jkr.project.system.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.framework.redis.RedisCache;
import com.jkr.project.system.domain.SysWechat;
import com.jkr.project.system.mapper.SysWechatMapper;
import com.jkr.project.system.service.ISysWechatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 微信小程序信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Service
@Transactional
public class SysWechatServiceImpl implements ISysWechatService {
	@Autowired
	private SysWechatMapper wechatMapper;
    @Autowired
    private RedisCache redisCache;

	/**
	 * 查询微信小程序信息
	 *
	 * @param id 微信小程序信息主键
	 * @return 微信小程序信息
	 */
	@Override
	public SysWechat selectWechatById(Long id) {
		return wechatMapper.selectWechatById(id);
	}

	/**
	 * 查询微信小程序信息列表
	 *
	 * @param wechat 微信小程序信息
	 * @return 微信小程序信息
	 */
	@Override
	public List<SysWechat> selectWechatList(SysWechat wechat) {
		return wechatMapper.selectWechatList(wechat);
	}

	/**
	 * 新增微信小程序信息
	 *
	 * @param wechat 微信小程序信息
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int insertWechat(SysWechat wechat) {
		wechat.insertInit(SecurityUtils.getLoginUser().getUsername());
		return wechatMapper.insertWechat(wechat);
	}

	/**
	 * 修改微信小程序信息
	 *
	 * @param wechat 微信小程序信息
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int updateWechat(SysWechat wechat) {
		wechat.updateInit(SecurityUtils.getLoginUser().getUsername());
		redisCache.deleteObject(getWechatSecretCacheKey(wechat.getWechatAppId()));
		return wechatMapper.updateWechat(wechat);
	}

	/**
	 * 批量删除微信小程序信息
	 *
	 * @param ids 需要删除的微信小程序信息主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteWechatByIds(List<Long> ids) {
		for (Long id : ids) {
			SysWechat wechat = wechatMapper.selectById(id);
			if (null != wechat && StrUtil.isNotEmpty(wechat.getWechatAppId())) {
				redisCache.deleteObject(getWechatSecretCacheKey(wechat.getWechatAppId()));
			}
		}
		return wechatMapper.logicRemoveByIds(ids);
		//return wechatMapper.deleteWechatByIds(ids);
	}

	/**
	 * 删除微信小程序信息信息
	 *
	 * @param id 微信小程序信息主键
	 * @return 结果
	 */
	@Override
	@Transactional(readOnly = false, rollbackFor = Exception.class)
	public int deleteWechatById(Long id) {
		SysWechat wechat = wechatMapper.selectById(id);
		if (null != wechat && StrUtil.isNotEmpty(wechat.getWechatAppId())) {
			redisCache.deleteObject(getWechatSecretCacheKey(wechat.getWechatAppId()));
		}
		return wechatMapper.logicRemoveById(id);
		//return wechatMapper.deleteWechatById(id);
	}

	/**
	 * 根据appid获取secret
	 *
	 * <AUTHOR>
	 * @date  2025/4/23 9:31
	 * @param appid  appid
	 * @return java.lang.String secret
	 **/
	public String getSecretByAppid(String appid) {
		if (redisCache.hasKey(getWechatSecretCacheKey(appid))) {
			return redisCache.getCacheObject(getWechatSecretCacheKey(appid));
		}
		SysWechat sysWechat = new SysWechat();
		sysWechat.setWechatAppId(appid);
		List<SysWechat> wechatList = selectWechatList(sysWechat);
		if (ArrayUtil.isEmpty(wechatList)) {
			return null;
		}
		redisCache.setCacheObject(getWechatSecretCacheKey(appid), wechatList.get(0).getWechatAppSecret());
		return wechatList.get(0).getWechatAppSecret();
	}


	private String getWechatSecretCacheKey(String configKey) {
		return CacheConstants.WECHAT + CacheConstants.WX_SECRET + configKey;
	}
}
