package com.jkr.project.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.github.yitter.idgen.YitIdHelper;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.constant.SysConfigConstants;
import com.jkr.common.enums.AreaTypeStatus;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.redis.RedisCache;
import com.jkr.project.system.domain.SysArea;
import com.jkr.project.system.mapper.SysAreaMapper;
import com.jkr.project.system.service.ISysAreaService;
import com.jkr.project.system.service.ISysConfigService;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 区域管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
public class SysAreaServiceImpl implements ISysAreaService {
    public static final String ROOT_PARENT_ID = "000000000000";
    @Autowired
    private SysAreaMapper sysAreaMapper;
    @Resource
    private ISysConfigService configService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询区域管理
     *
     * @param areaId 区域管理主键
     * @return 区域管理
     */
    @Override
    public SysArea selectSysAreaByAreaId(String areaId) {
        return sysAreaMapper.selectSysAreaByAreaId(areaId);
    }

    /**
     * 查询区域管理列表
     *
     * @param sysArea 区域管理
     * @return 区域管理
     */
    @Override
    public List<SysArea> selectSysAreaList(SysArea sysArea) {
        //行政区划父id逻辑处理，赋默认值
        if (StringUtils.isBlank(sysArea.getParentId())) {
            sysArea.setParentId(ROOT_PARENT_ID);
        }
        return sysAreaMapper.selectSysAreaList(sysArea);
    }

    /**
     * 新增区域管理
     *
     * @param sysArea 区域管理
     * @return 结果
     */
    @Override
    public int insertSysArea(SysArea sysArea) {
        sysArea.setAreaId(String.valueOf(YitIdHelper.nextId()));
        sysArea.setCreateTime(DateUtils.getNowDate());
        //行政区划父id逻辑处理，赋默认值
        String parentId = sysArea.getParentId();
        if (StringUtils.isBlank(parentId)) {
            parentId = ROOT_PARENT_ID;
        }
        //获取父对象
        SysArea parentSysArea = this.sysAreaMapper.selectSysAreaByAreaId(parentId);
        sysArea.setParentId(parentSysArea.getAreaId());
        sysArea.setParentIds(parentSysArea.getParentIds() + "," + parentSysArea.getAreaId());
        sysArea.setType(String.valueOf(Integer.parseInt(parentSysArea.getType()) + 1));

        //清除系统区划数据缓存
        clearSysAreaCache();
        return sysAreaMapper.insertSysArea(sysArea);
    }

    /**
     * 修改区域管理
     *
     * @param sysArea 区域管理
     * @return 结果
     */
    @Override
    public int updateSysArea(SysArea sysArea) {
        sysArea.setUpdateTime(DateUtils.getNowDate());
        //清除系统区划数据缓存
        clearSysAreaCache();
        return sysAreaMapper.updateSysArea(sysArea);
    }

    /**
     * 批量删除区域管理
     *
     * @param areaIds 需要删除的区域管理主键
     * @return 结果
     */
    @Override
    public int deleteSysAreaByAreaIds(String[] areaIds) {
        //清除系统区划数据缓存
        clearSysAreaCache();
        return sysAreaMapper.deleteSysAreaByAreaIds(areaIds);
    }

    /**
     * 删除区域管理信息
     *
     * @param areaId 区域管理主键
     * @return 结果
     */
    @Override
    public int deleteSysAreaByAreaId(String areaId) {
        //清除系统区划数据缓存
        clearSysAreaCache();
        //删除本级及下级数据
        return sysAreaMapper.deleteSysAreaByAreaId(areaId);
    }

    /**
     * 获取区划树形数据
     */
    public List<SysArea> getSysAreaTreeData() {
        //默认根节点编码
        String rootCode = configService.selectConfigByKey(SysConfigConstants.SYS_AREA_TREE_ROOT_CODE);
        Object JSONObj = redisCache.getCacheObject(getCacheKey(rootCode));
        String JSONStr = "";
        //判断缓存数据
        if (null != JSONObj && StringUtils.isNotEmpty(JSONObj.toString())) {
            JSONStr = JSON.toJSONString(redisCache.getCacheObject(getCacheKey(rootCode)));
            return JSONArray.parseArray(JSONStr, SysArea.class);
        }
        //设置缓存
        setSysAreaTreeCache();
        JSONStr = JSON.toJSONString(redisCache.getCacheObject(getCacheKey(rootCode)));
        return JSONArray.parseArray(JSONStr, SysArea.class);
    }

    /**
     * 设置区划树形数据缓存
     */
    public void setSysAreaTreeCache() {
        //默认根节点编码
        String rootCode = configService.selectConfigByKey(SysConfigConstants.SYS_AREA_TREE_ROOT_CODE);
        //区域类型集合
        List<String> areaTypeList = Lists.newArrayList();
        areaTypeList.add(AreaTypeStatus.PROVINCE.getCode());
        areaTypeList.add(AreaTypeStatus.CITY.getCode());
        areaTypeList.add(AreaTypeStatus.COUNTY.getCode());

        SysArea sysArea = new SysArea();
        sysArea.setCodePrefix(rootCode);
        sysArea.setTypeList(areaTypeList);
        List<SysArea> list = sysAreaMapper.selectSysAreaList(sysArea);
        List<SysArea> treeList = this.findChildList(list, "");
        redisCache.setCacheObject(getCacheKey(rootCode), treeList);

    }

    @Override
    public List<SysArea> findTreeList(SysArea area) {
        return sysAreaMapper.findTreeList(area);
    }

    /**
     * 获取行政区划子集合
     *
     * @param areaList
     * @param parentId
     * @return List<SysArea>
     * <AUTHOR>
     * @date 2025年1月15日14:49:12
     */
    public List<SysArea> findChildList(List<SysArea> areaList, String parentId) {
        List<SysArea> childList = null;
        if (StringUtils.isBlank(parentId)) {
            childList = areaList.stream().filter(re -> re.getType().equals(AreaTypeStatus.PROVINCE.getCode())).collect(Collectors.toList());
        } else {
            childList = areaList.stream().filter(re -> re.getParentId().equals(parentId)).collect(Collectors.toList());
        }
        for (SysArea area : childList) {
            area.setChildren(this.findChildList(areaList, area.getAreaId()));
        }
        return childList;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return CacheConstants.SYS_AREA_TREE_KEY + configKey;
    }

    /**
     * 清除系统区划数据缓存
     */
    private void clearSysAreaCache() {
        //清除系统区划数据缓存
        String rootCode = configService.selectConfigByKey(SysConfigConstants.SYS_AREA_TREE_ROOT_CODE);
        redisCache.deleteObject(getCacheKey(rootCode));
    }
}
