package com.jkr.project.system.mapper;

import java.util.List;

import com.jkr.project.system.domain.SysUserClient;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统账号与客户端关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Mapper
public interface SysUserClientMapper {
	/**
	 * 查询系统账号与客户端关联关系
	 *
	 * @param id 系统账号与客户端关联关系主键
	 * @return 系统账号与客户端关联关系
	 */
	public SysUserClient selectSysUserClientById(Long id);

	/**
	 * 查询系统账号与客户端关联关系列表
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 系统账号与客户端关联关系集合
	 */
	public List<SysUserClient> selectSysUserClientList(SysUserClient sysUserClient);

	/**
	 * 新增系统账号与客户端关联关系
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 结果
	 */
	public int insertSysUserClient(SysUserClient sysUserClient);

	/**
	 * 修改系统账号与客户端关联关系
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 结果
	 */
	public int updateSysUserClient(SysUserClient sysUserClient);

	/**
	 * 删除系统账号与客户端关联关系
	 *
	 * @param id 系统账号与客户端关联关系主键
	 * @return 结果
	 */
	public int deleteSysUserClientById(Long id);

	/**
	 * 批量删除系统账号与客户端关联关系
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteSysUserClientByIds(Long[] ids);

	/**
	 * 根据对象删除数据
	 *
	 * @param sysUserClient 对象
	 * @return 结果
	 */
	public int deleteSysUserClientByEntity(SysUserClient sysUserClient);

	/**
	 * 查询用户是否授权应用系统
	 *
	 * @param sysUserClient 用户id
	 * @return 结果
	 */
	public int hasAuthByUser(SysUserClient sysUserClient);
}
