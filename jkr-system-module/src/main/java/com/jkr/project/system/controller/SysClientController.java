package com.jkr.project.system.controller;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.system.domain.SysClient;
import com.jkr.project.system.service.ISysClientService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 客户端应用Controller
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/system/client")
public class SysClientController extends BaseController {
    @Autowired
    private ISysClientService sysClientService;

    /**
     * @title list
     * @Description: 查询客户端应用列表
     * @param sysClient
     * @return com.jkr.framework.web.page.TableDataInfo
     * <AUTHOR>
     * @date 2025/2/5 13:31
     */
    @PreAuthorize("@ss.hasPermi('system:client:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysClient sysClient) {
        startPage();
        List<SysClient> list = sysClientService.selectSysClientList(sysClient);
        return getDataTable(list);
    }

    /**
     * @title getInfo
     * @Description: 获取客户端应用详细信息
     * @param id
     * @return com.jkr.framework.web.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/2/5 13:36
     */
    @PreAuthorize("@ss.hasPermi('system:client:query')")
    @PostMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysClientService.selectSysClientById(id));
    }

    /**
     * @title add
     * @Description: 新增客户端应用
     * @param sysClient
     * @return com.jkr.framework.web.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/2/5 13:36
     */
    @PreAuthorize("@ss.hasPermi('system:client:add')")
    @Log(title = "客户端应用", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody SysClient sysClient) {
        return toAjax(sysClientService.insertSysClient(sysClient));
    }

    /**
     * @title edit
     * @Description: 修改客户端应用
     * @param sysClient
     * @return com.jkr.framework.web.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/2/5 13:36
     */
    @PreAuthorize("@ss.hasPermi('system:client:edit')")
    @Log(title = "客户端应用", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody SysClient sysClient) {
        return toAjax(sysClientService.updateSysClient(sysClient));
    }

    /**
     * @title remove
     * @Description: 删除客户端应用
     * @param clientId
     * @return com.jkr.framework.web.domain.AjaxResult
     * <AUTHOR>
     * @date 2025/2/5 13:36
     */
    @PreAuthorize("@ss.hasPermi('system:client:remove')")
    @Log(title = "客户端应用", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{clientId}")
    public AjaxResult remove(@PathVariable Long clientId) {
        return toAjax(sysClientService.deleteSysClientByClientId(clientId));
    }
}
