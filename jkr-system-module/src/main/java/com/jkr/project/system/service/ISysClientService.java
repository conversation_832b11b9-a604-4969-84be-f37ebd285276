package com.jkr.project.system.service;

import java.util.List;

import com.jkr.project.system.domain .SysClient;

/**
 * 客户端应用Service接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface ISysClientService {
	/**
	 * 查询客户端应用
	 *
	 * @param id 客户端应用主键
	 * @return 客户端应用
	 */
	public SysClient selectSysClientById(Long id);

	/**
	 * 查询客户端应用列表
	 *
	 * @param sysClient 客户端应用
	 * @return 客户端应用集合
	 */
	public List<SysClient> selectSysClientList(SysClient sysClient);

	/**
	 * 新增客户端应用
	 *
	 * @param sysClient 客户端应用
	 * @return 结果
	 */
	public int insertSysClient(SysClient sysClient);

	/**
	 * 修改客户端应用
	 *
	 * @param sysClient 客户端应用
	 * @return 结果
	 */
	public int updateSysClient(SysClient sysClient);

	/**
	 * 批量删除客户端应用
	 *
	 * @param ids 需要删除的客户端应用主键集合
	 * @return 结果
	 */
	public int deleteSysClientByClientIds(Long[] ids);

	/**
	 * 删除客户端应用信息
	 *
	 * @param id 客户端应用主键
	 * @return 结果
	 */
	public int deleteSysClientByClientId(Long id);
}
