package com.jkr.project.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.core.text.Convert;
import com.jkr.common.enums.ConfigKeyEnum;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.exception.sms.SmsException;
import com.jkr.common.exception.sms.SmsExpireException;
import com.jkr.common.exception.user.PhoneNotExistsException;
import com.jkr.common.enums.sms.SmsCodeStatusEnum;
import com.jkr.common.enums.sms.SmsSendEnum;
import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.sms.AliSMSSendUtil;
import com.jkr.common.utils.sms.CaptchaUtils;
import com.jkr.framework.redis.RedisCache;
import com.jkr.project.system.domain.SysSms;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.mapper.SysSmsMapper;
import com.jkr.project.system.mapper.SysUserMapper;
import com.jkr.project.system.service.ISysConfigService;
import com.jkr.project.system.service.ISysSmsService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 短信记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Service
public class SysSmsServiceImpl implements ISysSmsService {
    @Autowired
    private SysSmsMapper sysSmsMapper;
    @Autowired
    private SysUserMapper userMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private ISysConfigService sysConfigService;

	//验证码长度
    public static final Integer DEFAULT_SMS_LENGTH = 4;
    //验证码有效时长 单位分钟
    public static final Integer DEFAULT_SMS_VALIDITY_TIME = 5;

    public static final Integer MILLI_TIME= 1000; //毫秒

    public static final Integer MINUTE_TIME = 60; //毫秒
    @Autowired
    private AliSMSSendUtil aliSMSSendUtil;

    /**
     * 查询短信记录
     *
     * @param smsId 短信记录主键
     * @return 短信记录
     */
    @Override
    public SysSms selectSysSmsBySmsId(String smsId) {
        return sysSmsMapper.selectSysSmsBySmsId(smsId);
    }

    /**
     * 查询短信记录列表
     *
     * @param sysSms 短信记录
     * @return 短信记录
     */
    @Override
    public List<SysSms> selectSysSmsList(SysSms sysSms) {
        return sysSmsMapper.selectSysSmsList(sysSms);
    }

    /**
     * 新增短信记录
     *
     * @param sysSms 短信记录
     * @return 结果
     */
    @Override
    public int insertSysSms(SysSms sysSms) {
        sysSms.setCreateTime(DateUtils.getNowDate());
        return sysSmsMapper.insertSysSms(sysSms);
    }

    /**
     * 修改短信记录
     *
     * @param sysSms 短信记录
     * @return 结果
     */
    @Override
    public int updateSysSms(SysSms sysSms) {
        sysSms.setUpdateTime(DateUtils.getNowDate());
        return sysSmsMapper.updateSysSms(sysSms);
    }


    /**
     * 删除短信记录信息
     *
     * @param smsId 短信记录主键
     * @return 结果
     */
    @Override
    public int deleteSysSmsBySmsId(String smsId) {
        return sysSmsMapper.deleteSysSmsBySmsId(smsId);
    }

    /**
     * 发送短信验证码
     *
     * @param loginName 用户登录名
     * @return 结果
     */
    public Map sendCode(String loginName) {
        Map resultMap = new HashMap();
		//获取电话号码
        //根据loginName  判断是电话还是手机号  因为规则不一样 所以直接查
        // 用户名至少一个非数字字符
        String phone = loginName;
        SysUser sysUser = userMapper.selectUserByUserNameOrMobile(loginName);
        if (ObjectUtil.isNotNull(sysUser)) {
            phone=sysUser.getPhone();
        }
        resultMap.put("phone",phone);
        //发送验证码
        if (StringUtils.isBlank(phone)) {
            throw new ServiceException("导入用户数据不能为空！");
        }
//        if (!PhoneUtils.isChinaPhone(phone)) {
//            throw new ServiceException("导入用户数据不能为空！");
//        }
        try {
            SysSms sysSms = new SysSms();
            //sysSms.setIp(ip);
            sysSms.setPhone(phone);
            //sysSms.setBusinessCode(SmsEnum.APPLOGIN.getKey());
            resultMap.put("verifyCode",this.sendVerifyCode(sysSms));
        } catch (Exception e) {
            e.getStackTrace();
            throw new ServiceException("获取短信验证码错误！"+ e.getMessage());
        }
        return resultMap;
    }

	/**
	* @title sendVerifyCode
	* @Description: 保存并返送验证码
	* @param sysSms
	* @return String
	* <AUTHOR>
	* @date 2025/1/17 15:41
	*/
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public String sendVerifyCode(SysSms sysSms) throws Exception{
        String verifyCode;
        try {
            //if(captchaMode) {
            //根据数据判断 发送次数是否超过限制
//            if(true) {
//                //校验 手机号 ip发送数量是否超过限制 超过限制不能发送 并提示
//                Map<String, Object> resultMap = searchSendInfoByIpAndPhone(sysSms);
//                Integer sameIpCnt=Integer.parseInt(resultMap.get("sameIpCnt").toString());
//                if (sameIpCnt >= daySameIpCnt) {
//                    return ResponseResult.failure("短信验证码发送量已超过当日最大数量，请您明日再试。");
//                }
//                Integer samePhoneCnt=Integer.parseInt(resultMap.get("samePhoneCnt").toString());
//                if (samePhoneCnt >= daySamePhoneCnt) {
//                    return ResponseResult.failure(verificationCode.getPhone()+"短信验证码发送量已超过当日最大数量，请您明日再试。");
//                }
//            }

            SysSms vQuery = getSmsCode(sysSms);
            //刚发完短信验证码 还未使用 重新发送相同的验证码 保证两次发送验证码 只要有一次发送到达，即可完成验证(兼容通信网络不佳情况)
            if (null != vQuery && SmsCodeStatusEnum.CODE_0.getKey().equals(vQuery.getCodeStatus())) {
                verifyCode = vQuery.getCode();
            } else {
                //根据长度生成验证码
                String smsLength=sysConfigService.selectConfigByKey(ConfigKeyEnum.SYS_ACCOUNT_SMS_LENGTH.getKey());
                verifyCode = CaptchaUtils.crate(true, StringUtils.isNotEmpty(smsLength)&&Integer.parseInt(smsLength)>0?Integer.parseInt(smsLength):DEFAULT_SMS_LENGTH);
            }
            //保存发送记录
            sysSms.setCode(verifyCode);//验证码
            sysSms.setCodeStatus(SmsCodeStatusEnum.CODE_0.getKey());//使用状态
            //默认有效时间5分钟 当前时间增加300秒
            //根据长度生成验证码
            String validityTime=sysConfigService.selectConfigByKey(ConfigKeyEnum.SYS_ACCOUNT_SMS_VALIDITY_TIME.getKey());
            sysSms.setExpiryTime((int) (System.currentTimeMillis() / MILLI_TIME
                    + (StringUtils.isNotEmpty(validityTime)&&Integer.parseInt(validityTime)>0?Integer.parseInt(validityTime):DEFAULT_SMS_VALIDITY_TIME) * MINUTE_TIME));
            //发送状态 默认成功 如果发送失败，需要修改发送状态
            sysSms.setSendFlag(SmsSendEnum.SUCCESS.getKey());
            this.insertSysSms(sysSms);
            //存入redis
            String verifyKey = CacheConstants.SMS_CODE_KEY + com.jkr.common.utils.StringUtils.nvl(sysSms.getPhone(), "");
            redisCache.setCacheObject(verifyKey,verifyCode);

            // todo  以下为发送调用sdk发送验证码  因服务商原因未做开发
            // 是否调用sdk 发送
            Boolean sendEnabled = Convert.toBool(sysConfigService.selectConfigByKey(ConfigKeyEnum.SYS_ACCOUNT_SMS_SEND_ENABLED.getKey()));
            if(sendEnabled) {
                aliSMSSendUtil.sendVerifySms(sysSms.getPhone() ,sysSms.getCode());
            }
        } catch (Exception e) {
            throw new ServiceException("短信发送错误！");
        }
        return verifyCode;
    }

    /**
     *
     * @Title: getSmsCode
     * @Author: lty
     * @Date: 2025年02月07日 下午 06:43:01
     * @Description: 获取验证码
     * @Param: sysSms
     * @Return:
     * @Throws:
     */
    public SysSms getSmsCode(SysSms sysSms) {
        if (StringUtils.isBlank(sysSms.getPhone())) {
            //throw new Exception("手机号不能为空");
            throw new PhoneNotExistsException();
        }
        // 默认5分钟过期失效
        if (null == sysSms.getExpiryTime()) {
            sysSms.setExpiryTime((int) (System.currentTimeMillis() / MILLI_TIME));
        }
        return sysSmsMapper.searchSmsCode(sysSms);
    }

    /**
     *
     * @Title: searchSendInfoByIpAndPhone
     * @Author: lxy
     * @Date: 2020年02月08日 下午 12:49:25
     * @Description: 通ip或手机号 获取发送短信记录条数 严格控制相同ip或手机号发送短信数量
     * @Param: verificationCode
     * @Return:
     * @Throws:
     */
    public Map<String,Object> searchSendInfoByIpAndPhone(SysSms sysSms) throws Exception{
        if (StringUtils.isBlank(sysSms.getPhone())) {
            throw new Exception("手机号不能为空");
        }
        return sysSmsMapper.searchSendInfoByIpAndPhone(sysSms).get(0);
    }

    /**
     * @title checkSmsCode
     * @Description: 短信校验码校验通用方法
     * @param phone
     * @param code
     * @return boolean
     * <AUTHOR>
     * @date 2025/1/17 16:35
     */
    public boolean checkSmsCode(String phone, String code) {
        SysSms sysSms = new SysSms();
        sysSms.setPhone(phone);
        sysSms.setCode(code);
        return this.checkSmsCode(sysSms);
    }



   /**
    * @title checkSmsCode
    * @Description: 检查验证码
    * @param sysSms
    * @return boolean
    * <AUTHOR>
    * @date 2025/1/17 16:35
    */
    private boolean checkSmsCode(SysSms sysSms)  {
        boolean flag=false;
        SysSms vquery = getSmsCode(sysSms);
        if (null == vquery || !SmsCodeStatusEnum.CODE_0.getKey().equals(vquery.getCodeStatus()) ) {
            //短信验证码已过期或无效
           // throw new Exception("验证码已过期或无效");
            throw new SmsExpireException();
        }
        if (!vquery.getCode().equals(sysSms.getCode())) {
            //短信验证码不正确
            //return flag;
            throw new SmsException();
        }
        flag=true;
        // 短信验证码校验 以及状态更新 已用 0 未用 1 已用 2过期
        vquery.setCodeStatus(SmsCodeStatusEnum.CODE_1.getKey());
        this.updateSysSms(vquery);
        return flag;
    }
}
