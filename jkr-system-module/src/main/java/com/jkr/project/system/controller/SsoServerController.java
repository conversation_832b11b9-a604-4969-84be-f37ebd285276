package com.jkr.project.system.controller;

import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.service.ISsoServerService;
import com.jkr.project.system.service.impl.SsoServerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
/**
 * 单点登录接口
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Slf4j
@Api(tags = "业务系统对接接口")
@RestController
@RequestMapping("/sso")
public class SsoServerController {
    @Autowired
    private ISsoServerService ssoServerService;

    /**
     * @Title: authAppByAppId
     * @author: lty
     * @date: 2021/1/5/0018 13:54
     * @Description: 为客户端系统授权
     * @Param: []
     * @return:com.sx.common.ResponseResult<java.lang.String>
     */
    @PostMapping(value = "/authAppByAppId")
    @ApiOperation(value = "业务系统授权", notes = "与业务系统交互,为业务系统授权")
    public AjaxResult authAppByAppId() {
        return ssoServerService.appAuthByAppId();
    }


    //第三方应用获取token方法
    /**
     * @methodName: getAppToken
     * @mescription: 获取app认证的token
     * @param: [password, ssoTicket]
     * @return: com.sx.common.ResponseResult
     * @author: lty
     * @date: 17:26
     **/
    @ApiOperation(value = "获取app认证的token")
    @PostMapping(value = "/getAppToken")
    public AjaxResult getAppToken(@RequestParam String loginName, @RequestParam String password,@RequestParam  String appId,@RequestParam String  appSecret) throws Exception {
        return ssoServerService.getAppToken(loginName,password,appId,appSecret);
    }

    /**
     * @title: validToken
     * <AUTHOR>
     * @date: 2021/2/27
     * @description: 用户登录
     * @return: com.sx.common.response.ResponseResult
     */
    @ApiOperation(value = "验证token是否有效")
    @PostMapping("/validToken")
    public AjaxResult validToken() throws Exception {
        return AjaxResult.success();
    }
}

