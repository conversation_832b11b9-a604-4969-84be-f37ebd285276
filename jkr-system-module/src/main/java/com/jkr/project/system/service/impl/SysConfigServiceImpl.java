package com.jkr.project.system.service.impl;

import com.github.yitter.idgen.YitIdHelper;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.constant.SysConfigConstants;
import com.jkr.common.constant.UserConstants;
import com.jkr.common.core.text.Convert;
import com.jkr.common.enums.ConfigKeyEnum;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.utils.JsonUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.common.utils.StringUtils;
import com.jkr.framework.redis.RedisCache;
import com.jkr.project.system.domain.SysConfig;
import com.jkr.project.system.domain.vo.WebConfigVO;
import com.jkr.project.system.mapper.SysConfigMapper;
import com.jkr.project.system.service.ISysConfigService;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 参数配置 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImpl implements ISysConfigService {
    @Autowired
    private SysConfigMapper configMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        loadingConfigCache();
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public SysConfig selectConfigById(Long configId) {
        SysConfig config = new SysConfig();
        config.setConfigId(configId);
        return configMapper.selectConfig(config);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        String configValue = Convert.toStr(redisCache.getCacheObject(getCacheKey(configKey)));
        if (StringUtils.isNotEmpty(configValue)) {
            return configValue;
        }
        SysConfig config = new SysConfig();
        config.setConfigKey(configKey);
        SysConfig retConfig = configMapper.selectConfig(config);
        if (StringUtils.isNotNull(retConfig)) {
            redisCache.setCacheObject(getCacheKey(configKey), retConfig.getConfigValue());
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取验证码开关
     *
     * @return true开启，false关闭
     */
    @Override
    public boolean selectCaptchaEnabled() {
        String captchaEnabled = selectConfigByKey(SysConfigConstants.SYS_ACCOUNT_CAPTCHA_ENABLED);
        if (StringUtils.isEmpty(captchaEnabled)) {
            return true;
        }
        return Convert.toBool(captchaEnabled);
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfig> selectConfigList(SysConfig config) {
        return configMapper.selectConfigList(config);
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(SysConfig config) {
        config.setConfigId(YitIdHelper.nextId());
        int row = configMapper.insertConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(SysConfig config) {
        SysConfig temp = configMapper.selectConfigById(config.getConfigId());
        if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey())) {
            redisCache.deleteObject(getCacheKey(temp.getConfigKey()));
        }

        int row = configMapper.updateConfig(config);
        if (row > 0) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
        return row;
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config = selectConfigById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            configMapper.deleteConfigById(configId);
            redisCache.deleteObject(getCacheKey(config.getConfigKey()));
        }
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = configMapper.selectConfigList(new SysConfig());
        for (SysConfig config : configsList) {
            redisCache.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 清空参数缓存数据
     */
    @Override
    public void clearConfigCache() {
        Collection<String> keys = redisCache.keys(CacheConstants.SYS_CONFIG_KEY + "*");
        redisCache.deleteObject(keys);
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfig config) {
        SysConfig info = configMapper.checkConfigKeyUnique(config);
        if (StringUtils.isNotNull(info)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return CacheConstants.SYS_CONFIG_KEY + configKey;
    }

    /**
     * 获取网站配置信息
     *
     * @return List<SysConfig>
     * <AUTHOR>
     * @date 2025年01月16日 09:19:19
     */
    public WebConfigVO selectDataForWebsite(SysConfig config) {
        WebConfigVO webConfig = this.getSysConfigObject(config.getConfigKey(), WebConfigVO.class);
        if (null != webConfig) {
            webConfig.setConfigKey(config.getConfigKey());
        }
        return webConfig;
    }

    /**
     * @param
     * @return Map<String, Object>
     * @title selectDataForSmsEnabled
     * @Description: 获取短信验证码配置
     * <AUTHOR>
     * @date 2025/1/17 13:51
     */
    @Override
    public Map<String, Object> selectDataForSmsConfig() {
        Map<String, Object> smsConfig = new HashMap<>();
        //短信验证使用标识
        String smsEnabled = selectConfigByKey(ConfigKeyEnum.SYS_ACCOUNT_SMS_ENABLED.getKey());
        //短信验证码 有效时长
        String smsValidityTime = selectConfigByKey(ConfigKeyEnum.SYS_ACCOUNT_SMS_VALIDITY_TIME.getKey());

        smsConfig.put(ConfigKeyEnum.SYS_ACCOUNT_SMS_ENABLED.getKey(), smsEnabled);
        smsConfig.put(ConfigKeyEnum.SYS_ACCOUNT_SMS_VALIDITY_TIME.getKey(), smsValidityTime);
        return smsConfig;


    }


    /**
     * 获取配置信息，并返回对应的类
     *
     * @param key   key
     * @param clazz 类
     * @return T 泛型
     * <AUTHOR>
     * @date 2025年01月16日 10:43:54
     */
    public <T> T getSysConfigObject(String key, Class<T> clazz) {
        String value = selectConfigByKey(key);
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        String className = "java.lang.String";
        if (className.equals(clazz.getName())) {
            return (T) value;
        } else {
            return JsonUtils.parseObject(value, clazz);
        }
    }

    /**
     * 获取小程序appid(根据请求头信息，如果没有则获取默认)
     *
     * <AUTHOR>
     * @date  2025/4/22 16:20
     * @return java.lang.String
     **/
    public String getWechatAppIdFromHeaderOrDefault() {
        String result = SecurityUtils.getWechatAppIdFromHeader();
        if(StringUtils.isEmpty(result)){
            result = selectConfigByKey(SysConfigConstants.WECHAT_APPID);
        }
        return result;
    }
}
