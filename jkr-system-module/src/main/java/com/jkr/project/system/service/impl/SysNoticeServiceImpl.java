package com.jkr.project.system.service.impl;

import com.github.yitter.idgen.YitIdHelper;
import com.jkr.project.system.domain.SysNotice;
import com.jkr.project.system.mapper.SysNoticeMapper;
import com.jkr.project.system.service.ISysNoticeService;
import com.jkr.project.tool.sse.servicce.SseService;
import com.jkr.project.tool.sse.vo.MessageVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公告 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {
    @Resource
    private SysNoticeMapper noticeMapper;
    @Resource
    private SseService sseService;

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId) {
        return noticeMapper.selectNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectNoticeList(SysNotice notice) {
        return noticeMapper.selectNoticeList(notice);
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int insertNotice(SysNotice notice) {
        notice.setNoticeId(YitIdHelper.nextId());
        int row = noticeMapper.insertNotice(notice);
        MessageVo vo = new MessageVo();
        vo.setClientId(null);
        vo.setName("notice add");
        vo.setData(noticeMapper.selectCount() + "");
        sseService.publish(vo);
        return row;
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    public int updateNotice(SysNotice notice) {
        return noticeMapper.updateNotice(notice);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId) {
        return noticeMapper.deleteNoticeById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeByIds(Long[] noticeIds) {
        return noticeMapper.deleteNoticeByIds(noticeIds);
    }

}
