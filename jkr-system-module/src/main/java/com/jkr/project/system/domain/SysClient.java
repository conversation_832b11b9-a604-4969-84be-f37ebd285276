package com.jkr.project.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jkr.framework.aspectj.lang.annotation.Excel;
import com.jkr.framework.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 客户端应用对象 sys_client
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysClient extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 客户端编号
     */
    @Excel(name = "客户端编号")
    private String clientAppId;

    /**
     * 客户端密钥
     */
    @Excel(name = "客户端密钥")
    private String clientSecret;

    /**
     * 应用分类字典表id
     */
    @Excel(name = "应用分类字典表id")
    private String type;

    /**
     * 项目英文文名称
     */
    @Excel(name = "英文名称")
    private String enName;

    /**
     * 项目中文名称
     */
    @Excel(name = "中文名称")
    private String cnName;

    /**
     * 中文简称
     */
    @Excel(name = "中文简称")
    private String shortCnName;

    /**
     * 应用接口链接
     */
    @Excel(name = "应用接口链接")
    private String redirectPath;

    /**
     * 应用服务端地址
     */
    @Excel(name = "应用服务端地址")
    private String serverPath;

    /**
     * 登录方式 0 免登录 1 手输
     */
    @Excel(name = "登录方式 0 免登录 1 手输")
    private Long loginAuth;


    /**
     * 有权限图片
     */
    @Excel(name = "有权限图片")
    private String allowImgUrl;

    /**
     * 无权限图片
     */
    @Excel(name = "无权限图片")
    private String unAllowImgUrl;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer sort;

    /**
     * 删除标志（默认为0，表示数据可用，所有值为时间戳的表示数据不可用）
     */
    private String delFlag;

    @ApiModelProperty(value = "图标")
    @TableField(exist = false)
    private List<SysFile> imgInfoList;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("clientId", getId())
                .append("clientAppId", getClientAppId())
                .append("clientSecret", getClientSecret())
                .append("type", getType())
                .append("enName", getEnName())
                .append("cnName", getCnName())
                .append("shortCnName", getShortCnName())
                .append("redirectPath", getRedirectPath())
                .append("serverPath", getServerPath())
                .append("loginAuth", getLoginAuth())
                .append("allowImgUrl", getAllowImgUrl())
                .append("unAllowImgUrl", getUnAllowImgUrl())
                .append("sort", getSort())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
