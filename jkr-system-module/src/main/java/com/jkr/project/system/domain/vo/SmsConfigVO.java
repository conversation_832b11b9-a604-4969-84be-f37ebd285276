/*
 * Copyright (c) 2018-2999 广州市蓝海创新科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */
package com.jkr.project.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 短信配置
 *
 * <AUTHOR>
 * @date 2025年01月19日 10:30:37
 */
@Data
public class SmsConfigVO implements Serializable{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(name = "smsEnabled",value = "短信验证标识")
    private Boolean smsEnabled;
    @ApiModelProperty(name = "smsSendEnabled",value = "短信发送标识")
    private Boolean smsSendEnabled;
    @ApiModelProperty(name = "smsLength",value = "验证码长度")
    private Boolean smsLength;
    @ApiModelProperty(name = "validityTime",value = "系统标题文本")
    private Integer validityTime;
}
