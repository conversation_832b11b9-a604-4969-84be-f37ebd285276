package com.jkr.project.system.service.impl;

import com.jkr.common.constant.Constants;
import com.jkr.common.constant.SsoConstant;
import com.jkr.common.exception.ServiceException;
import com.jkr.common.exception.user.UserPasswordNotMatchException;
import com.jkr.common.utils.MessageUtils;
import com.jkr.framework.manager.AsyncManager;
import com.jkr.framework.manager.factory.AsyncFactory;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.security.context.AuthenticationContextHolder;
import com.jkr.framework.security.service.TokenService;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysUserClient;
import com.jkr.project.system.service.ISsoServerService;
import com.jkr.project.system.service.ISysUserClientService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 * 账号 服务类
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@Slf4j
@Service
@Validated
@Transactional(readOnly = true)
public class SsoServerService implements ISsoServerService {


    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private ISysUserClientService sysUserClientService;
    /**
     * @Title: appAuthByAppId
     * @author: lty
     * @date: 2020/12/17/0017 10:42
     * @Description: 为业务系统授权
     * @Param: [token] ssoToken
     * @return:com.sx.common.ResponseResult<java.lang.String>
     */
    public AjaxResult appAuthByAppId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String clientAppId = request.getHeader(SsoConstant.CLIENT_APP_ID);
        String clientAppSecret = request.getHeader(SsoConstant.CLIENT_APP_SECRET);
        //通过token获取用户信息
        LoginUser loginUser = tokenService.getLoginUser(request);
        //判断用户是否含有应用访问权限
        SysUserClient sysUserClient = new SysUserClient();
        sysUserClient.setClientAppId(clientAppId);
        sysUserClient.setClientSecret(clientAppSecret);
        sysUserClient.setUserId(loginUser.getUser().getUserId());
        boolean hasAuth = sysUserClientService.hasAuthByUser(sysUserClient);
        if(!hasAuth){
            return AjaxResult.error("无权限登录访问客户端系统！");
        }
        //根据用户信息获取单点的关联账号
        // 1、可以直接返回用户信息，客户端系统通过用户信息中的电话号码登录
        return AjaxResult.success(loginUser.getUser().getPhone());
        // 2、可以根据账号信息获取客户端系统关联的账号

        // 根据账号判断是有包含访问该系统的权限

    }

   /**
    * @title getAppToken
    * @Description: TODO
    * @param loginName
    * @param password
    * @param appId
    * @param appSecret
    * @return com.jkr.framework.web.domain.AjaxResult
    * <AUTHOR>
    * @date 2025/2/5 15:54
    */
    public AjaxResult getAppToken(@RequestParam String loginName, @RequestParam String password, @RequestParam  String appId, @RequestParam String  appSecret) throws Exception {
        if (StringUtils.isBlank(loginName)) {
            return AjaxResult.error("用户名不能为空");
        }
        if (StringUtils.isBlank(password)) {
            return AjaxResult.error("密码不能为空");
        }
        if (StringUtils.isBlank(appId)) {
            return AjaxResult.error("appId不能为空");
        }
        if (StringUtils.isBlank(appSecret)) {
            return AjaxResult.error("appSecret不能为空");
        }
        // 用户验证 登录
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginName, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginName, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginName, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(loginName, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        // 生成token
        String token = tokenService.createToken(loginUser);

        //判断用户是否含有应用访问权限
        SysUserClient sysUserClient = new SysUserClient();
        sysUserClient.setClientAppId(appId);
        sysUserClient.setClientSecret(appSecret);
        sysUserClient.setUserId(loginUser.getUser().getUserId());
        boolean hasAuth = sysUserClientService.hasAuthByUser(sysUserClient);
        if(!hasAuth){
            return AjaxResult.error("无权限登录访问客户端系统！");
        }

        return AjaxResult.success(token);
    }
}
