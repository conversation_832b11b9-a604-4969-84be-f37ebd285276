package com.jkr.project.system.service;

import java.util.List;
import java.util.Map;

import com.jkr.project.system.domain .SysSms;

/**
 * 短信记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
public interface ISysSmsService {
	/**
	 * 查询短信记录
	 *
	 * @param smsId 短信记录主键
	 * @return 短信记录
	 */
	public SysSms selectSysSmsBySmsId(String smsId);

	/**
	 * 查询短信记录列表
	 *
	 * @param sysSms 短信记录
	 * @return 短信记录集合
	 */
	public List<SysSms> selectSysSmsList(SysSms sysSms);

	/**
	 * 新增短信记录
	 *
	 * @param sysSms 短信记录
	 * @return 结果
	 */
	public int insertSysSms(SysSms sysSms);

	/**
	 * 修改短信记录
	 *
	 * @param sysSms 短信记录
	 * @return 结果
	 */
	public int updateSysSms(SysSms sysSms);

	/**
	 * 删除短信记录信息
	 *
	 * @param smsId 短信记录主键
	 * @return 结果
	 */
	public int deleteSysSmsBySmsId(String smsId);

	/**
	 * 发送短信验证码
	 *
	 * @param loginName 用户登录名
	 * @return 结果
	 */
	public Map sendCode(String loginName);

	/**
	 * @title checkSmsCode
	 * @Description: 短信校验码校验通用方法
	 * @param userName
	 * @param code
	 * @return boolean
	 * <AUTHOR>
	 * @date 2025/1/17 16:35
	 */
	public boolean checkSmsCode(String phone, String code) ;
}
