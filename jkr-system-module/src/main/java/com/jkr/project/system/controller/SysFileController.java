package com.jkr.project.system.controller;

import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件Controller
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@RestController
@RequestMapping("/system/file")
public class SysFileController extends BaseController {
    @Autowired
    private ISysFileService sysFileService;
    /**
     * 列表
     *
     * <AUTHOR>
     * @date 2020年09月16日 15:02:14
     * @param sysFile
     * @since 1.0.0
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @PreAuthorize("@ss.hasPermi('system:file:list')")
    @GetMapping(value = "/list")
    public TableDataInfo list(SysFile sysFile) {
        startPage();
        List<SysFile> list = sysFileService.selectSysFileList(sysFile);
        return getDataTable(list);
    }

    /**
     * 详情
     *
     * <AUTHOR>
     * @date 2020年09月16日 15:02:29
     * @param fileId
     * @since 1.0.0
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @PreAuthorize("@ss.hasPermi('system:file:query')")
    @ApiOperation(value = "详情", response = SysFile.class, notes = "sys:file:info")
    @GetMapping(value = "/info/{fileId}")
    public AjaxResult getInfo(@PathVariable Long fileId) {
        return success(sysFileService.selectSysFileById(fileId));
    }

    /**
     * 新增文件
     *
     * <AUTHOR>
     * @date 2020年09月16日 15:02:42
     * @param sysFile
     * @since 1.0.0
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @PreAuthorize("@ss.hasPermi('system:file:add')")
    @Log(title = "新增文件", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody SysFile sysFile) {
        return toAjax(sysFileService.insertSysFile(sysFile));
    }

    /**
     * 批量新增文件
     *
     * <AUTHOR>
     * @date 2020年09月16日 15:02:42
     * @param sysFileList
     * @since 1.0.0
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @PreAuthorize("@ss.hasPermi('system:file:add')")
    @Log(title = "批量新增文件", businessType = BusinessType.INSERT)
    @PostMapping(value = "/addList")
    public AjaxResult addList(@Validated @RequestBody List<SysFile> sysFileList) {
        sysFileService.insertSysFileList(sysFileList);
        return AjaxResult.success();
    }



    @PostMapping(value = "/addListNew")
    public AjaxResult addListNew(@Validated @RequestBody List<SysFile> sysFileList) {
        List<SysFile> sysFiles = sysFileService.addListNew(sysFileList);
        return AjaxResult.success(sysFiles);
    }


    /**
     * 删除文件
     *
     * <AUTHOR>
     * @date 2020年09月16日 15:03:04
     * @param fileId
     * @since 1.0.0
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @Log(title = "删除文件", businessType = BusinessType.DELETE)
    @PostMapping(value = "/remove/{fileId}")
    public AjaxResult remove(@PathVariable Long fileId) {
        return toAjax(sysFileService.deleteSysFileById(fileId));
    }

}
