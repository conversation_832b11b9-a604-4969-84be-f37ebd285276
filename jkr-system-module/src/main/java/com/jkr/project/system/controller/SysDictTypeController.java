package com.jkr.project.system.controller;

import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.SysDictType;
import com.jkr.project.system.service.ISysDictTypeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController extends BaseController {
	@Resource
	private ISysDictTypeService dictTypeService;

	/**
	 * 数据字典列表
	 *
	 * @param dictType
	 * @return com.jkr.framework.web.page.TableDataInfo
	 * @date 2025年01月15日 15:30:31
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysDictType dictType) {
		startPage();
		List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
		return getDataTable(list);
	}

	/**
	 * 导出字典类型列表
	 *
	 * @param response
	 * @param dictType
	 * <AUTHOR>
	 * @date 2025年01月15日 15:31:15
	 */
	@Log(title = "字典类型", businessType = BusinessType.EXPORT)
	@PreAuthorize("@ss.hasPermi('system:dict:export')")
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysDictType dictType) {
		List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
		ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
		util.exportExcel(response, list, "字典类型");
	}

	/**
	 * 查询字典类型详细
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:query')")
	@GetMapping(value = "/info/{dictId}")
	public AjaxResult getInfo(@PathVariable Long dictId) {
		return success(dictTypeService.selectDictTypeById(dictId));
	}

	/**
	 * 新增字典类型
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:add')")
	@Log(title = "字典类型", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public AjaxResult add(@Validated @RequestBody SysDictType dict) {
		if (!dictTypeService.checkDictTypeUnique(dict)) {
			return error("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
		}
		dict.setCreateBy(getUsername());
		return toAjax(dictTypeService.insertDictType(dict));
	}

	/**
	 * 修改字典类型
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:edit')")
	@Log(title = "字典类型", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult edit(@Validated @RequestBody SysDictType dict) {
		if (!dictTypeService.checkDictTypeUnique(dict)) {
			return error("修改字典'" + dict.getDictName() + "'失败，字典类型已存在");
		}
		dict.setUpdateBy(getUsername());
		return toAjax(dictTypeService.updateDictType(dict));
	}

	/**
	 * 删除字典类型
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:remove')")
	@Log(title = "字典类型", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{dictIds}")
	public AjaxResult remove(@PathVariable Long[] dictIds) {
		dictTypeService.deleteDictTypeByIds(dictIds);
		return success();
	}

	/**
	 * 刷新字典缓存
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:remove')")
	@Log(title = "字典类型", businessType = BusinessType.CLEAN)
	@PostMapping("/refreshCache")
	public AjaxResult refreshCache() {
		dictTypeService.resetDictCache();
		return success();
	}

	/**
	 * 获取字典选择框列表
	 */
	@GetMapping("/optionSelect")
	public AjaxResult optionSelect() {
		List<SysDictType> dictTypes = dictTypeService.selectDictTypeAll();
		return success(dictTypes);
	}
}
