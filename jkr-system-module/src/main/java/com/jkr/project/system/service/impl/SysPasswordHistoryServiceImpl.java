package com.jkr.project.system.service.impl;

import com.jkr.common.utils.DateUtils;
import com.jkr.common.utils.SecurityUtils;
import com.jkr.project.system.domain.SysPasswordHistory;
import com.jkr.project.system.mapper.SysPasswordHistoryMapper;
import com.jkr.project.system.service.ISysPasswordHistoryService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * SysPasswordHistoryServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-14 18:41
 */
@Service
public class SysPasswordHistoryServiceImpl implements ISysPasswordHistoryService {
	@Resource
	private SysPasswordHistoryMapper sysPasswordHistoryMapper;

	/**
	 * 查询密码历史列表
	 *
	 * @param sysPasswordHistory 密码历史
	 * @return 密码历史
	 */
	@Override
	public List<SysPasswordHistory> selectSysPasswordHistoryList(SysPasswordHistory sysPasswordHistory) {
		return sysPasswordHistoryMapper.selectSysPasswordHistoryList(sysPasswordHistory);
	}

	/**
	 * 新增密码历史
	 *
	 * @param sysPasswordHistory 密码历史
	 * @return 结果
	 */
	@Override
	public int insertSysPasswordHistory(SysPasswordHistory sysPasswordHistory) {
		sysPasswordHistory.setCreateTime(DateUtils.getNowDate());
		return sysPasswordHistoryMapper.insertSysPasswordHistory(sysPasswordHistory);
	}

	/**
	 * 检查新密码是否与历史密码相同
	 *
	 * @param userId      用户id
	 * @param newPassword 明文新密码
	 * @return true：与某个历史密码相同，false：和历史密码不相同
	 */
	@Override
	public boolean passwordCheck(Long userId, String newPassword) {
		SysPasswordHistory history = new SysPasswordHistory();
		history.setUserId(userId);
		List<SysPasswordHistory> list = selectSysPasswordHistoryList(history);
		for (SysPasswordHistory passwordHistory : list) {
			if (SecurityUtils.matchesPassword(newPassword, passwordHistory.getPassword())) {
				return true;
			}
		}
		return false;
	}
}
