package com.jkr.project.system.service;

import java.util.List;

import com.jkr.project.system.domain .SysUserClient;

/**
 * 系统账号与客户端关联关系Service接口
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
public interface ISysUserClientService {
	/**
	 * 查询系统账号与客户端关联关系
	 *
	 * @param id 系统账号与客户端关联关系主键
	 * @return 系统账号与客户端关联关系
	 */
	public SysUserClient selectSysUserClientById(Long id);

	/**
	 * 查询系统账号与客户端关联关系列表
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 系统账号与客户端关联关系集合
	 */
	public List<SysUserClient> selectSysUserClientList(SysUserClient sysUserClient);

	/**
	 * 新增系统账号与客户端关联关系
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 结果
	 */
	public int insertSysUserClient(SysUserClient sysUserClient);

	/**
	 * 修改系统账号与客户端关联关系
	 *
	 * @param sysUserClient 系统账号与客户端关联关系
	 * @return 结果
	 */
	public int updateSysUserClient(SysUserClient sysUserClient);

	/**
	 * 批量删除系统账号与客户端关联关系
	 *
	 * @param ids 需要删除的系统账号与客户端关联关系主键集合
	 * @return 结果
	 */
	public int deleteSysUserClientByIds(Long[] ids);

	/**
	 * 删除系统账号与客户端关联关系信息
	 *
	 * @param id 系统账号与客户端关联关系主键
	 * @return 结果
	 */
	public int deleteSysUserClientById(Long id);
	/**
	 * @title saveOrUpdate
	 * @Description: 根据授权情况保存或修改表数据
	 * @param entity
	 * @return java.util.List<com.jkr.project.system.domain.SysUserClient>
	 * <AUTHOR>
	 * @date 2025/2/6 9:58
	 */
	public int saveOrUpdate(SysUserClient entity);
	/**
	 * @title hasAuthByUser
	 * @Description: 查询用户是否授权应用系统
	 * @param sysUserClient 用户id
	 * @return boolean
	 * <AUTHOR>
	 * @date 2025/2/6 15:21
	 */
	public boolean hasAuthByUser(SysUserClient sysUserClient);
}
