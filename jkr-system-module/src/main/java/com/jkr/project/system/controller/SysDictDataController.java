package com.jkr.project.system.controller;

import com.jkr.common.utils.StringUtils;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.SysDictData;
import com.jkr.project.system.service.ISysDictDataService;
import com.jkr.project.system.service.ISysDictTypeService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dict/data")
public class SysDictDataController extends BaseController {
	@Resource
	private ISysDictDataService dictDataService;

	@Resource
	private ISysDictTypeService dictTypeService;

	/**
	 * 列表
	 *
	 * @param dictData
	 * @return com.jkr.framework.web.page.TableDataInfo
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysDictData dictData) {
		startPage();
		List<SysDictData> list = dictDataService.selectDictDataList(dictData);
		return getDataTable(list);
	}

	/**
	 * 导出字典数据
	 *
	 * @param response
	 * @param dictData
	 */
	@Log(title = "字典数据", businessType = BusinessType.EXPORT)
	@PreAuthorize("@ss.hasPermi('system:dict:export')")
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysDictData dictData) {
		List<SysDictData> list = dictDataService.selectDictDataList(dictData);
		ExcelUtil<SysDictData> util = new ExcelUtil<SysDictData>(SysDictData.class);
		util.exportExcel(response, list, "字典数据");
	}

	/**
	 * 查询字典数据详细
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:query')")
	@GetMapping(value = "/info/{dictCode}")
	public AjaxResult getInfo(@PathVariable Long dictCode) {
		return success(dictDataService.selectDictDataById(dictCode));
	}

	/**
	 * 根据字典类型查询字典数据信息
	 */
	@GetMapping(value = "/type/{dictType}")
	public AjaxResult dictType(@PathVariable String dictType) {
		List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
		if (StringUtils.isNull(data)) {
			data = new ArrayList<SysDictData>();
		}
		return success(data);
	}

	/**
	 * 新增字典类型
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:add')")
	@Log(title = "字典数据", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public AjaxResult add(@Validated @RequestBody SysDictData dict) {
		dict.setCreateBy(getUsername());
		return toAjax(dictDataService.insertDictData(dict));
	}

	/**
	 * 修改保存字典类型
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:edit')")
	@Log(title = "字典数据", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult edit(@Validated @RequestBody SysDictData dict) {
		dict.setUpdateBy(getUsername());
		return toAjax(dictDataService.updateDictData(dict));
	}

	/**
	 * 删除字典类型
	 */
	@PreAuthorize("@ss.hasPermi('system:dict:remove')")
	@Log(title = "字典类型", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{dictCodes}")
	public AjaxResult remove(@PathVariable Long[] dictCodes) {
		dictDataService.deleteDictDataByIds(dictCodes);
		return success();
	}
}
