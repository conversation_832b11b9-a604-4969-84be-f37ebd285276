package com.jkr.project.system.controller;

import com.jkr.common.utils.*;
import com.jkr.common.utils.file.FileUploadUtils;
import com.jkr.common.utils.file.MimeTypeUtils;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.config.RuoYiConfig;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.security.service.TokenService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysUser;
import com.jkr.project.system.domain.vo.UpdatePwdVo;
import com.jkr.project.system.service.ISysPasswordHistoryService;
import com.jkr.project.system.service.ISysUserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {
	@Resource
	private ISysUserService userService;

	@Resource
	private TokenService tokenService;

	@Resource
	private ISysPasswordHistoryService sysPasswordHistoryService;

	/**
	 * 个人信息
	 */
	@GetMapping("/info")
	public AjaxResult profile() {
		LoginUser loginUser = getLoginUser();
		SysUser user = loginUser.getUser();
		AjaxResult ajax = AjaxResult.success(user);
		ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
		ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
		return ajax;
	}

	/**
	 * 修改用户
	 */
	@Log(title = "个人信息", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult updateProfile(@RequestBody SysUser user) {
		LoginUser loginUser = getLoginUser();
		SysUser currentUser = loginUser.getUser();
		currentUser.setNickName(user.getNickName());
		currentUser.setEmail(user.getEmail());
		currentUser.setPhone(user.getPhone());
		currentUser.setSex(user.getSex());
		if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(currentUser)) {
			return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
		}
		if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser)) {
			return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
		}
		if (userService.updateUserProfile(currentUser) > 0) {
			// 更新缓存用户信息
			tokenService.setLoginUser(loginUser);
			return success();
		}
		return error("修改个人信息异常，请联系管理员");
	}

	/**
	 * 重置密码
	 */
	@Log(title = "个人信息", businessType = BusinessType.UPDATE)
	@PostMapping("/updatePwd")
	public AjaxResult updatePwd(@RequestBody UpdatePwdVo params) {
		String oldPassword = params.getOldPassword();
		String newPassword = params.getNewPassword();
		// 密码强度检查
		if (!PasswordChecker.isPasswordValid(newPassword)) {
			return error("新密码强度不符合要求，请重新设置");
		}
		LoginUser loginUser = getLoginUser();
		String userName = loginUser.getUsername();
		String password = loginUser.getPassword();
		// 检查旧密码
		if (!SecurityUtils.matchesPassword(oldPassword, password)) {
			return error("修改密码失败，旧密码错误");
		}
		// 新旧密码比较
		if (SecurityUtils.matchesPassword(newPassword, password)) {
			return error("新密码不能与旧密码相同");
		}

		// 新密码与历史密码比较
		boolean bool = sysPasswordHistoryService.passwordCheck(loginUser.getUserId(), newPassword);
		if (bool) {
			return error("新密码与历史密码相同，请重新设置");
		}
		// 加密新密码
		newPassword = SecurityUtils.encryptPassword(newPassword);
		// 更新密码
		if (userService.resetUserPwd(loginUser.getUserId(), userName, newPassword) > 0) {
			// 更新缓存用户密码
			loginUser.getUser().setPassword(newPassword);
			tokenService.setLoginUser(loginUser);
			return success();
		}
		return error("修改密码异常，请联系管理员");
	}

	/**
	 * 头像上传
	 */
	@Log(title = "用户头像", businessType = BusinessType.UPDATE)
	@PostMapping("/avatar")
	public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws Exception {
		if (!file.isEmpty()) {
			LoginUser loginUser = getLoginUser();
			String avatar = FileUploadUtils.upload(RuoYiConfig.getAvatarPath(), file, MimeTypeUtils.IMAGE_EXTENSION);
			if (userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
				AjaxResult ajax = AjaxResult.success();
				ajax.put("imgUrl", avatar);
				// 更新缓存用户头像
				loginUser.getUser().setAvatar(avatar);
				tokenService.setLoginUser(loginUser);
				return ajax;
			}
		}
		return error("上传图片异常，请联系管理员");
	}
}
