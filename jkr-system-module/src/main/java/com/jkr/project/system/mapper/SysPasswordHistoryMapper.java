package com.jkr.project.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jkr.project.system.domain.SysPasswordHistory;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * SysPasswordHistoryMapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-14 18:35
 */
@Mapper
public interface SysPasswordHistoryMapper extends BaseMapper<SysPasswordHistory> {

	/**
	 * 查询密码历史列表
	 *
	 * @param sysPasswordHistory 密码历史
	 * @return 密码历史集合
	 */
	List<SysPasswordHistory> selectSysPasswordHistoryList(SysPasswordHistory sysPasswordHistory);

	/**
	 * 新增密码历史
	 *
	 * @param sysPasswordHistory 密码历史
	 * @return 结果
	 */
	int insertSysPasswordHistory(SysPasswordHistory sysPasswordHistory);
}
