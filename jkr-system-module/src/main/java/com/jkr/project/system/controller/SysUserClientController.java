package com.jkr.project.system.controller;

import java.util.List;

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.project.system.domain.SysUserClient;
import com.jkr.project.system.service.ISysUserClientService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.web.page.TableDataInfo;

/**
 * 系统账号与客户端关联关系Controller
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@RestController
@RequestMapping("/system/user/client")
public class SysUserClientController extends BaseController {
    @Autowired
    private ISysUserClientService sysUserClientService;

    /**
     * 查询系统账号与客户端关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:client:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUserClient sysUserClient) {
        startPage();
        List<SysUserClient> list = sysUserClientService.selectSysUserClientList(sysUserClient);
        return getDataTable(list);
    }

    /**
     * 导出系统账号与客户端关联关系列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:client:export')")
    @Log(title = "系统账号与客户端关联关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUserClient sysUserClient) {
        List<SysUserClient> list = sysUserClientService.selectSysUserClientList(sysUserClient);
        ExcelUtil<SysUserClient> util = new ExcelUtil<SysUserClient>(SysUserClient.class);
        util.exportExcel(response, list, "系统账号与客户端关联关系数据");
    }

    /**
     * 获取系统账号与客户端关联关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:client:query')")
    @PostMapping(value = "/info/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(sysUserClientService.selectSysUserClientById(id));
    }

    /**
     * 新增系统账号与客户端关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:user:client:add')")
    @Log(title = "系统账号与客户端关联关系", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@Validated @RequestBody SysUserClient sysUserClient) {
        return toAjax(sysUserClientService.insertSysUserClient(sysUserClient));
    }

    /**
     * 修改系统账号与客户端关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:user:client:edit')")
    @Log(title = "系统账号与客户端关联关系", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@Validated @RequestBody SysUserClient sysUserClient) {
        return toAjax(sysUserClientService.updateSysUserClient(sysUserClient));
    }

    /**
     * 删除系统账号与客户端关联关系
     */
    @PreAuthorize("@ss.hasPermi('system:user:client:remove')")
    @Log(title = "系统账号与客户端关联关系", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(sysUserClientService.deleteSysUserClientById(id));
    }


    @ApiOperation(value = "保存或修改应用绑定信息的项目权限")
    @Log(title = "保存或修改应用绑定信息的项目权限")
    @PostMapping(value = "/saveOrUpdate")
    public AjaxResult saveOrUpdate(@Validated @RequestBody SysUserClient entity) {
        return AjaxResult.success(sysUserClientService.saveOrUpdate(entity));
    }
}
