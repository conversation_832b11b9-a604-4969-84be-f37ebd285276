package com.jkr.project.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jkr.project.system.domain.SysFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Mapper
public interface SysFileMapper extends BaseMapper<SysFile> {
    /**
     * 查询文件
     *
     * @param fileId 文件主键
     * @return 文件
     */
    public SysFile selectSysFileById(Long fileId);

    /**
     * 查询文件列表
     *
     * @param sysFile 文件
     * @return 文件集合
     */
    public List<SysFile> selectSysFileList(SysFile sysFile);

    /**
     * 新增文件
     *
     * @param sysFile 文件
     * @return 结果
     */
    public int insertSysFile(SysFile sysFile);

    /**
     * 修改文件
     *
     * @param sysFile 文件
     * @return 结果
     */
    public int updateSysFile(SysFile sysFile);

    /**
     * 删除文件
     *
     * @param fileId 文件主键
     * @return 结果
     */
    public int deleteSysFileById(Long fileId);

    /**
     * 批量删除文件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysFileByIds(Long[] ids);


    /**
     *
     * 根据业务表Id逻辑删除附件
     *
     * <AUTHOR>
     * @date 2022年01月07日 13:25:07
     * @param tableId 业务表Id
     * @return : void
     */
    void removeByTableId(@Param("tableId") String tableId);

    /**
     *
     * 根据业务表Id和图片业务类型逻辑删除附件
     *
     * <AUTHOR>
     * @date 2022年1月16日09:48:52
     * @param tableId 业务表Id
     * @param fieldType 图片业务类型，如员工个人照片personal，学历照片education
     * @return : void
     */
    public void removeByTableIdAndFieldType(@Param("tableId") Long tableId, @Param("fieldType") String fieldType);
}
