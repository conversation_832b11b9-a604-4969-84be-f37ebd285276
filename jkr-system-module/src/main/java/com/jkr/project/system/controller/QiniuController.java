package com.jkr.project.system.controller;

import com.jkr.common.utils.FileSecurityUtil;
import com.jkr.common.utils.FileValidatorUtils;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.IQiniuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "服务器文件管理")
@RestController
@RequestMapping("/qiniu")
@Slf4j
public class QiniuController {

    @Autowired
    FileValidatorUtils fileValidatorUtils;
    @Autowired
    private IQiniuService qiniuService;

    @ApiOperation(value = "获取七牛token")
    @PostMapping("/get7nToken")
    public AjaxResult get7nToken() throws Exception {
        return AjaxResult.success(qiniuService.getQiniuAuthToken());
    }

    @ApiOperation(value = "上传")
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "tableId") String tableId, @RequestParam(name = "tableName") String tableName, @RequestParam(name = "type") String type){
        SysFile att = new SysFile();
        try {
            boolean checkFlag = FileSecurityUtil.checkWhiteSuffixFile(file);
            if(checkFlag){
                att = qiniuService.uploadToQiniu(file, tableId, tableName, type);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(att);
    }

}
