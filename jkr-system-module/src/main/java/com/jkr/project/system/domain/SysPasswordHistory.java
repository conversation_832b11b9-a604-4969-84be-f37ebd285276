package com.jkr.project.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * SysPasswordHistory 密码历史表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-14 18:23
 */
@Data
@TableName(value = "sys_password_history")
public class SysPasswordHistory {
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 替换下来的密码
	 */
	private String password;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
}
