package com.jkr.project.system.controller;

import com.jkr.common.utils.file.FileUtils;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.project.system.domain.vo.MinioInfo;
import com.jkr.project.system.service.IMinioService;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;


/**
 *
 * 服务器文件管理
 *
 * <AUTHOR>
 * @date 2025/1/13
 */
@Api(tags = "服务器文件管理")
@RestController
@RequestMapping("/minio")
@Slf4j
public class MinioController {

	@Autowired
	private MinioClient minioClient;
	@Autowired
	private IMinioService minioService;

	@Value("${com.jkr.minio.bucketName}")
	private String bucketName;

    /**
     * 上传文件
     * <AUTHOR>
     * @date 2025/1/13
     * @param file
     * @param filePath
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @ApiOperation(value = "上传")
    @PostMapping("/upload")
    public AjaxResult upload(@RequestParam(name = "file") MultipartFile file, @RequestParam(name = "filePath") String filePath) {
        MinioInfo info = minioService.upload(file, filePath);
        if (info != null) {
            return AjaxResult.success(info);
        }
        return AjaxResult.error("上传失败");
    }

    /**
     * 附件下载方法
     *
     * <AUTHOR>
     * @date 2025/1/13
     * @param fileName
     * @param name
     * @param type
     * @param response
     * @return void
     */
    @ApiOperation(value = "下载")
    @PostMapping(value = "/download", produces = "application/octet-stream")
    public void download(@RequestParam(name = "fileName") String fileName, @RequestParam(name = "name") String name, @RequestParam(name = "type") String type, HttpServletResponse response) {
        try (InputStream in = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build())) {
            /// response.setContentType("application/force-download");
            response.setHeader("content-type", type);
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8"));
            IOUtils.copy(in, response.getOutputStream());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 删除文件数据
     *
     * <AUTHOR>
     * @date 2025/1/13
     * @param fileName
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public AjaxResult delete(@RequestParam(name = "fileName") String fileName) {
        boolean bool = minioService.delete(bucketName, fileName);
        if (bool) {
            return AjaxResult.success();
        }
        return AjaxResult.error("操作失败");
    }



    /**
     * 预览文件数据
     *
     * <AUTHOR>
     * @date 2025/1/13
     * @param fileName
     * @param response
     */
    @ApiOperation(value = "预览")
    @GetMapping(value = "/preview/{fileName}", produces = "image/jpeg")
    public void preview(@PathVariable String fileName, HttpServletResponse response) throws Exception {
        try (InputStream in = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build())) {
            response.setContentType("application/force-download");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            IOUtils.copy(in, response.getOutputStream());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 通过文件名称查询获取图片base64格式
     *
     * <AUTHOR>
     * @date 2025/1/13
     * @param fileName
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @ApiOperation(value = "返回base64字符串")
    @PostMapping(value = "/baseinfo")
    public AjaxResult baseinfo(String fileName) {
        try (InputStream in = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build())) {
            Object data = FileUtils.getBase64FromInputStream(in);
            return AjaxResult.success(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return AjaxResult.success();
        }
    }

    /**
     * 删除文件数据
     *
     * <AUTHOR>
     * @date 2025/1/13
     * @param fileName
     * @param sysFileId
     * @return com.jkr.framework.web.domain.AjaxResult
     */
    @ApiOperation(value = "删除文件并同步删除数据")
    @PostMapping("/allDelete")
    public AjaxResult allDelete(@RequestParam(name = "fileName") String fileName, @RequestParam(name = "sysFileId") Long sysFileId) {
        boolean bool = minioService.allDelete(bucketName, fileName, sysFileId);
        if (bool) {
            return AjaxResult.success();
        }
        return AjaxResult.error("操作失败");
    }


}
