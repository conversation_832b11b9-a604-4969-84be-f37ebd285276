package com.jkr.project.system.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.jkr.project.system.domain.SysWechat;

/**
 * 微信小程序信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Mapper
public interface SysWechatMapper extends BaseMapper<SysWechat>{
	/**
	 * 查询微信小程序信息
	 *
	 * @param id 微信小程序信息主键
	 * @return 微信小程序信息
	 */
	public SysWechat selectWechatById(Long id);

	/**
	 * 查询微信小程序信息列表
	 *
	 * @param wechat 微信小程序信息
	 * @return 微信小程序信息集合
	 */
	public List<SysWechat> selectWechatList(SysWechat wechat);

	/**
	 * 新增微信小程序信息
	 *
	 * @param wechat 微信小程序信息
	 * @return 结果
	 */
	public int insertWechat(SysWechat wechat);

	/**
	 * 修改微信小程序信息
	 *
	 * @param wechat 微信小程序信息
	 * @return 结果
	 */
	public int updateWechat(SysWechat wechat);

	/**
	 * 删除微信小程序信息
	 *
	 * @param id 微信小程序信息主键
	 * @return 结果
	 */
	public int deleteWechatById(Long id);

	/**
	 * 批量删除微信小程序信息
	 *
	 * @param ids 需要删除的数据主键集合
	 * @return 结果
	 */
	public int deleteWechatByIds(Long[] ids);

	/**
	 * 批量逻辑删除微信小程序信息
	 *
	 * @param  ids 微信小程序信息主键
	 * @return 结果
	 */
	public int logicRemoveByIds(List<Long> ids);

	/**
	 * 通过微信小程序信息主键id逻辑删除信息
	 *
	 * @param  id 微信小程序信息主键
	 * @return 结果
	 */
	public int logicRemoveById(Long id);
}
