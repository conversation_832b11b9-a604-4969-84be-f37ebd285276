package com.jkr.project.system.controller;

import com.jkr.common.utils.StringUtils;
import com.jkr.common.utils.poi.ExcelUtil;
import com.jkr.framework.aspectj.lang.annotation.Log;
import com.jkr.framework.aspectj.lang.enums.BusinessType;
import com.jkr.framework.security.LoginUser;
import com.jkr.framework.security.service.SysPermissionService;
import com.jkr.framework.security.service.TokenService;
import com.jkr.framework.web.controller.BaseController;
import com.jkr.framework.web.domain.AjaxResult;
import com.jkr.framework.web.page.TableDataInfo;
import com.jkr.project.system.domain.*;
import com.jkr.project.system.service.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/role")
public class SysRoleController extends BaseController {
	@Resource
	private ISysRoleService roleService;

	@Resource
	private TokenService tokenService;

	@Resource
	private SysPermissionService permissionService;

	@Resource
	private ISysUserService userService;

	@Resource
	private ISysDeptService deptService;

	/**
	 * 列表
	 *
	 * @param role
	 * @return com.jkr.framework.web.page.TableDataInfo
	 */
	@PreAuthorize("@ss.hasPermi('system:role:list')")
	@GetMapping("/list")
	public TableDataInfo list(SysRole role) {
		startPage();
		List<SysRole> list = roleService.selectRoleList(role);
		return getDataTable(list);
	}

	/**
	 * 角色管理-导出
	 *
	 * @param response
	 * @param role
	 * @return void
	 */
	@Log(title = "角色管理", businessType = BusinessType.EXPORT)
	@PreAuthorize("@ss.hasPermi('system:role:export')")
	@PostMapping("/export")
	public void export(HttpServletResponse response, SysRole role) {
		List<SysRole> list = roleService.selectRoleList(role);
		ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
		util.exportExcel(response, list, "角色数据");
	}

	/**
	 * 根据角色编号获取详细信息
	 */
	@PreAuthorize("@ss.hasPermi('system:role:query')")
	@GetMapping(value = "/info/{roleId}")
	public AjaxResult getInfo(@PathVariable Long roleId) {
		roleService.checkRoleDataScope(roleId);
		return success(roleService.selectRoleById(roleId));
	}

	/**
	 * 新增角色
	 */
	@PreAuthorize("@ss.hasPermi('system:role:add')")
	@Log(title = "角色管理", businessType = BusinessType.INSERT)
	@PostMapping("/add")
	public AjaxResult add(@Validated @RequestBody SysRole role) {
		if (!roleService.checkRoleNameUnique(role)) {
			return error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
		} else if (!roleService.checkRoleKeyUnique(role)) {
			return error("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
		}
		role.setCreateBy(getUsername());
		return toAjax(roleService.insertRole(role));

	}

	/**
	 * 修改保存角色
	 */
	@PreAuthorize("@ss.hasPermi('system:role:edit')")
	@Log(title = "角色管理", businessType = BusinessType.UPDATE)
	@PostMapping("/edit")
	public AjaxResult edit(@Validated @RequestBody SysRole role) {
		roleService.checkRoleAllowed(role);
		roleService.checkRoleDataScope(role.getRoleId());
		if (!roleService.checkRoleNameUnique(role)) {
			return error("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
		} else if (!roleService.checkRoleKeyUnique(role)) {
			return error("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
		}
		role.setUpdateBy(getUsername());

		if (roleService.updateRole(role) > 0) {
			// 更新缓存用户权限
			LoginUser loginUser = getLoginUser();
			if (StringUtils.isNotNull(loginUser.getUser()) && !loginUser.getUser().isAdmin()) {
				loginUser.setUser(userService.selectUserByUserName(loginUser.getUser().getUserName()));
				loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
				tokenService.setLoginUser(loginUser);
			}
			return success();
		}
		return error("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
	}

	/**
	 * 修改保存数据权限
	 */
	@PreAuthorize("@ss.hasPermi('system:role:edit')")
	@Log(title = "角色管理", businessType = BusinessType.UPDATE)
	@PostMapping("/dataScope")
	public AjaxResult dataScope(@RequestBody SysRole role) {
		roleService.checkRoleAllowed(role);
		roleService.checkRoleDataScope(role.getRoleId());
		return toAjax(roleService.authDataScope(role));
	}

	/**
	 * 状态修改
	 */
	@PreAuthorize("@ss.hasPermi('system:role:edit')")
	@Log(title = "角色管理", businessType = BusinessType.UPDATE)
	@PostMapping("/changeStatus")
	public AjaxResult changeStatus(@RequestBody SysRole role) {
		roleService.checkRoleAllowed(role);
		roleService.checkRoleDataScope(role.getRoleId());
		role.setUpdateBy(getUsername());
		return toAjax(roleService.updateRoleStatus(role));
	}

	/**
	 * 删除角色
	 */
	@PreAuthorize("@ss.hasPermi('system:role:remove')")
	@Log(title = "角色管理", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{roleIds}")
	public AjaxResult remove(@PathVariable Long[] roleIds) {
		return toAjax(roleService.deleteRoleByIds(roleIds));
	}

	/**
	 * 获取角色选择框列表
	 */
	@PreAuthorize("@ss.hasPermi('system:role:query')")
	@GetMapping("/optionselect")
	public AjaxResult optionselect() {
		return success(roleService.selectRoleAll());
	}

	/**
	 * 查询已分配用户角色列表
	 */
	@PreAuthorize("@ss.hasPermi('system:role:list')")
	@GetMapping("/authUser/allocatedList")
	public TableDataInfo allocatedList(SysUser user) {
		startPage();
		List<SysUser> list = userService.selectAllocatedList(user);
		return getDataTable(list);
	}

	/**
	 * 查询未分配用户角色列表
	 */
	@PreAuthorize("@ss.hasPermi('system:role:list')")
	@GetMapping("/authUser/unallocatedList")
	public TableDataInfo unallocatedList(SysUser user) {
		startPage();
		List<SysUser> list = userService.selectUnallocatedList(user);
		return getDataTable(list);
	}

	/**
	 * 取消授权用户
	 */
	@PreAuthorize("@ss.hasPermi('system:role:edit')")
	@Log(title = "角色管理", businessType = BusinessType.GRANT)
	@PostMapping("/authUser/cancel")
	public AjaxResult cancelAuthUser(@RequestBody SysUserRole userRole) {
		return toAjax(roleService.deleteAuthUser(userRole));
	}

	/**
	 * 批量取消授权用户
	 */
	@PreAuthorize("@ss.hasPermi('system:role:edit')")
	@Log(title = "角色管理", businessType = BusinessType.GRANT)
	@PostMapping("/authUser/cancelAll")
	public AjaxResult cancelAuthUserAll(@RequestBody SysRole sysRole) {
		return toAjax(roleService.deleteAuthUsers(sysRole.getRoleId(), sysRole.getUserIds()));
	}

	/**
	 * 批量选择用户授权
	 */
	@PreAuthorize("@ss.hasPermi('system:role:edit')")
	@Log(title = "角色管理", businessType = BusinessType.GRANT)
	@PostMapping("/authUser/selectAll")
	public AjaxResult selectAuthUserAll(@RequestBody SysRole sysRole) {
		roleService.checkRoleDataScope(sysRole.getRoleId());
		return toAjax(roleService.insertAuthUsers(sysRole.getRoleId(), sysRole.getUserIds()));
	}

	/**
	 * 获取对应角色机构树列表
	 */
	@PreAuthorize("@ss.hasPermi('system:role:query')")
	@GetMapping(value = "/deptTree/{roleId}")
	public AjaxResult deptTree(@PathVariable("roleId") Long roleId) {
		AjaxResult ajax = AjaxResult.success();
		ajax.put("checkedKeys", deptService.selectDeptListByRoleId(roleId));
		ajax.put("depts", deptService.selectDeptTreeList(new SysDept()));
		return ajax;
	}
}
