package com.jkr.project.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.web.domain.BaseEntity;
import jakarta.validation.constraints.*;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 机构表 sys_dept
 *
 * <AUTHOR>
 */
@TableName("sys_dept")
public class SysDept extends BaseEntity {
	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 机构ID
	 */
	@TableId(value = "dept_id")
	private Long deptId;

	/**
	 * 父机构ID
	 */
	private Long parentId;

	/**
	 * 祖级列表
	 */
	private String ancestors;

	/**
	 * 机构名称
	 */
	private String deptName;

	/**
	 * 显示顺序
	 */
	private Integer orderNum;

	/**
	 * 负责人
	 */
	private String leader;

	/**
	 * 区域编码
	 */
	private String areaCode;
	/**
	 * 区域
	 */
	private String areaName;
	/**
	 * 部门级别：1省，2市（州），3县（区）
	 */
	private String areaLevel;

	/**
	 * 部门类型：1农业农村部门，2市场监管部门
	 */
	private String deptType;

	/**
	 * 联系电话
	 */
	private String phone;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 机构状态:0正常,1停用
	 */
	private String status;

	/**
	 * 删除标志（0代表存在 2代表删除）
	 */
	private String delFlag;
	/**
	 * 监管机构（0非监管，1监管）
	 */
	private String supervisionFlag;

	/**
	 * 父机构名称
	 */
	private String parentName;
	private Date beginCreateDate;    //开始注册时间
	private Date endCreateDate;        //结束注册时间
	private String loginAreaCode;

	public String getDeptType() {
		return deptType;
	}

	public void setDeptType(String deptType) {
		this.deptType = deptType;
	}

	public String getAreaLevel() {
		return areaLevel;
	}

	public void setAreaLevel(String areaLevel) {
		this.areaLevel = areaLevel;
	}

	public String getLoginAreaCode() {
		return loginAreaCode;
	}

	public void setLoginAreaCode(String loginAreaCode) {
		this.loginAreaCode = loginAreaCode;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public Date getBeginCreateDate() {
		return beginCreateDate;
	}

	public void setBeginCreateDate(Date beginCreateDate) {
		this.beginCreateDate = beginCreateDate;
	}

	public Date getEndCreateDate() {
		return endCreateDate;
	}

	public void setEndCreateDate(Date endCreateDate) {
		this.endCreateDate = endCreateDate;
	}

	/**
	 * 子机构
	 */
	private List<SysDept> children = new ArrayList<SysDept>();

	public String getSupervisionFlag() {
		return supervisionFlag;
	}

	public void setSupervisionFlag(String supervisionFlag) {
		this.supervisionFlag = supervisionFlag;
	}

	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getAncestors() {
		return ancestors;
	}

	public void setAncestors(String ancestors) {
		this.ancestors = ancestors;
	}

	@NotBlank(message = "机构名称不能为空")
	@Size(min = 0, max = 30, message = "机构名称长度不能超过30个字符")
	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	@NotNull(message = "显示顺序不能为空")
	public Integer getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}

	public String getLeader() {
		return leader;
	}

	public void setLeader(String leader) {
		this.leader = leader;
	}

	@Size(min = 0, max = 11, message = "联系电话长度不能超过11个字符")
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	@Email(message = "邮箱格式不正确")
	@Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(String delFlag) {
		this.delFlag = delFlag;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public List<SysDept> getChildren() {
		return children;
	}

	public void setChildren(List<SysDept> children) {
		this.children = children;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
				.append("deptId", getDeptId())
				.append("parentId", getParentId())
				.append("ancestors", getAncestors())
				.append("deptName", getDeptName())
				.append("orderNum", getOrderNum())
				.append("areaCode", getAreaCode())
				.append("leader", getLeader())
				.append("phone", getPhone())
				.append("email", getEmail())
				.append("status", getStatus())
				.append("delFlag", getDelFlag())
				.append("createBy", getCreateBy())
				.append("createTime", getCreateTime())
				.append("updateBy", getUpdateBy())
				.append("updateTime", getUpdateTime())
				.toString();
	}


}
