package com.jkr.project.system.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.jkr.framework.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 第三方授权表 sys_auth_user
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAuthUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 授权ID
     */
    private Long authId;

    /**
     * 第三方平台用户唯一ID
     */
    private String uuid;

    /**
     * 系统用户ID
     */
    private Long userId;

    /**
     * 登录账号
     */
    private String loginName;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户来源
     */
    private String source;
    /**
     *用户来源渠道id(微信小程序appid等)
     */
    private String sourceId;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 验证码
     */
    @TableField(exist = false)
    private String code;

}
