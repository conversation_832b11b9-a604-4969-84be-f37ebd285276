package com.jkr.project.system.service;

import java.util.List;

import com.jkr.project.system.domain.SysWechat;

/**
 * 微信小程序信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface ISysWechatService {
	/**
	 * 查询微信小程序信息
	 *
	 * @param id 微信小程序信息主键
	 * @return 微信小程序信息
	 */
	public SysWechat selectWechatById(Long id);

	/**
	 * 查询微信小程序信息列表
	 *
	 * @param wechat 微信小程序信息
	 * @return 微信小程序信息集合
	 */
	public List<SysWechat> selectWechatList(SysWechat wechat);

	/**
	 * 新增微信小程序信息
	 *
	 * @param wechat 微信小程序信息
	 * @return 结果
	 */
	public int insertWechat(SysWechat wechat);

	/**
	 * 修改微信小程序信息
	 *
	 * @param wechat 微信小程序信息
	 * @return 结果
	 */
	public int updateWechat(SysWechat wechat);

	/**
	 * 批量删除微信小程序信息
	 *
	 * @param ids 需要删除的微信小程序信息主键集合
	 * @return 结果
	 */
	public int deleteWechatByIds(List<Long> ids);

	/**
	 * 删除微信小程序信息信息
	 *
	 * @param id 微信小程序信息主键
	 * @return 结果
	 */
	public int deleteWechatById(Long id);

	/**
	 * 根据appid获取secret
	 *
	 * <AUTHOR>
	 * @date  2025/4/23 9:31
	 * @param appid  appid
	 * @return java.lang.String secret
	 **/
	String getSecretByAppid(String appid);

}
