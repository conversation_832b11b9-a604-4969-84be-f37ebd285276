package com.jkr.common.constant;

/**
 * 系统参数常量
 *
 * <AUTHOR>
 */
public class SysConfigConstants {
	/**
	 * 主框架页-默认皮肤样式名称
	 */
	public static final String SYS_INDEX_SKIN_NAME = "sys.index.skinName";
	/**
	 * 主框架页-侧边栏主题
	 */
	public static final String SYS_INDEX_SIDE_THEME = "sys.index.sideTheme";
	/**
	 * 用户管理-账号初始密码
	 */
	public static final String SYS_USER_INIT_PASSWORD = "sys.user.initPassword";
	/**
	 * 账号自助-验证码开关
	 */
	public static final String SYS_ACCOUNT_CAPTCHA_ENABLED = "sys.account.captchaEnabled";
	/**
	 * 账号自助-是否开启用户注册功能
	 */
	public static final String SYS_ACCOUNT_REGISTER_USER = "sys.account.registerUser";
	/**
	 * 用户登录-黑名单列表
	 */
	public static final String SYS_LOGIN_BLACK_IP_LIST = "sys.login.blackIPList";
	/**
	 * 区域管理-树形结构数据根节点编码
	 */
	public static final String SYS_AREA_TREE_ROOT_CODE = "sys.area.treeRootCode";
	/**
	 * 高德地图-开发者key
	 */
	public static final String AMAP_KEY = "amap.key";
	/**
	 * 高德地图-JSAPI 的版本
	 */
	public static final String AMAP_VERSION = "amap.version";
	/**
	 * 系统PC网站配置
	 */
	public static final String SYS_PC_WEBSITE_CONFIG = "sys.pc.website.config";
	/**
	 * 账号自助-短信验证码开关
	 */
	public static final String SYS_ACCOUNT_SMS_ENABLED = "sys.account.sms.enabled";
	/**
	 * 账号自助-短信验证码发送开关
	 */
	public static final String SYS_ACCOUNT_SMS_SEND_ENABLED = "sys.account.sms.send.enabled";
	/**
	 * 账号自助-短信验证码长度
	 */
	public static final String SYS_ACCOUNT_SMS_LENGTH = "sys.account.sms.length";
	/**
	 * 账号自助-短信验证码有效时间
	 */
	public static final String SYS_ACCOUNT_SMS_VALIDITY_TIME = "sys.account.sms.validity.time";
	/**
	 * 微信-小程序ID
	 */
	public static final String WECHAT_APPID = "wechat.appid";
	/**
	 * 微信-小程序密钥
	 */
	public static final String WECHAT_SECRET = "wechat.secret";
}
