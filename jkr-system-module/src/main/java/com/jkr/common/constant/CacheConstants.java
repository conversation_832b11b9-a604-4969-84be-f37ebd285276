package com.jkr.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public final class CacheConstants {
	/**
	 * 登录用户 redis key
	 */
	public static final String LOGIN_TOKEN_KEY = "login_tokens:";
	/**
	 * 图形验证码 redis key
	 */
	public static final String CAPTCHA_CODE_KEY = "captcha_codes:";
	/**
	 * 短信验证码 redis key
	 */
	public static final String SMS_CODE_KEY = "sms_codes:";
	/**
	 * 参数管理 cache key
	 */
	public static final String SYS_CONFIG_KEY = "sys_config:";
	/**
	 * 字典管理 cache key
	 */
	public static final String SYS_DICT_KEY = "sys_dict:";
	/**
	 * 防重提交 redis key
	 */
	public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";
	/**
	 * 限流 redis key
	 */
	public static final String RATE_LIMIT_KEY = "rate_limit:";
	/**
	 * 登录账户密码错误次数 redis key
	 */
	public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";
	/**
	 * 区划树形结构数据 cache key
	 */
	public static final String SYS_AREA_TREE_KEY = "sys_area_tree:";
	/**
	 * 微信
	 */
	public static final String WECHAT = "wechat:";
	/**
	 * 微信 access_token
	 */
	public static final String WX_ACCESS_TOKEN = "wx_access_token:";

	/**
	 * 微信secret
	 **/
	public static final String WX_SECRET = "wx_secret:";
	private CacheConstants() {
	}
}
