package com.jkr.common.constant;

/**
 * sso项目常量类
 *
 * <AUTHOR>
 * @since 2020/6/22 15:42
 */
public interface SsoConstant {
    public static final String ROOT_NAME = "SYS";
    public static final String DATA_SCOPE = "SYS:DATA:SCOPE:";
    public static final String USER_INFO = "SYS:USER:INFO";
    public static final String TOKEN_EXPIRE = "SYS:TOKEN:EXPIRE:";
    public static final String USER_LOGIN_NUM = "SYS:USER:LOGINNUM";
    public static final String RE_SUBMIT = "SYS:RESUBMIT:";

    public static final String SSO_TOKEN="ssoToken";
    public static final String CLIENT_APP_ID="appId";
    public static final String CLIENT_APP_SECRET="appSecret";
    public static final String USER_ID="userId";
    public static final String LOGIN_NAME="loginName";
    public static final String AUTHORIZATION="Authorization";
    public static final Integer SALT_LENGTH=32;//秘钥加盐随机数长度
}
