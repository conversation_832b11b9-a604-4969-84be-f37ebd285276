package com.jkr.common.filter;

import com.jkr.common.constant.Constants;
import com.jkr.common.utils.http.HttpHelper;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.io.*;

/**
 * 构建可重复读取inputStream的request
 *
 * <AUTHOR>
 */
public class RepeatedlyRequestWrapper extends HttpServletRequestWrapper {
	private final byte[] body;

	public RepeatedlyRequestWrapper(HttpServletRequest request, ServletResponse response) throws IOException {
		super(request);
		request.setCharacterEncoding(Constants.UTF8);
		response.setCharacterEncoding(Constants.UTF8);

		body = HttpHelper.getBodyString(request).getBytes(Constants.UTF8);
	}

	@Override
	public BufferedReader getReader() throws IOException {
		return new BufferedReader(new InputStreamReader(getInputStream()));
	}

	@Override
	public ServletInputStream getInputStream() throws IOException {
		final ByteArrayInputStream bais = new ByteArrayInputStream(body);
		return new ServletInputStream() {
			@Override
			public int read() throws IOException {
				return bais.read();
			}

			@Override
			public int available() throws IOException {
				return body.length;
			}

			@Override
			public boolean isFinished() {
				return false;
			}

			@Override
			public boolean isReady() {
				return false;
			}

			@Override
			public void setReadListener(ReadListener readListener) {

			}
		};
	}
}
