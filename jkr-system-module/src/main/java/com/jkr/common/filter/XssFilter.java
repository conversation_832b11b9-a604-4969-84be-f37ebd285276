package com.jkr.common.filter;

import com.jkr.common.enums.HttpMethod;
import com.jkr.common.utils.StringUtils;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 防止XSS攻击的过滤器
 *
 * <AUTHOR>
 */
public class XssFilter implements Filter {
	/**
	 * 排除链接
	 */
	public List<String> excludes = new ArrayList<>();

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		String tempExcludes = filterConfig.getInitParameter("excludes");
		if (StringUtils.isNotEmpty(tempExcludes)) {
			String[] urls = tempExcludes.split(",");
			for (String url : urls) {
				excludes.add(url);
			}
		}
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest req = (HttpServletRequest) request;
		HttpServletResponse resp = (HttpServletResponse) response;
		if (handleExcludeURL(req, resp)) {
			chain.doFilter(request, response);
			return;
		}
		XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(request);
		chain.doFilter((ServletRequest) xssRequest, response);
	}

	private boolean handleExcludeURL(HttpServletRequest request, HttpServletResponse response) {
		String url = request.getServletPath();
		String method = request.getMethod();
		// GET DELETE 不过滤
		if (method == null || HttpMethod.GET.matches(method) || HttpMethod.DELETE.matches(method)) {
			return true;
		}
		return StringUtils.matches(url, excludes);
	}

	@Override
	public void destroy() {

	}
}