package com.jkr.common.utils;

import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.BasicHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.util.Map;

/**
 * ClassName:SXRequest
 * Function: HTTP请求类，携带证书和不带证书
 * Date:     2019-12-27 下午3:22:33
 *
 * <AUTHOR>
 */
public class SXRequest {

	public static final int connectTimeoutMs = 20000, readTimeoutMs = 20000;

	public static String requestOnce(String url, String data, Map<String, String> headerMap) throws Exception {
		return requestOnce(url, data, headerMap, connectTimeoutMs, readTimeoutMs);
	}

	/**
	 * 请求，只请求一次，不做重试
	 *
	 * @param url
	 * @param data
	 * @param headerMap 请求头
	 * @param connectTimeoutMs
	 * @param readTimeoutMs
	 * @return
	 * @throws Exception
	 */
	private static String requestOnce(final String url, String data, Map<String, String> headerMap, int connectTimeoutMs, int readTimeoutMs) throws Exception {
		BasicHttpClientConnectionManager connManager = setSocketFactory();
		HttpClient httpClient = HttpClientBuilder.create()
				.setConnectionManager(connManager)
				.build();

		HttpPost httpPost = new HttpPost(url);

		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(readTimeoutMs).setConnectTimeout(connectTimeoutMs).build();
		httpPost.setConfig(requestConfig);
		StringEntity postEntity;

		// 防止被当成攻击添加的
		//httpPost.setHeader("User-Agent", USERAGENT);
		postEntity = new StringEntity(data, ContentType.APPLICATION_JSON);
		if(null != headerMap){
			for (String key : headerMap.keySet()) {
				httpPost.addHeader(key, headerMap.get(key));
			}
		}

		httpPost.setEntity(postEntity);

		HttpResponse httpResponse = httpClient.execute(httpPost);
		HttpEntity httpEntity = httpResponse.getEntity();
		return EntityUtils.toString(httpEntity, Consts.UTF_8);
	}

	private static BasicHttpClientConnectionManager setSocketFactory() {
		return new BasicHttpClientConnectionManager(
				RegistryBuilder.<ConnectionSocketFactory>create()
						.register("http", PlainConnectionSocketFactory.getSocketFactory())
						.register("https", SSLConnectionSocketFactory.getSocketFactory())
						.build(),
				null,
				null,
				null
		);
	}

}
