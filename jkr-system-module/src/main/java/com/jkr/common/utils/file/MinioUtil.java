package com.jkr.common.utils.file;

import com.jkr.project.system.domain.vo.MinioInfo;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MinioUtil {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private HttpServletResponse response;

    @Value("${com.jkr.minio.bucketName}")
    private String bucketName;

    /**
     * 对存储桶进行创建
     *
     * @author: jkr
     * @date: 2023/3/23 14:03
     * @param: []
     * @since: 1.0
     * @return: void
     **/
    @SneakyThrows
    private void initBucket(){
        //1 判断桶是否存在
        boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        //2 不存在则创建
        if (!bucketExists) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        }
    }

    /**
     * 获取上传目标的文件名
     *
     * @author: jkr
     * @date: 2023/3/23 14:10
     * @param: [originalFileName, filePath]
     * @since: 1.0
     * @return: java.lang.String
     **/
    private String buildOutFileName(String originalFileName,String filePath){
        //输出文件名（路径）处理
        StringBuilder outFileNameBuilder = new StringBuilder();
        outFileNameBuilder.append(filePath);
        outFileNameBuilder.append("/");
        outFileNameBuilder.append(originalFileName);
        //获取上传后的文件名 涵路径
        return FileUtils.formatFileName(bucketName,outFileNameBuilder.toString());
    }

    /**
     * 文件上传到minio服务器
     *
     * @author: jkr
     * @date: 2023/3/23 14:29
     * @param: [objectName, stream]
     * @since: 1.0
     * @return: com.sx.minio.domain.MinioInfo
     **/
    @SneakyThrows
    private MinioInfo minioUpload(String originalFileName,String filePath,InputStream stream){
        //获取上传后的文件名 仅文件 不涵路径
        String objectName = buildOutFileName(originalFileName,filePath);
        //获取文件大小
        long fileSize = stream.available();
        //1 初始化bucket
        initBucket();
        //2 文件上传  参数校验 和分片上传
        PutObjectArgs objectArgs = PutObjectArgs.builder().object(objectName)
                .bucket(bucketName)
                .stream(stream, fileSize, -1).build();
        minioClient.putObject(objectArgs);
        return new MinioInfo(bucketName, objectName, FileUtils.formatFileSize(fileSize));
    }

    /**
     * 输出文件
     *
     * @author: jkr
     * @date: 2023/3/23 15:50
     * @param: [stream, contentType, objectName]
     * @since: 1.0
     * @return: void
     **/
    @SneakyThrows
    private void outPutFile(InputStream stream,String objectName,String contentType){
        response.setHeader("content-type", contentType);
        response.setContentType("application/octet-stream");
        String[] split = objectName.split("/");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(split[split.length - 1], "UTF-8"));
        IOUtils.copy(stream, response.getOutputStream());
        stream.close();
    }



    /**
     * 文件上传到Minio服务器
     *
     * @author: jkr
     * @date: 2023/3/23 13:52
     * @param: [multipartFile, filePath 桶内路径]
     * @since: 1.0
     * @return: com.sx.minio.domain.MinioInfo
     **/
    @SneakyThrows
    public MinioInfo upload(MultipartFile multipartFile,String filePath){
        //对文件进行上传
        return minioUpload(multipartFile.getOriginalFilename(),filePath,multipartFile.getInputStream());
    }

    /**
     * base64文件上传
     *
     * @author: jkr
     * @date: 2023/3/23 13:55
     * @param: [base64Str 文件, originalFileName 原文件名, filePath 桶内路径]
     * @since: 1.0
     * @return: com.sx.minio.domain.MinioInfo
     **/
    @SneakyThrows
    public MinioInfo upload(String base64Str, String originalFileName, String filePath) {
        //判断baose64数据是否为空
        if (StringUtils.isNotBlank(base64Str)) {
            String base65StrLast = base64Str.substring(base64Str.indexOf("base64,") + 7);
            byte[] byt = Base64.decodeBase64(base65StrLast);
            for (int i = 0; i < byt.length; ++i) {
                if (byt[i] < 0) {
                    byt[i] += 256;
                }
            }
            //字节转换为输入流
            InputStream stream = new ByteArrayInputStream(byt);
            return minioUpload(originalFileName,filePath,stream);
        }
        return null;
    }

    /**
     * 对目标文件进行移除
     *
     * @author: jkr
     * @date: 2023/3/23 14:38
     * @param: [objectName]
     * @since: 1.0
     * @return: void
     **/
    @SneakyThrows
    public void removeObject(String objectName) {
        RemoveObjectArgs removeBucketArgs = RemoveObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName).build();
        minioClient.removeObject(removeBucketArgs);
    }

    /**
     * 批量删除文件 返回异常列表
     *
     * @author: jkr
     * @date: 2023/3/23 14:46
     * @param: [objectNames]
     * @since: 1.0
     * @return: java.util.List<io.minio.messages.DeleteError>
     **/
    @SneakyThrows
    public List<String> removeObjects(List<String> objectNames) {
        List<String> deleteErrors = new ArrayList<>();
        List<DeleteObject> deleteObjects = objectNames.stream().map(value -> new DeleteObject(value)).collect(Collectors.toList());
        //对上传异常反馈进行记录
        Iterable<Result<DeleteError>> results =
                minioClient.removeObjects(
                        RemoveObjectsArgs
                                .builder()
                                .bucket(bucketName)
                                .objects(deleteObjects)
                                .build());
        for (Result<DeleteError> result : results) {
            DeleteError error = result.get();
            deleteErrors.add(error.objectName());
        }
        return deleteErrors;
    }


    /**
     * 生成一个给HTTP GET请求用的URL。浏览器/移动端的客户端可以用这个URL进行下载，默认有效期是1天,最大有效期7天,以秒为单位
     *
     * @author: jkr
     * @date: 2023/3/23 14:58
     * @param: [objectName, expires]
     * @since: 1.0
     * @return: java.lang.String
     **/
    @SneakyThrows
    public String presignedGetObject(String objectName, Integer expires) {
        if(checkFileIsExist(objectName)) {//新版minio获取图片路径，路径是否存在图片没有区分，故获取前判断minio是否存在此文件
            if (expires == null) {
                expires = 86400;
            }
            GetPresignedObjectUrlArgs getPresignedObjectUrlArgs = GetPresignedObjectUrlArgs
                    .builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(expires)// 单位秒
                    .method(Method.GET)
                    .build();
            return minioClient.getPresignedObjectUrl(getPresignedObjectUrlArgs);
        }
        return null;
    }

    /**
     * 判断minio是否存在此文件
     *
     * <AUTHOR>
     * @date 2024年04月24日 16:05
     * @param objectName
     * @return java.lang.Boolean
     */
    public boolean checkFileIsExist(String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder().bucket(bucketName).object(objectName).build()
            );
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 校验文件是否存在
     *
     * @author: jkr
     * @date: 2023/3/23 15:09
     * @param: [objectName]
     * @since: 1.0
     * @return: boolean
     **/
    public boolean statObject(String objectName) {
        StatObjectArgs statObjectArgs = StatObjectArgs
                .builder()
                .bucket(bucketName)
                .object(objectName).build();
        try{
            //文件是否存在  文件不存在会抛出异常 -- 官方解释
            minioClient.statObject(statObjectArgs);
            return true;
        }catch(Exception e){
            return false;
        }
    }

    /**
     * 查询存储桶下的全部文件
     *
     * @author: jkr
     * @date: 2023/3/23 15:17
     * @param: []
     * @since: 1.0
     * @return: java.util.List<com.sx.minio.domain.MinioInfo>
     **/
    @SneakyThrows
    public List<MinioInfo> listObjects() {
        ListObjectsArgs listObjectsArgs = ListObjectsArgs
                .builder()
                .recursive(true)//递归查询多层级元素
                .bucket(bucketName)
                .build();
        //.startAfter("2021")
        //.prefix("2") // 指定前缀
        //.maxKeys(100) // 最大数量
        Iterable<Result<Item>> objects = minioClient.listObjects(listObjectsArgs);
        List<MinioInfo> itemList = new ArrayList<MinioInfo>();
        for (Result<Item> object : objects) {
            Item item = object.get();
            MinioInfo info = new MinioInfo(bucketName, item.objectName(), FileUtils.formatFileSize(item.size()));
            itemList.add(info);
        }
        return itemList;
    }

    /**
     * 文件下载
     *
     * @author: jkr
     * @date: 2023/3/23 15:44
     * @param: [objectName, contentType]
     * @since: 1.0
     * @return: void
     **/
    @SneakyThrows
    public void download(String objectName){
        StatObjectArgs statObjectArgs = StatObjectArgs
                .builder()
                .bucket(bucketName)
                .object(objectName).build();
        //文件是否存在  文件不存在会抛出异常 -- 官方解释
        StatObjectResponse objectResponse = minioClient.statObject(statObjectArgs);
        GetObjectArgs getObjectArgs = GetObjectArgs
                .builder()
                .bucket(bucketName)
                .object(objectName)
                .build();
        InputStream stream = minioClient.getObject(getObjectArgs);
        outPutFile(stream,objectName,objectResponse.contentType());
    }

    /**
     * 获取base64文件
     *
     * @author: jkr
     * @date: 2023/3/23 15:54
     * @param: [objectName]
     * @since: 1.0
     * @return: java.lang.String
     **/
    @SneakyThrows
    public String getBase64File(String objectName) {
        GetObjectArgs getObjectArgs = GetObjectArgs
                .builder()
                .bucket(bucketName)
                .object(objectName)
                .build();
        InputStream stream = minioClient.getObject(getObjectArgs);
        // 读取图片字节数组
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
        byte[] buff = new byte[100];
        int rc = 0;
        while ((rc = stream.read(buff, 0, 100)) > 0) {
            swapStream.write(buff, 0, rc);
        }
        stream.close();
        // 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        byte[] data = swapStream.toByteArray();
        return new String(Base64.encodeBase64(data));
    }
}
