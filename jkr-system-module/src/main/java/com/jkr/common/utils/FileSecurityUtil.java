package com.jkr.common.utils;

import org.apache.tika.metadata.HttpHeaders;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.TikaMetadataKeys;
import org.apache.tika.mime.MediaType;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.helpers.DefaultHandler;

import java.io.*;
import java.util.Arrays;
import java.util.HashMap;

/**
 * 文件白名单校验
 * <AUTHOR>
 * @version 2020-05-09
 */
public class FileSecurityUtil {

	//允许上传的文件类型,包括文件内容类型
	private static String[] whiteList = {"image/jpeg","image/png","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-powerpoint","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/vnd.ms-works","application/x-rar-compressed","application/x-zip-compressed","application/zip"};
	//尾缀过滤白名单
	private static String whiteSuffixs = "jpeg,jpg,png,pdf,doc,docx,xls,xlsx,ppt,pptx,rar,zip,txt,mp4,apk";
	private static String ckeditorFile = "jpeg,jpg,png";

	/**
	 * @title:  checkWhiteFile
	 * @Author: lvpeng
	 * @Date: 2020-05-09
	 * @Description: 文件校验-过滤白名单
	 * @Param:  multipartFile
	 * @return:  boolean
	 */
	public static boolean checkWhiteFile(MultipartFile multipartFile) throws IOException {

		File toFile = null;
		if (!multipartFile.equals("") && multipartFile.getSize() > 0) {
			InputStream ins = null;
			ins = multipartFile.getInputStream();
			toFile = new File(multipartFile.getOriginalFilename());
			inputStreamToFile(ins, toFile);
			ins.close();
		}

		AutoDetectParser parser = new AutoDetectParser();
		parser.setParsers(new HashMap<MediaType, Parser>());
		Metadata metadata = new Metadata();
		metadata.add(TikaMetadataKeys.RESOURCE_NAME_KEY, toFile.getName());
		InputStream stream;
		try {
			stream = new FileInputStream(toFile);
			parser.parse(stream, new DefaultHandler(), metadata, new ParseContext());
			stream.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		String contentType = metadata.get(HttpHeaders.CONTENT_TYPE);
		return Arrays.asList(whiteList).contains(contentType);
	}

	//获取流文件
	private static void inputStreamToFile(InputStream ins, File file) {
		try {
			OutputStream os = new FileOutputStream(file);
			int bytesRead = 0;
			byte[] buffer = new byte[8192];
			while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
				os.write(buffer, 0, bytesRead);
			}
			os.close();
			ins.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * @title:  checkWhiteSuffixFile
	 * @Author: ZMY
	 * @Date: 2020-05-11
	 * @Description: 文件校验-尾缀过滤白名单
	 * @Param:  MultipartFile
	 * @return:  boolean
	 */
	public static boolean checkWhiteSuffixFile(MultipartFile file){
		boolean allowFlag = false;
		//取文件后缀
		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
		//如果文件在黑名单中
		if(whiteSuffixs.indexOf(suffix)>0){
			allowFlag = true;
		}
		return allowFlag;
	}
	/**
	* @title: checkCkeditorFile
	* @author: ZYJ
	* @date: 2020/7/28 14:11
	* @description: 富文本上传图片过滤
	* @param: [file]
	* @return: boolean
	*/
	public static boolean checkCkeditorFile(MultipartFile file){
		boolean allowFlag = false;
		//取文件后缀
		String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
		//如果文件在黑名单中
		if(ckeditorFile.indexOf(suffix)>0){
			allowFlag = true;
		}
		return allowFlag;
	}
}

