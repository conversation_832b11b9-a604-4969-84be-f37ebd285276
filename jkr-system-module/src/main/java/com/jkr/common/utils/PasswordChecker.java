package com.jkr.common.utils;

import java.util.HashSet;
import java.util.Set;

/**
 * PasswordChecker 密码强度检查
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-15 09:41
 */
public final class PasswordChecker {
	// 密码最小长度
	private static final int MIN_LENGTH = 8;
	// 密码最大长度
	private static final int MAX_LENGTH = 20;
	// 最少相异字符数
	private static final int MIN_UNIQUE_CHARS = 3;
	// 最少字母字符数
	private static final int MIN_LETTER_CHARS = 3;
	// 最少数字字符数
	private static final int MIN_DIGIT_CHARS = 3;
	// 最多重复字符数
	private static final int MAX_REPEAT_CHARS = 2;
	// 最多连续字符数
	private static final int MAX_CONSECUTIVE_CHARS = 2;
	// 特殊字符
	private static final String SPECIAL_CHARS = "~!@#$%^&*_-+=`|\\(){}[]:;\"'<>,.?/";

	private PasswordChecker() {
	}

	/**
	 * 密码强度检查
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	public static boolean isPasswordValid(String password) {
		// 检查密码长度
		if (password.length() < MIN_LENGTH || password.length() > MAX_LENGTH) {
			return false;
		}
		// 检查密码包含的字符类别
		if (!containsRequiredCharacterTypes(password)) {
			return false;
		}
		// 检查最多重复字符数
		if (hasExcessiveRepeats(password)) {
			return false;
		}
		// 检查最多连续字符数
		if (hasExcessiveConsecutives(password)) {
			return false;
		}
		// 检查最少相异字符数
		if (countUniqueChars(password) < MIN_UNIQUE_CHARS) {
			return false;
		}
		// 检查最少字母字符数
		if (countLetterChars(password) < MIN_LETTER_CHARS) {
			return false;
		}
		// 检查最少数字字符数
		if (countDigitChars(password) < MIN_DIGIT_CHARS) {
			return false;
		}

		return true;
	}

	/**
	 * 检查密码是否包含至少3个类别的字符（大小写字母、数字、特殊字符）
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	private static boolean containsRequiredCharacterTypes(String password) {
		boolean hasLower = false;
		boolean hasUpper = false;
		boolean hasDigit = false;
		boolean hasSpecial = false;
		for (char c : password.toCharArray()) {
			if (Character.isLowerCase(c)) {
				hasLower = true;
			} else if (Character.isUpperCase(c)) {
				hasUpper = true;
			} else if (Character.isDigit(c)) {
				hasDigit = true;
			} else if (SPECIAL_CHARS.contains(String.valueOf(c))) {
				hasSpecial = true;
			} else {
				hasSpecial = true;
			}
		}
		int categoryCount = 0;
		if (hasLower) {
			categoryCount++;
		}
		if (hasUpper) {
			categoryCount++;
		}
		if (hasDigit) {
			categoryCount++;
		}
		if (hasSpecial) {
			categoryCount++;
		}
		return categoryCount >= 3;
	}

	/**
	 * 检查密码中是否有超过最大重复字符数
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	private static boolean hasExcessiveRepeats(String password) {
		for (int i = 0; i < password.length() - MAX_REPEAT_CHARS; i++) {
			char currentChar = password.charAt(i);
			int repeatCount = 1;
			for (int j = i + 1; j < password.length(); j++) {
				if (password.charAt(j) == currentChar) {
					repeatCount++;
					if (repeatCount > MAX_REPEAT_CHARS) {
						return true;
					}
				} else {
					break;
				}
			}
		}
		return false;
	}

	/**
	 * 检查密码中是否有超过最大连续字符数
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	private static boolean hasExcessiveConsecutives(String password) {
		for (int i = 0; i < password.length() - MAX_CONSECUTIVE_CHARS; i++) {
			char currentChar = password.charAt(i);
			int consecutiveCount = 1;
			for (int j = i + 1; j < password.length(); j++) {
				if (password.charAt(j) == currentChar + consecutiveCount) {
					consecutiveCount++;
					if (consecutiveCount > MAX_CONSECUTIVE_CHARS) {
						return true;
					}
				} else {
					break;
				}
			}
		}
		return false;
	}

	/**
	 * 计算密码中相异字符的数量
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	private static int countUniqueChars(String password) {
		Set<Character> uniqueChars = new HashSet<>();
		for (char c : password.toCharArray()) {
			uniqueChars.add(c);
		}
		return uniqueChars.size();
	}

	/**
	 * 计算密码中字母字符的数量
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	private static int countLetterChars(String password) {
		int count = 0;
		for (char c : password.toCharArray()) {
			if (Character.isLetter(c)) {
				count++;
			}
		}
		return count;
	}

	/**
	 * 计算密码中数字字符的数量
	 *
	 * @param password 密码
	 * @return true：通过检查，false：未通过检查
	 */
	private static int countDigitChars(String password) {
		int count = 0;
		for (char c : password.toCharArray()) {
			if (Character.isDigit(c)) {
				count++;
			}
		}
		return count;
	}
}
