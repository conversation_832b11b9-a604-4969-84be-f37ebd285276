package com.jkr.common.utils.uuid;

import com.github.yitter.idgen.YitIdHelper;
import org.springframework.beans.factory.annotation.Value;

/**
 * ID生成器工具类
 *
 * <AUTHOR>
 */
//@Component
@Deprecated // zwd 20250512 使用yitter
public class IdUtils {
    /**
     * 数据中心ID
     */
    private static long dataCenterId;

    /**
     * 机器ID
     */
    private static long workerId;

    private static SnowflakeIdGenerator generator;

    /**
     * 获取雪花生成器实例对象
     *
     * @return SnowflakeIdGenerator
     */
    public static SnowflakeIdGenerator getSnowGenerator() {
        if (generator == null) {
            synchronized (SnowflakeIdGenerator.class) {
                if (generator == null) {
                    generator = new SnowflakeIdGenerator(dataCenterId, workerId);
                }
            }
        }
        return generator;
    }

    @Value("${com.jkr.snowflake.dataCenterId}")
    public void setDataCenterId(long dataCenterId) {
        IdUtils.dataCenterId = dataCenterId;
    }

    @Value("${com.jkr.snowflake.workerId}")
    public void setWorkerId(long workerId) {
        IdUtils.workerId = workerId;
    }

    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 随机UUID
     */
    public static String fastUUID() {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID() {
        return UUID.fastUUID().toString(true);
    }

    /**
     * 获取雪花算法ID
     *
     * @return ID
     */
    public static Long getSnowflakeId() {
        //传统雪花算法
        //return getSnowGenerator().nextId();
        //雪花漂移算法
        return YitIdHelper.nextId();
    }
}
