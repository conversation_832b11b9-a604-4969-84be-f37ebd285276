/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.jkr.common.utils.sms;

/**
 * 封装各种生成唯一性ID算法的工具类.
 * 
 * <AUTHOR>
 * @version 2013-01-15
 */
public class CaptchaUtils {

	/**
	 * 创建指定数量的随机字符串
	 * 
	 * @param numberFlag
	 *            是否是数字
	 * @param length
	 * @return
	 */
	public static String crate(boolean numberFlag, int length) {
		String retStr = "";
		String strTable = numberFlag ? "1234567890" : "1234567890abcdefghijkmnpqrstuvwxyz";
		int len = strTable.length();
		boolean bDone = true;
		do {
			retStr = "";
			int count = 0;
			for (int i = 0; i < length; i++) {
				double dblR = Math.random() * len;
				int intR = (int) Math.floor(dblR);
				char c = strTable.charAt(intR);
				if (('0' <= c) && (c <= '9')) {
					count++;
				}
				retStr += strTable.charAt(intR);
			}
			if (count >= 2) {
				bDone = false;
			}
		} while (bDone);
		return retStr;
	}
	
	public static void main(String[] argc) {
		System.out.println(CaptchaUtils.crate(true,4));
		System.out.println(CaptchaUtils.crate(true,6));
		System.out.println(CaptchaUtils.crate(false,4));
		System.out.println(CaptchaUtils.crate(false,6));
	}

}
