/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.jkr.common.utils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.validation.ConstraintViolation;
import javax.validation.GroupSequence;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import javax.validation.groups.Default;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 字段校验工具类
 */
public class ValidationUtils {

	public interface Add {
	}

	public interface Delete{
	}

	public interface Update {
	}

	public interface Search {
	}

	@GroupSequence( { Default.class, Add.class, Delete.class, Update.class, Search.class})
	public interface Group {
	}

	/**
	 * 返回错误值以及数据
	 *
	 * @param: result
	 * @return: list
	 */
	public static <T> List<Map<String, Object>> validationResult(T destinationClass) {
		ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
		Validator validator = vf.getValidator();
		Set<ConstraintViolation<T>> set = validator.validate(destinationClass);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (ConstraintViolation<T> constraintViolation : set) {
			map = new LinkedHashMap<String, Object>();
			map.put("fieldName", constraintViolation.getPropertyPath().toString());
			map.put("fieldValue", constraintViolation.getInvalidValue());
			map.put("message", constraintViolation.getMessage());
			list.add(map);
		}
		return list;
	}

	/**
	 * 返回错误值以及数据
	 *
	 * @param: result
	 * @param: clazz
	 * @return: list
	 */
	public static <T> List<Map<String, Object>> validationResult(T destinationClass, String group) {
		ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
		Validator validator = vf.getValidator();
		Set<ConstraintViolation<T>> set = null;
		if("add".equals(group)){
			set = validator.validate(destinationClass,Add.class);
		}else if("update".equals(group)){
			set = validator.validate(destinationClass,Update.class);
		}else if("delete".equals(group)){
			set = validator.validate(destinationClass,Delete.class);
		}else if("search".equals(group)){
			set = validator.validate(destinationClass,Search.class);
		}else{
			set = validator.validate(destinationClass);
		}
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (ConstraintViolation<T> constraintViolation : set) {
			map = new LinkedHashMap<String, Object>();
			map.put("fieldName", constraintViolation.getPropertyPath().toString());
			map.put("fieldValue", constraintViolation.getInvalidValue());
			map.put("message", constraintViolation.getMessage());
			list.add(map);
		}
		return list;
	}

	/**
	 * 返回错误值以及数据
	 *
	 * @param: result
	 * @param: exception 例外字段
	 * @return: list
	 */
	public static <T> List<Map<String, Object>> validationResult(T destinationClass, ArrayList<String> exception) {
		ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
		Validator validator = vf.getValidator();
		Set<ConstraintViolation<T>> set = validator.validate(destinationClass);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (ConstraintViolation<T> constraintViolation : set) {
			map = new LinkedHashMap<String, Object>();
			map.put("fieldName", constraintViolation.getPropertyPath().toString());
			map.put("fieldValue", constraintViolation.getInvalidValue());
			map.put("message", constraintViolation.getMessage());
			if (!exception.contains(constraintViolation.getPropertyPath().toString())) {
				list.add(map);
			}
		}
		return list;
	}

	/**
	 * 返回错误值以及数据
	 *
	 * @param: result
	 * @param: clazz
	 * @return: list
	 */
	public static <T> List<Map<String, Object>> validationResultList(JSONArray jsonArray, Class<T> clazz) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (int i = 0; i < jsonArray.size(); i++) {
			map = new LinkedHashMap<String, Object>();
			JSONObject jsonObject = jsonArray.getJSONObject(i);
			T bean = (T) JSONObject.toJavaObject(jsonObject, clazz);
			List<Map<String, Object>> errList = validationResult(bean);
			if(errList.size()>0){
				map.put("error_" + (i + 1), errList);
				list.add(map);
			}
		}
		return list;
	}

	/**
	 * 返回错误值以及数据
	 *
	 * @param: result
	 * @param: clazz
	 * @return: list
	 */
	public static <T> List<Map<String, Object>> validationResultList(JSONArray jsonArray, Class<T> clazz, String group) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (int i = 0; i < jsonArray.size(); i++) {
			map = new LinkedHashMap<String, Object>();
			JSONObject jsonObject = jsonArray.getJSONObject(i);
			T bean = (T) JSONObject.toJavaObject(jsonObject, clazz);
			List<Map<String, Object>> errList = validationResult(bean,group);
			if(errList.size()>0){
				map.put("error_" + (i + 1), errList);
				list.add(map);
			}
		}
		return list;
	}



	/**2018-06-05新加验证方法**/
	public static <T> List<Map<String, Object>> validationResultList(JSONArray jsonArray, Class<T> clazz, Class<?>... groups) {
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (int i = 0; i < jsonArray.size(); i++) {
			map = new LinkedHashMap<String, Object>();
			JSONObject jsonObject = jsonArray.getJSONObject(i);
			T bean = (T) JSONObject.toJavaObject(jsonObject, clazz);
			List<Map<String, Object>> errList = validationResult(bean,groups);
			if(errList.size()>0){
				map.put("error_" + (i + 1), errList);
				list.add(map);
			}
		}
		return list;
	}

	/**
	 * 返回错误值以及数据
	 *
	 * @param: result
	 * @param: clazz
	 * @return: list
	 */
	public static <T> List<Map<String, Object>> validationResult(T destinationClass,  Class<?>... groups) {
		ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
		Validator validator = vf.getValidator();
		Set<ConstraintViolation<T>> set = null;
		set = validator.validate(destinationClass,groups);
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map;
		for (ConstraintViolation<T> constraintViolation : set) {
			map = new LinkedHashMap<String, Object>();
			map.put("fieldName", constraintViolation.getPropertyPath().toString());
			map.put("fieldValue", constraintViolation.getInvalidValue());
			map.put("message", constraintViolation.getMessage());
			list.add(map);
		}
		return list;
	}
}
