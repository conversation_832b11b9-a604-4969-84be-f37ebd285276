package com.jkr.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Map;
import java.util.Random;
import java.util.Set;

public class SXSignUtil {
    /**
     * 常量
     */
    public static class SXConstants {

        public static enum SignType {
            MD5, HMACSHA256
        }

        public static final String DOMAIN_API = "qianhe.vipluo.com";

        public static final String FAIL     = "FAIL";
        public static final String SUCCESS  = "SUCCESS";
        public static final String HMACSHA256 = "HMAC-SHA256";
        public static final String MD5 = "MD5";

        public static final String FIELD_SIGN = "sign";
        public static final String FIELD_SIGN_TYPE = "sign_type";

        public static final String FIELD_APPID = "appid";
        public static final String FIELD_APP_SECRET = "key";
        public static final String FIELD_TIME_STAMP = "time_stamp";
        public static final String FIELD_NONCE_STR = "nonce_str";
        public static final String FIELD_NOTIFY_URL = "notify_url";

        public static final String PARAM_SEPARATOR = "?";
        public static final String FIELD_SEPARATOR = "&";
        public static final String VALUE_SEPARATOR = "=";

        public static final String CHARSET_NAME = "UTF-8";

    }
    private static final String SYMBOLS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final Random RANDOM = new SecureRandom();

    /**
     * 验签 判断签名是否正确，必须包含sign字段，否则返回false。使用MD5签名。
     *
     * @param data Map类型数据
     * @param key API密钥
     * @return 签名是否正确
     * @throws Exception
     */
    public static boolean verifySign(Map<String, String> data, String key) throws Exception {
        return verifySign(data, key, SXConstants.SignType.MD5);
    }

    /**
     * 验签 判断签名是否正确，必须包含sign字段，否则返回false。
     *
     * @param data Map类型数据
     * @param key API密钥
     * @param signType 签名方式
     * @return 签名是否正确
     * @throws Exception
     */
    public static boolean verifySign(Map<String, String> data, String key, SXConstants.SignType signType) throws Exception {
        if (!data.containsKey(SXConstants.FIELD_SIGN) ) {
            return false;
        }
        String sign = data.get(SXConstants.FIELD_SIGN);
        return sign(data, key, signType).equals(sign);
    }

    /**
     * 生成签名
     *
     * @param data 待签名数据
     * @param key API密钥
     * @return 签名
     */
    public static String sign(final Map<String, String> data, String key) throws Exception {
        return sign(data, key, SXConstants.SignType.MD5);
    }

    /**
     * 生成签名. 注意，若含有sign_type字段，必须和signType参数保持一致。
     *
     * @param data 待签名数据
     * @param key API密钥
     * @param signType 签名方式
     * @return 签名
     */
    public static String sign(Map<String, String> data, String key, SXConstants.SignType signType) throws Exception {
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (k.equals(SXConstants.FIELD_SIGN)) {
                continue;
            }
            String value = data.get(k);
            // utf8 编码
            try {
                if(null==value) {
                    value = "";
                }
                value = URLEncoder.encode(value, SXConstants.CHARSET_NAME);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            if (value.trim().length() > 0) {
                // 参数值为空，则不参与签名
                sb.append(k).append(SXConstants.VALUE_SEPARATOR).append(data.get(k).trim()).append(SXConstants.FIELD_SEPARATOR);
            }
        }
        sb.append(SXConstants.FIELD_APP_SECRET).append(SXConstants.VALUE_SEPARATOR).append(key);
        if (SXConstants.SignType.MD5.equals(signType)) {
            return MD5(sb.toString()).toUpperCase();
        }
        else if (SXConstants.SignType.HMACSHA256.equals(signType)) {
            return HMACSHA256(sb.toString(), key);
        }
        else {
            throw new Exception(String.format("Invalid sign_type: %s", signType));
        }
    }

    /**
     * 获取随机字符串 Nonce Str
     *
     * @return String 随机字符串
     */
    public static String generateNonceStr() {
        char[] nonceChars = new char[32];
        for (int index = 0; index < nonceChars.length; ++index) {
            nonceChars[index] = SYMBOLS.charAt(RANDOM.nextInt(SYMBOLS.length()));
        }
        return new String(nonceChars);
    }

    /**
     * 生成 MD5
     *
     * @param data 待处理数据
     * @return MD5结果
     */
    public static String MD5(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance(SXConstants.MD5);
        byte[] array = md.digest(data.getBytes(SXConstants.CHARSET_NAME));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toUpperCase();
    }

    /**
     * 生成 HMACSHA256
     * @param data 待处理数据
     * @param key 密钥
     * @return 加密结果
     * @throws Exception
     */
    public static String HMACSHA256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toUpperCase();
    }

    /**
     * 日志
     * @return
     */
    public static Logger getLogger() {
        return LoggerFactory.getLogger("QH java sdk");
    }

    /**
     * 获取当前时间戳，单位秒
     * @return
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis()/1000;
    }

    /**
     * 获取当前时间戳，单位秒
     * @return
     */
    public static String getCurrentTimestampStr() {
        return String.valueOf(getCurrentTimestamp());
    }

    /**
     * 获取当前时间戳，单位毫秒
     * @return
     */
    public static long getCurrentTimestampMs() {
        return System.currentTimeMillis();
    }

    /**
     * 判断字符数组是否为空
     */
    public static boolean isNotEmpty(String... values) {
        boolean result = true;
        if (values == null || values.length == 0) {
            result = false;
        } else {
            for (String value : values) {
                result &= !isEmpty(value);
            }
        }
        return result;
    }

    public static boolean isEmpty(final CharSequence cs) {
        return cs == null || cs.length() == 0;
    }

    public static boolean isNotEmpty(final CharSequence cs) {
        return !isEmpty(cs);
    }

    public static boolean isBlank(CharSequence cs) {
        int strLen;
        if (cs != null && (strLen = cs.length()) != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(cs.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return true;
        }
    }

    public static boolean isNotBlank(CharSequence cs) {
        return !isBlank(cs);
    }

}
