package com.jkr.common.utils;

public class PropertyEnum {
	/**
	 *
	 * @ClassName: FlagEnum
	 * @Description: 是否
	 * @author: LJX
	 * @date: 2020年7月9日 下午2:34:00
	 *
	 */
	public enum FlagEnum {
		TRUE("1", "是"), FALSE("0", "否");

		private String key;
		private String value;

		FlagEnum(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
	}

	/**
	 *
	 * @ClassName: BusinessTypeEnum
	 * @Description:主体类型
	 * @author: LJX
	 * @date: 2020年7月18日 下午3:29:30
	 *
	 */
	public enum BusinessTypeEnum {
		BT_1("0", "种植"), BT_2("1", "养殖");

		private String key;
		private String value;

		BusinessTypeEnum(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
		public static BusinessTypeEnum getValueByKey(String key) {
			for (BusinessTypeEnum statusEnum : BusinessTypeEnum.values()) {
				if (statusEnum.getKey().equals(key)) {
					return statusEnum;
				}
			}
			return null;
		}
	}

	/**
	 *
	 * @ClassName: EntTypeEnum
	 * @Description:主体性质
	 * @author: LJX
	 * @date: 2020年7月18日 下午3:29:30
	 *
	 */
	public enum EntTypeEnum {
		ET_0("0", "企业"), ET_1("1", "个人"), ET_2("2", "机构");

		private String key;
		private String value;

		EntTypeEnum(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
	}

	/**
	 *
	 * @ClassName: RoleEnum
	 * @Description: 角色
	 * @author: LJX
	 * @date: 2020年7月18日 下午4:26:37
	 *
	 */
	public enum RoleEnum {
		personal("722775900357132288", "1", "个人"), ent("722775778382577664", "0", "企业");
		private String key;
		private String value;
		private String label;

		private RoleEnum(String key, String value, String label) {
			this.key = key;
			this.value = value;
			this.label = label;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}

		public String getLabel() {
			return this.label;
		}

		public static RoleEnum getRoleEnum(String key) {
			for (RoleEnum item : RoleEnum.values()) {
				if (item.getKey().equals(key)) {
					return item;
				}
			}
			return null;
		}
	}

	/**
	 *
	 * @ClassName: FileTypeEnum
	 * @Description: 附件类型
	 * @author: LJX
	 * @date: 2020年7月21日 下午3:17:19
	 *
	 */
	public enum FileTypeEnum {
		FT_1("licensePic", "营业执照", "bas_ent"),
		FT_2("sealPic", "电子公章", "bas_ent"),
		FT_3("entPic", "企业照片","bas_ent"),
		FT_4("idCardFront", "身份证正面", "bas_ent"),
		FT_5("idCardBack", "身份证反面", "bas_ent"),
		FT_6("productPicture", "产品","bas_product"),
		FT_7("entHonorPic", "企业荣誉照片", "bas_ent"),
		FT_8("productCertPic", "产品认证","bas_product"),
		FT_9("video", "视频培训", "bas_video_training"),
		FT_10("guidePicture","技术指导", "bas_guide"),
		FT_11("videoCover", "视频封面","bas_video_training"),
		FT_12("photo", "监督检查照片", "bas_check"),
		FT_13("photo", "检测报告","bas_product_inspection");

		private String key;
		private String value;
		private String label;

		FileTypeEnum(String key, String value, String label) {
			this.key = key;
			this.value = value;
			this.label = label;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}

		public String getLabel() {
			return this.label;
		}

	}

	/**
	 *
	 * @ClassName: InspectionSituationEnum
	 * @Description:检测情况
	 * @author: LJX
	 * @date: 2020年7月18日 下午3:29:30
	 *
	 */
	public enum InspectionSituationEnum {
		IS_01("is01", "自我承诺"), IS_02("is02", "快检合格"),IS_03("is03", "委托检测合格"),IS_04("is04", "自行检测合格"),IS_05("is05", "质量安全控制符合要求");

		private String key;
		private String value;

		InspectionSituationEnum(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
	}

	/**
	 *
	 * @ClassName: TestResultEnum
	 * @Description: 检测结果
	 * @author: ljx
	 * @date: 2020年9月10日 下午6:12:37
	 *
	 */
	public enum TestResultEnum {
		TR0("1", "合格"), TR1("0", "不合格");

		private String key;
		private String value;

		TestResultEnum(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
	}

	/**
	 *
	 * @ClassName: MonthEnum
	 * @Description: 月份枚举
	 * @author: 胡志国
	 * @date: 2021年4月15日 下午6:12:37
	 *
	 */
	public enum MonthEnum {
		JANUARY("01", "1月"),
		FEBRUARY("02", "2月"),
		MARCH("03", "3月"),
		APRIL("04", "4月"),
		MAY("05", "5月"),
		JUNE("06", "6月"),
		JULY("07", "7月"),
		AUGUST("08", "8月"),
		SEPTEMBER("09", "9月"),
		OCTOBER("10", "10月"),
		NOVEMBER("11", "11月"),
		DECEMBER("12", "12月");

		private String key;
		private String value;

		MonthEnum(String key, String value) {
			this.key = key;
			this.value = value;
		}

		public String getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
	}

	/**
	 * @description 检查结果
	 * <AUTHOR>
	 * @date 2021/4/17
	 */
	public enum CheckResultFlag {
		YES(1, "合格"),
		NO(0, "不合格");

		private Integer key;
		private String value;

		CheckResultFlag(Integer key, String value) {
			this.key = key;
			this.value = value;
		}

		public Integer getKey() {
			return this.key;
		}

		public String getValue() {
			return this.value;
		}
	}
}
