/**
 * Copyright &copy; 2012-2016 <a href="https://github.com/thinkgem/jeesite">JeeSite</a> All rights reserved.
 */
package com.jkr.common.utils;

import com.google.common.collect.Maps;
import com.jkr.common.utils.PropertiesLoader;
import org.springframework.core.io.DefaultResourceLoader;

import java.io.File;
import java.io.IOException;
import java.util.Map;

/**
 * 全局配置类 懒汉式单例类.在第一次调用的时候实例化自己
 * <AUTHOR>
 * @version 2018年1月5日
 */
public class Global {

	private Global() {}

	/**
	 * 当前对象实例
	 */
	private static Global global = null;

	/**
	 * 静态工厂方法 获取当前对象实例 多线程安全单例模式(使用双重同步锁)
	 */

	public static synchronized Global getInstance() {

		if (global == null) {
			synchronized (Global.class) {
				if (global == null)
					global = new Global();
			}
		}
		return global;
	}

	/**
	 * 保存全局属性值
	 */
	private static Map<String, String> map = Maps.newHashMap();

	/**
	 * 属性文件加载对象
	 */
	private static PropertiesLoader loader = new PropertiesLoader("jeesite.properties");

	/**
	 * 显示/隐藏
	 */
	public static final String SHOW = "1";
	public static final String HIDE = "0";

	/**
	 * 是/否
	 */
	public static final String YES = "1";
	public static final String NO = "0";

	/**
	 * 合格/不合格
	 */
	public static final String QUALIFIED = "0";//合格
	public static final String UNQUALIFIED = "1";//不合格


	/**
	 * 对/错
	 */
	public static final String TRUE = "true";
	public static final String FALSE = "false";

	/**
	 * 上传文件基础虚拟路径
	 */
	public static final String USERFILES_BASE_URL = "/userfiles/";

	/**
	 * 获取配置
	 *
	 * @see {fns:getConfig('adminPath')}
	 */
	public static String getConfig(String key) {
		String value = map.get(key);
		if (value == null) {
			value = loader.getProperty(key);
			map.put(key, value != null ? value : StringUtils.EMPTY);
		}
		return value;
	}

	/**
	 * 获取管理端根路径
	 */
	public static String getAdminPath() {
		return getConfig("adminPath");
	}

	/**
	 * 获取前端根路径
	 */
	public static String getFrontPath() {
		return getConfig("frontPath");
	}

	/**
	 * 获取URL后缀
	 */
	public static String getUrlSuffix() {
		return getConfig("urlSuffix");
	}

	/**
	 * 是否是演示模式，演示模式下不能修改用户、角色、密码、菜单、授权
	 */
	public static Boolean isDemoMode() {
		String dm = getConfig("demoMode");
		return "true".equals(dm) || "1".equals(dm);
	}

	/**
	 * 页面获取常量
	 *
	 * @see {fns:getConst('YES')}
	 */
	public static Object getConst(String field) {
		try {
			return Global.class.getField(field).get(null);
		} catch (Exception e) {
			// 异常代表无配置，这里什么也不做
		}
		return null;
	}

	/**
	 * 获取工程路径
	 *
	 * @return
	 */
	public static String getProjectPath() {
		// 如果配置了工程路径，则直接返回，否则自动获取。
		String projectPath = Global.getConfig("projectPath");
		if (StringUtils.isNotBlank(projectPath)) {
			return projectPath;
		}
		try {
			File file = new DefaultResourceLoader().getResource("").getFile();
			if (file != null) {
				while (true) {
					File f = new File(file.getPath() + File.separator + "src" + File.separator + "main");
					if (f == null || f.exists()) {
						break;
					}
					if (file.getParentFile() != null) {
						file = file.getParentFile();
					} else {
						break;
					}
				}
				projectPath = file.toString();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return projectPath;
	}

	/**
	 * 是否是调试模式
	 * @return
	 */
	public static Boolean isDebugMode() {
		String dm = getConfig("debugMode");
		return "true".equals(dm) || "1".equals(dm);
	}

	/**
	 * 是否是微信模式
	 * @return
	 */
	public static Boolean isWechatMode() {
		String dm = getConfig("wechatMode");
		return "true".equals(dm) || "1".equals(dm);
	}

	/**
	 * 获取下载图片的路径
	 */
	public static String getDownImgUrl(){
		return getConfig("downloadUrl");
	}

	/**
	 * 行政区划第一级parent_id
	 */
	public static String getRootAreaId(){
		return getConfig("rootAreaId");
	}
	/**
	 * 校验token
	 * <AUTHOR>
	 * @date 2020年09月17日 17:20
	 * @param token
	 * @since 1.0.0
	 * @return String
	 */
	public static String tokenValidate(String token){
		if(StringUtils.isBlank(token)){
			return null;
		}
		String tokenType = token.split("_")[0];
		String config = getConfig(tokenType+".token");

		return config.equals(token)?tokenType:null;
	}
}
