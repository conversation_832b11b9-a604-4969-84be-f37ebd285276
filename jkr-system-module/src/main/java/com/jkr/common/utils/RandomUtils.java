package com.jkr.common.utils;/**
 * @ClassName RandomUtil
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/3/22
 * @Version V1.0
 **/

/**
 * @className RandomUtil
 * @description: TODO
 * <AUTHOR>
 * @date 2021/3/22 
 * @version V1.0
 **/
public class RandomUtils {
    public static final String NUMBER = "1234567890";
    private static final String LETTER = "1234567890abcdefghijkmnpqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public static String createRandom(boolean numberFlag, int length) {
        String retStr = "";
        String strTable = numberFlag ? NUMBER : LETTER;
        int len = strTable.length();
        boolean bDone = true;

        do {
            retStr = "";
            int count = 0;

            for(int i = 0; i < length; ++i) {
                double dblR = Math.random() * (double)len;
                int intR = (int)Math.floor(dblR);
                char c = strTable.charAt(intR);
                if ('0' <= c && c <= '9') {
                    ++count;
                }

                retStr = retStr + strTable.charAt(intR);
            }

            if (count >= 2) {
                bDone = false;
            }
        } while(bDone);

        return retStr;
    }
}

