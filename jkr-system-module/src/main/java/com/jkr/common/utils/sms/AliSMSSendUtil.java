package com.jkr.common.utils.sms;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20180501.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20180501.models.SendMessageWithTemplateRequest;
import com.aliyun.sdk.service.dysmsapi20180501.models.SendMessageWithTemplateResponse;
import com.jkr.common.enums.ConfigKeyEnum;
import com.jkr.project.system.service.ISysConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;


/**
 * 验证码短信发送<br/>
 * <i>注意:所有短信发送都是异步的</i>
 *
 * @Filename: SMSVerifyCodeModel.java
 * @Version: 1.0
 * @Author: zhaihl
 * @Email: <EMAIL>
 */
@Slf4j
@Component
public class AliSMSSendUtil {

    @Resource
    private ISysConfigService configService;

    /*
    发送特定验证码
     */
    public JSONObject sendSms(String telephone, String templateId, String verifyCode) {
        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(configService.selectConfigByKey(ConfigKeyEnum.ALI_SMS_ACCESS_KEY_ID.getKey()))
                .accessKeySecret(configService.selectConfigByKey(ConfigKeyEnum.ALI_SMS_ACCESS_KEY_SECRET.getKey()))
                .build());

        AsyncClient client = AsyncClient.builder()
                .credentialsProvider(provider)
                .build();

        SendMessageWithTemplateRequest req = SendMessageWithTemplateRequest.builder()
                .to(telephone)
                .from(configService.selectConfigByKey(ConfigKeyEnum.ALI_SMS_SIGN.getKey()))
                .templateCode(templateId)
                .templateParam(verifyCode).build();

        CompletableFuture<SendMessageWithTemplateResponse> response = client.sendMessageWithTemplate(req);
        SendMessageWithTemplateResponse resp = null;
        try {
            resp = response.get();
        } catch (Exception e) {
            e.printStackTrace();
            return JSONUtil.parseObj(resp);
        }
        client.close();
        return JSONUtil.parseObj(resp);

    }

    public JSONObject sendVerifySms(String telephone, String verifyCode) {
        return sendSms(telephone, configService.selectConfigByKey(ConfigKeyEnum.ALI_SMS_VERIFY_TEMPLATE_ID.getKey()), verifyCode);
    }
}
