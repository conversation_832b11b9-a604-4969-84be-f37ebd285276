package com.jkr.common.utils.ip;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jkr.common.constant.Constants;
import com.jkr.common.utils.StringUtils;
import com.jkr.common.utils.http.HttpUtils;
import com.jkr.framework.config.RuoYiConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 获取地址类
 *
 * <AUTHOR>
 */
public class AddressUtils {
	// IP地址查询
	public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";
	// 未知地址
	public static final String UNKNOWN = "XX XX";
	private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);

	public static String getRealAddressByIP(String ip) {
		// 内网不查询
		if (IpUtils.internalIp(ip)) {
			return "内网IP";
		}
		if (RuoYiConfig.isAddressEnabled()) {
			try {
				String rspStr = HttpUtils.sendGet(IP_URL, "ip=" + ip + "&json=true", Constants.GBK);
				if (StringUtils.isEmpty(rspStr)) {
					log.error("获取地理位置异常 {}", ip);
					return UNKNOWN;
				}
				Map<String, String> map = new ObjectMapper().readValue(rspStr, new TypeReference<>() {
				});
				String region = map.get("pro");
				String city = map.get("city");
//				JSONObject obj = JSON.parseObject(rspStr);
//				String region = obj.getString("pro");
//				String city = obj.getString("city");
				return String.format("%s %s", region, city);
			} catch (Exception e) {
				log.error("获取地理位置异常 {}", ip);
			}
		}
		return UNKNOWN;
	}
}
