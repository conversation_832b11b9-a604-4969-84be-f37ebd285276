package com.jkr.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jkr.common.utils.Global;

import java.util.HashMap;
import java.util.Map;

/**
 * 高德工具类
 * 2022年1月11日11:31:56
 * lxy
 */
public class GaoDeUtils {

    //设置key
    public static final String KEY = Global.getConfig("gaode.key");
    public static final String URL = "https://restapi.amap.com";
    public static final String IP_URL_V5 = "/v5/ip";
    public static final String IP_URL_V3 = "/v3/ip";
    public static final String IP_TYPE_4 = "4";

    /**
     *
    * @title: getIpV5
    * @author: lxy
    * @date: 2022年1月11日 上午11:52:08
    * @description: 获取ip v5版
    * @param:  ip
    * @return: JSONObject
     */
    public static JSONObject getIpV5(String ip){
    	if(StringUtils.isBlank(ip)) {
    		return null;
    	}
    	Map<String,String> param=new HashMap<String,String>();
    	param.put("key", KEY);
    	param.put("type", IP_TYPE_4);
    	param.put("ip", ip);
    	JSONObject jsonObject = JSONUtil.parseObj(HttpClientUtil.doGet(URL+IP_URL_V5, param));
    	return jsonObject;
    }
    /**
     *
    * @title: getIpV3
    * @author: lxy
    * @date: 2022年1月11日 上午11:52:08
    * @description: 获取ip v3版
    * @param:  ip
    * @return: JSONObject
     */
    public static JSONObject getIpV3(){
    	Map<String,String> param=new HashMap<String,String>();
    	param.put("key", KEY);
    	JSONObject jsonObject = JSONUtil.parseObj(HttpClientUtil.doGet(URL+IP_URL_V3, param));
    	return jsonObject;
    }
    public static void main(String[] args) {
    	/*String ip="************";
    	JSONObject jsonObject =getIpV5(ip);
    	System.out.println(jsonObject.toString());*/
    	JSONObject jsonObject =getIpV3();
    	System.out.println(jsonObject.toString());
	}
}
