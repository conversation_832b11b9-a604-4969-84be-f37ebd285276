package com.jkr.common.utils;


import org.apache.commons.codec.binary.Hex;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Arrays;

/**
 * 国密sm4算法
 * 提供了使用固定秘钥进行加密和解密的方法
 *
 * @ClassName Sm4Util
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/6/30 0030
 **/
public class Sm4Util {
    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String ENCODING = "UTF-8";
    public static final String ALGORITHM_NAME = "SM4";
    /**
     * 加密算法/分组加密模式/分组填充模式
     * PKCS5Padding 以8个字节为一组进行分组加密
     * 定义分组加密模式使用 PKCS5Padding
     */
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";
    /**
     * 128-32位16进制，256-64位16进制
     */
    public static final int DEFAULT_KEY_SIZE = 128;

    private static final String KEY = "6E10C4D38CDBBF5C9A76477311E4F240";

    /**
     * 生成秘钥
     *
     * @param keySize
     * @return
     * @throws Exception
     */
    private static byte[] generateKey(int keySize) throws Exception {
        KeyGenerator kg = KeyGenerator.getInstance(ALGORITHM_NAME, BouncyCastleProvider.PROVIDER_NAME);
        kg.init(keySize, new SecureRandom());
        return kg.generateKey().getEncoded();
    }

    private static String generateKey() throws Exception {
        return new String(Hex.encodeHex(generateKey(DEFAULT_KEY_SIZE), false));
    }

    /**
     * 生成ECB暗号
     *
     * @param algorithmName 算法名称
     * @param mode          模式
     * @param key
     * @return
     * @throws Exception
     */
    private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithmName, BouncyCastleProvider.PROVIDER_NAME);
        Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
        cipher.init(mode, sm4Key);
        return cipher;
    }

    /**
     * Ecb 加密模式
     *
     * @param key
     * @param data
     * @return
     * @throws Exception
     */
    private static byte[] encryptEcbPadding(byte[] key, byte[] data) throws Exception {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, key);
        return cipher.doFinal(data);
    }

    /**
     * sm4加密
     *
     * @param hexKey   秘钥
     * @param paramStr 待加密字符串
     * @return 16进制加密字符串
     */
    private static String encryptEcb(String hexKey, String paramStr) {
        try {
            String cipherText = "";
            byte[] keyData = ByteUtils.fromHexString(hexKey);
            byte[] srcData = paramStr.getBytes(ENCODING);
            byte[] cipherArray = encryptEcbPadding(keyData, srcData);
            cipherText = ByteUtils.toHexString(cipherArray);
            return cipherText;
        } catch (Exception e) {
            return paramStr;
        }
    }

    /**
     * 使用固定秘钥进行加密
     *
     * @param paramStr
     * @return
     */
    public static String encryptEcb(String paramStr) {
        return encryptEcb(KEY, paramStr);
    }

    /**
     * 解密
     *
     * @param key
     * @param cipherText
     * @return
     * @throws Exception
     */
    private static byte[] decryptEcbPadding(byte[] key, byte[] cipherText) throws Exception {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, key);
        return cipher.doFinal(cipherText);
    }

    /**
     * sm4解密
     *
     * @param hexKey     16进制秘钥
     * @param cipherText 16进制加密的字符串
     * @return 解密后的字符串
     */
    private static String decryptEcb(String hexKey, String cipherText) {
        String decryptStr = "";
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        byte[] cipherData = ByteUtils.fromHexString(cipherText);
        byte[] srcData = new byte[0];
        try {
            srcData = decryptEcbPadding(keyData, cipherData);
            decryptStr = new String(srcData, ENCODING);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decryptStr;
    }

    /**
     * 使用固定秘钥进行解密
     *
     * @param cipherText
     * @return
     */
    public static String decryptEcb(String cipherText) {
        return decryptEcb(KEY, cipherText);
    }

    /**
     * 字符串加密前后校验
     *
     * @param hexKey     秘钥
     * @param cipherText 加密后的字符串
     * @param paramStr   加密前的字符串
     * @return true:是同一数据,false:数据不一致
     * @throws Exception
     */
    private static boolean verifyEcb(String hexKey, String cipherText, String paramStr) throws Exception {
        boolean flag = false;
        byte[] keyData = ByteUtils.fromHexString(hexKey);
        byte[] cipherData = ByteUtils.fromHexString(cipherText);
        byte[] decryptData = decryptEcbPadding(keyData, cipherData);

        byte[] srcData = paramStr.getBytes(ENCODING);
        flag = Arrays.equals(decryptData, srcData);
        return flag;
    }

    public static boolean verifyEcb(String cipherText, String paramStr) throws Exception {
        return verifyEcb(KEY, cipherText, paramStr);
    }

    public static void main(String[] args) throws Exception {
        String Plaintext = "http://sysy.zhuisuguanjia.com/trace/23232212223";
        System.out.println("加密前：" + Plaintext);
        String decrypt1 = encryptEcb(Plaintext);
        System.out.println("加密后：" + decrypt1);

        //解密，密文
        String ciphertext="f0e249dacb23aaed96a05f3897eec65732691e630ef9512bd3a20f47dd436a5a8031b18934828035b1a8028daf5206f5";
        System.out.println("解密前：" + ciphertext);
        String jsData = decryptEcb(ciphertext);
        System.out.println("解密后：" + jsData);
    }

}

