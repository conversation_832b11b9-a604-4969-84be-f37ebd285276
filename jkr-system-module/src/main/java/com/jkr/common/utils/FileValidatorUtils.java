package com.jkr.common.utils;

import com.alibaba.fastjson2.JSONArray;
import com.jkr.common.constant.CacheConstants;
import com.jkr.common.exception.ServiceException;
import com.jkr.framework.redis.RedisCache;
import com.jkr.project.system.domain.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.filesystem.DirectoryEntry;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Slf4j
@Component
public class FileValidatorUtils {
    @Autowired
    private RedisCache redisCache;
    // 文件类型与对应的魔数（Magic Number）
    //2003以前的文档
    private static final String FILE_TYPE_2003_BEFORE = "D0 CF 11 E0 A1 B1 1A E1";
    //2003以后的版本
    private static final String FILE_TYPE_2003_AFTER = "50 4B 03 04";


    /**
     * 验证文件类型
     *
     * @param file 上传的文件
     * @return 是否合法
     */
    public void validateFile(MultipartFile file) throws IOException {
        boolean validate = false;
        // 1. 检查后缀名合法性
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            throw new ServiceException("文件没有后缀或后缀不合法");
        }
        //获取文件后缀统一小写
        String extension = getFileExtension(originalFilename).toLowerCase();

        // 2. 检查文件头
        StringBuilder hexBuilder = new StringBuilder();
        try (InputStream is = file.getInputStream()) {
            byte[] fileHeader = new byte[8]; // 读取前8字节
            int read = is.read(fileHeader);
            if (read > 0) {
                for (int i = 0; i < read; i++) {
                    hexBuilder.append(String.format("%02X", fileHeader[i]));
                }
            }
            if (hexBuilder.toString().startsWith(FILE_TYPE_2003_BEFORE)) {
                validate = this.detectFileType(file, extension);
            } else if (hexBuilder.toString().startsWith(FILE_TYPE_2003_AFTER)) {
                validate = this.detectFileTypeX(file, extension);
            } else {
                //获取字典项
                List<SysDictData> sysDictDataList = new ArrayList<SysDictData>();
                JSONArray array = redisCache.getCacheObject(CacheConstants.SYS_DICT_KEY + "file_type_header");
                sysDictDataList = array.toJavaList(SysDictData.class);
                //遍历字典项的键,获取对应后缀的数组，可能会没有对应类型
                List<String> expectedMagicNumbers = sysDictDataList.stream()
                        .filter(item -> item.getDictLabel().toLowerCase().equals(extension))
                        .map(item -> item.getDictValue().replaceAll(" ", "").trim())
                        .collect(Collectors.toList());
                if (!expectedMagicNumbers.isEmpty()) {
                    validate = expectedMagicNumbers.stream()
                            .anyMatch(magic -> hexBuilder.toString().startsWith(magic));
                }
            }
            if(!validate){
                throw new ServiceException("文件类型与后缀不符");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取文件后缀
     */
    private static String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex == -1) return "";
        return filename.substring(dotIndex + 1);
    }

    /**
     * 检查字节数组是否以指定前缀开头
     */
    private static boolean startsWith(byte[] source, byte[] prefix) {
        if (prefix.length > source.length) return false;
        for (int i = 0; i < prefix.length; i++) {
            if (source[i] != prefix[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断97 - 2003 版本 文件类型
     *
     * @param file
     * @return
     */
    public static boolean detectFileType(MultipartFile file, String extension) throws IOException {
        try (InputStream fis = file.getInputStream()) {
            POIFSFileSystem fs = new POIFSFileSystem(fis);
            DirectoryEntry root = fs.getRoot();
            if (root.hasEntry("WordDocument")) {
                return "doc".equals(extension);
            } else if (root.hasEntry("Workbook")) {
                return "xls".equals(extension);
            } else if (root.hasEntry("PowerPoint Document")) {
                return "ppt".equals(extension);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 判断 2003以后 版本 文件类型
     *
     * @param file
     * @return
     */
    public static Boolean detectFileTypeX(MultipartFile file, String extension) {
        try (ZipFile zipFile = convertMultipartFileToZipFile(file)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                String name = entry.getName();
                if (name.startsWith("word/")) {
                    return "docx".equals(extension);
                } else if (name.startsWith("xl/")) {
                    return "xlsx".equals(extension);
                } else if (name.startsWith("ppt/")) {
                    return "pptx".equals(extension);
                }
            }
            return "zip".equals(extension);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * MultipartFile对象 转化成ZipFile对象
     *
     * @param multipartFile
     * @return
     * @throws IOException
     */
    public static ZipFile convertMultipartFileToZipFile(MultipartFile multipartFile) throws IOException {
        // 创建临时文件
        File tempFile = File.createTempFile("tempZip", ".zip");
        try (InputStream inputStream = multipartFile.getInputStream();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            // 将 MultipartFile 的内容写入临时文件
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        // 创建 ZipFile 对象
        ZipFile zipFile = new ZipFile(tempFile);
        // 注册临时文件的删除钩子，在 JVM 退出时删除临时文件
        tempFile.deleteOnExit();
        return zipFile;
    }


}
