package com.jkr.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.EnvironmentPBEConfig;
import org.jasypt.encryption.pbe.config.SimplePBEConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class JasyptUtil {
    @Value("${jasypt.encryptor.password}")
    private String secrect; //秘钥

    @Bean("jasyptStringEncryptor")
    public StringEncryptor stringEncryptor() {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimplePBEConfig config = new EnvironmentPBEConfig();
        config.setPassword(secrect);
        config.setPoolSize(4);
        encryptor.setConfig(config);
        return encryptor;
    }

    /**
     * 加密方法
     * @param secrect 密钥
     * @param text 原字符串
     * @return
     */
    private static String encrypt(String secrect,String text){
        StandardPBEStringEncryptor standardPBEStringEncryptor = new StandardPBEStringEncryptor();
        EnvironmentPBEConfig config = new EnvironmentPBEConfig();
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setPassword(secrect);
        standardPBEStringEncryptor.setConfig(config);
        return standardPBEStringEncryptor.encrypt(text);
    }
    /**
     * 解密方法
     * @param secrect 密钥
     * @param text 原字符串
     * @return
     */
    public static String decrypt(String secrect,String text) {
        StandardPBEStringEncryptor standardPBEStringEncryptor = new StandardPBEStringEncryptor();
        EnvironmentPBEConfig config = new EnvironmentPBEConfig();
        config.setAlgorithm("PBEWithMD5AndDES");
        config.setPassword(secrect);
        standardPBEStringEncryptor.setConfig(config);
        return standardPBEStringEncryptor.decrypt(text);
    }

    public static void main(String[] args) {
        String secrect="1234qwer";
        String str="research";
        String encryptStr=new JasyptUtil().encrypt(secrect,str);
        String decryptStr=new JasyptUtil().decrypt(secrect,encryptStr);
        log.info("原字符串：{}，加密后：{}",str,encryptStr);
        log.info("解密后：{}",decryptStr);
    }
}
