package com.jkr.common.enums;

import cn.hutool.core.map.MapUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @desciption 公用枚举类
 * @Created 2020年06月02日
 */
public class EnumProperty {
    /**
     *@Title: LoginTypeEnum
     *@Author: lxy
     *@date: 2020年7月16日8:59:09
     *@Description: 登录类型枚举
     */
    public enum LoginTypeEnum {
        PASSWORD("1", "用户名密码"),
        CAPTCHA("2", "验证码");

        private String key;
        private String value;

        LoginTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }


    /**
     *@Title: EntTypeEnum
     *@Author: lvpeng
     *@date: 2020-07-21
     *@Description: 主体类型枚举
     */
    public enum EntTypeEnum {
        ENTERPRISE("0", "企业"),
        PERSON("1", "个人");

        private String key;
        private String value;

        EntTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }


    public enum ExamineStatusEnum {
        TODO("0", "待审核"),
        REJECT("-1", "驳回"),
        PASS("1", "通过"),
        CHANGETEMP("90", "变更暂存"),
    	TEMP("99", "暂存");

        private String key;
        private String value;

        ExamineStatusEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    public enum SmsEnum {
    	REGISTER("1", "会员注册"),
    	PAY("2", "支付"),
    	NOTICE("3", "通知");

        private String key;
        private String value;

        SmsEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    public enum SmsTemplateEnum {
    	REGISTER("SMS_197616208", "会员注册"),
    	PAY("22222", "支付"),
    	NOTICE("33333", "通知");

        private String key;
        private String value;

        SmsTemplateEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    /**
     *@Title: BusinessTypeEnum
     *@Author: lxy
     *@date: 2020年7月16日8:59:09
     *@Description: 主体类型
     */
    public enum BusinessTypeEnum {
        TYPE_0("0", "种植"),
        TYPE_1("1", "养殖");

        private String key;
        private String value;

        BusinessTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    /**
     *@Title: FarmTypeEnum
     *@Author: lxy
     *@date: 2020年7月16日8:59:09
     *@Description: 养殖分类
     */
    public enum FarmTypeEnum {
        TYPE_0("0", "牧业"),
        TYPE_1("1", "渔业");

        private String key;
        private String value;

        FarmTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    /**
     *@Title: TableNameEnum
     *@Author: lxy
     *@date: 2021年2月25日13:40:00
     *@Description: 业务表名分类
     */
    public enum TableNameEnum {
        ENT("bas_ent", "主体"),
        ENT_CHANGE("bas_ent_change", "主体变更");

        private String key;
        private String value;

        TableNameEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     *@Title: ItemModelEnum
     *@Author: 胡志国
     *@date: 2021年4月28日9:40:00
     *@Description: 指标项类型：0：日常 1：专项
     */
    public enum ItemModelEnum {
        DAILY(0, "日常"),
        SPECIAL(1, "专项");

        private Integer key;
        private String value;

        ItemModelEnum(Integer key, String value) {
            this.key = key;
            this.value = value;
        }

        public Integer getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     *@Title: CheckResultFlagEnum
     *@Author: 胡志国
     *@date: 2021年4月28日9:40:00
     *@Description: 检查结果类型：0：否 1：是
     */
    public enum CheckResultFlagEnum {
        NO(0, "否"),
        YES(1, "是");

        private Integer key;
        private String value;

        CheckResultFlagEnum(Integer key, String value) {
            this.key = key;
            this.value = value;
        }

        public Integer getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     *@Title: NodeTypeEnum
     *@Author: lxy
     *@date: 2020年7月16日8:59:09
     *@Description: 节点类型
     */
    public enum NodeTypeEnum {
        TYPE_0("produce_plant", "种植"),
        TYPE_1("produce_breed", "养殖");

        private String key;
        private String value;

        NodeTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum RoleTypeEnum{
        ROLE_0("885543918320484352", "乡村管理员");

        private String key;
        private String value;

        RoleTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }
        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum AreaCodeLength{
        LENGTH_TWO(2, "长度二"),
        LENGTH_FOUR(4, "长度四"),
        LENGTH_SIX(6, "长度六"),
        LENGTH_NINE(9, "长度九"),
        LENGTH_TWELVE(12, "长度十二");

        private int key;
        private String value;

        AreaCodeLength(int key, String value) {
            this.key = key;
            this.value = value;
        }
        public int getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum JudgmentConditions{
        THREE_ZERO("000", "000");

        private String key;
        private String value;

        JudgmentConditions(String key, String value) {
            this.key = key;
            this.value = value;
        }
        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    //手机号验证码是否限制同一IP发短信次数
    public enum VerificationEnum{
        flag_0("1", "1");

        private String key;
        private String value;

        VerificationEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }
        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     *@Title: areaTypeEnum
     *@Author: lxy
     *@date: 2020年7月16日8:59:09
     *@Description: 行政区划类别
     */
    public enum areaTypeEnum {
        CODE_01("1", "国家级"),
        CODE_02("2", "省级"),
        CODE_03("3", "市级"),
        CODE_04("4", "县区级"),
        CODE_05("5", "镇级"),
    	CODE_06("6", "村级");

        private String key;
        private String value;

        areaTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     *@Title: InspectionSituationEnum
     *@Author: lxy
     *@date: 2020年7月16日8:59:09
     *@Description: 检测情况类别
     */
    public enum InspectionSituationEnum {
        CODE_01("is01", "自我承诺"),
        CODE_02("is02", "快检合格"),
        CODE_03("is03", "委托检测合格"),
        CODE_04("is04", "自检合格"),
        CODE_05("is05", "内部质量控制");

        private String key;
        private String value;

        InspectionSituationEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    /**
     * 组合品标识
     * <AUTHOR>
     * @date 2025/3/3 17:29
     * @since 1.0.0
     * @return
     */
    public enum MixFlagEnum {
        FLAG_0("0", "单品"),
        FLAG_1("1", "组合品");

        private String key;
        private String value;

        MixFlagEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    /**
     * 身份标识
     * <AUTHOR>
     * @date 2025/3/3 17:31
     * @since 1.0.0
     */
    public enum IdentityTypeEnum {
        TYPE_0("0", "生产"),
        TYPE_1("1", "收购"),
        TYPE_2("2", "生产收购");

        private String key;
        private String value;

        IdentityTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    /**
     * 收购标识
     * <AUTHOR>
     * @date 2025/3/3 17:31
     * @since 1.0.0
     */
    public enum AcqurieFlagEnum {
        TYPE_0("0", "手动录入"),
        TYPE_1("1", "查验录入");

        private String key;
        private String value;

        AcqurieFlagEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    /**
     * 合格证类型标识
     * <AUTHOR>
     * @date 2025/3/3 17:31
     * @since 1.0.0
     */
    public enum CertificateFlagEnum {
        TYPE_0("0", "生产证"),
        TYPE_1("1", "收购证");

        private String key;
        private String value;

        CertificateFlagEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum AreaLevelEnum {
        PROVINCE("1", "省"),
        CITY("2", "市(州)"),
        COUNTY("3", "县(区)"),
        TOWN("4", "镇"),
        VILLAGE("5", "村");

        private String key;
        private String value;

        AreaLevelEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
    }
    public enum AeptTypeEnum {
        FLAG_0("1", "农业农村部门"),
        FLAG_1("2", "市场监管部门");

        private String key;
        private String value;

        AeptTypeEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }
        public static String getValueByKey(String key) {
            for (AeptTypeEnum statusEnum : AeptTypeEnum.values()) {
                if (statusEnum.getKey().equals(key)) {
                    return statusEnum.getValue();
                }
            }
            return null;
        }
    }
}
