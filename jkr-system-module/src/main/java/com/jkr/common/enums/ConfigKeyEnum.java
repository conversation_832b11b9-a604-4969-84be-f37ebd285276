package com.jkr.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfigKeyEnum {

    SYS_USER_INIT_PASSWORD("sys.user.initPassword","主框架页-默认皮肤样式名称"),
    SYS_INDEX_SKIN_NAME("sys.index.skinName","主框架页-默认皮肤样式名称"),
    SYS_INDEX_SIDE_THEME("sys.index.sideTheme","主框架页-侧边栏主题"),
    SYS_ACCOUNT_SMS_ENABLED("sys.account.sms.enabled","账号自助-短信验证码开关"),
    SYS_ACCOUNT_SMS_SEND_ENABLED("sys.account.sms.enabled","账号自助-短信验证码开关"),
    SYS_ACCOUNT_SMS_LENGTH("sys.account.sms.length","账号自助-短信验证码开关"),
    SYS_ACCOUNT_SMS_VALIDITY_TIME("sys.account.sms.validity.time","账号自助-短信验证码有效时间"),
    SYS_ACCOUNT_CAPTCHA_ENABLED("sys.account.captchaEnabled","账号自助-图像验证码开关"),
    SYS_ACCOUNT_REGISTE_USER("sys.account.registerUser","账号自助-是否开启用户注册功能"),
    SYS_PC_WEBSITE_CONFIG("sys.pc.website.config","系统PC网站配置"),
    SYS_LOGIN_BLACK_IP_LIST("sys.login.blackIPList","用户登录-黑名单列表"),
    SYS_AREA_TREE_ROOT_CODE("sys.area.treeRootCode","区域管理-树形结构数据根节点编码"),
    AMAP_KEY("amap.key","高德地图-开发者key"),
    AMAP_VERSION("amap.version","高德地图-JSAPI 的版本"),
    ALI_SMS_ACCESS_KEY_ID("ali.sms.accessKeyId", "阿里云-短信-accessKeyId"),
    ALI_SMS_ACCESS_KEY_SECRET("ali.sms.accessKeySecret", "阿里云-短信-accessKeySecret"),
    ALI_SMS_VERIFY_TEMPLATE_ID("ali.sms.verify.templateId", "阿里云-短信-模板id"),
    ALI_SMS_SIGN("ali.sms.sign", "阿里云-短信-签名");

    /**
     * 参数键名
     */
    private final String key;
    /**
     * 参数名称
     */
    private final String name;

}
