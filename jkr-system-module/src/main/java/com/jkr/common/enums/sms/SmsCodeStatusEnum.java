package com.jkr.common.enums.sms;

public enum SmsCodeStatusEnum {
	/**
	 * 短信使用类型
	 */
	CODE_0("0", "未用"),
	CODE_1("1", "已用"),
	CODE_2("2", "失败");
	private String key;
	private String value;
	private SmsCodeStatusEnum(String key, String value){
		this.key = key;
		this.value = value;
	}
	
	public String getKey(){
		return this.key;
	}
	
	public String getValue(){
		return this.value;
	}
	
	public static SmsCodeStatusEnum getEntManageTypeEnum(String key) {
		for (SmsCodeStatusEnum item : SmsCodeStatusEnum.values()) {
			if (item.getKey().equals(key)) {
				return item;
			}
		}
		return null;
	}

}
