package com.jkr.common.enums;

/**
 * 合格证配置适用类型
 */
public enum CertificateConfigTypeEnum {
	/**
	 * 类型
	 */
	CODE_0("0","通用区域"),
	CODE_1("1","指定区域");

	private String key;
	private String value;
	private CertificateConfigTypeEnum(String key, String value){
		this.key = key;
		this.value = value;
	}

	public String getKey(){
		return this.key;
	}

	public String getValue(){
		return this.value;
	}

	public static CertificateConfigTypeEnum getCertificateConfigTypeEnum(String key) {
		for (CertificateConfigTypeEnum item : CertificateConfigTypeEnum.values()) {
			if (item.getKey() == key) {
				return item;
			}
		}
		return null;
	}
}
