package com.jkr.common.enums.sms;

public enum SmsSendEnum {
	SUCCESS("1", "成功"),
	FAILED("0", "出现错误");
	private String key;
	private String value;
	private SmsSendEnum(String key, String value){
		this.key = key;
		this.value = value;
	}
	
	public String getKey(){
		return this.key;
	}
	
	public String getValue(){
		return this.value;
	}
	
	public static SmsSendEnum getEntManageTypeEnum(String key) {
		for (SmsSendEnum item : SmsSendEnum.values()) {
			if (item.getKey().equals(key)) {
				return item;
			}
		}
		return null;
	}

}
