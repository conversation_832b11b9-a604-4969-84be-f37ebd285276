package com.jkr.common.enums;

/**
 * 主体类别
 * <AUTHOR>
 */
public class MainTypeEnum {
	/**
	 *
	* @className: PersonEnum
	* @description: 个人主体类别:小农户、种养大户、家庭农场
	* @author: lxy
	* @date: 2020年8月10日 下午6:31:24
	*
	 */
	public enum PersonEnum {
		SMALL_FARMER("1", "小农户"),
		BREED_LARGE("15", "种养大户"),
		FAMILY_FARM("20", "家庭农场");

        private String key;
        private String value;

        PersonEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }

        public static PersonEnum getPersonEnumByKey(String key) {
            for (PersonEnum personEnum : PersonEnum.values()) {
                if (personEnum.getKey().equals(key)) {
                    return personEnum;
                }
            }
            return null;
        }
    }
	/**
	 *
	* @className: PersonEnum
	* @description: 主体主体类别:农产品生产企业、农民专业合作社、种养大户、家庭农场
	* @author: lxy
	* @date: 2020年8月10日 下午6:31:24
	*
	 */
	public enum EntEnum {
        SMALL_FARMERS("1", "小农户"),
		AGRICULTURAL_PRODUCTION_ENTERPRISE("5", "农产品生产企业"),
		FARMER_COOPERATIVE("10", "农民专业合作社"),
		BREED_LARGE("15", "种养大户"),
		FAMILY_FARM("20", "家庭农场");

        private String key;
        private String value;

        EntEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }

        public static EntEnum getEntEnumByKey(String key) {
            for (EntEnum entEnum : EntEnum.values()) {
                if (entEnum.getKey().equals(key)) {
                    return entEnum;
                }
            }
            return null;
        }
    }

    /**
     *
     * @className: CompanyEnum
     * @description: 机构主体类别:机构
     * @author: lxy
     * @date: 2020年8月10日 下午6:31:24
     *
     */
    public enum CompanyEnum {
        COMPANY("30", "检测机构");

        private String key;
        private String value;

        CompanyEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }

        public static CompanyEnum getCompanyEnumByKey(String key) {
            for (CompanyEnum companyEnum : CompanyEnum.values()) {
                if (companyEnum.getKey().equals(key)) {
                    return companyEnum;
                }
            }
            return null;
        }
    }

    public enum EntAllEnum {
        SMALL_FARMER("1", "小农户"),
        AGRICULTURAL_PRODUCTION_ENTERPRISE("5", "生产企业"),
        FARMER_COOPERATIVE("10", "合作社"),
        BREED_LARGE("15", "种养大户"),
        FAMILY_FARM("20", "家庭农场");

        private String key;
        private String value;

        EntAllEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public String getKey() {
            return this.key;
        }

        public String getValue() {
            return this.value;
        }

        public static EntAllEnum getEntAllEnumByKey(String key) {
            for (EntAllEnum entAllEnum : EntAllEnum.values()) {
                if (entAllEnum.getKey().equals(key)) {
                    return entAllEnum;
                }
            }
            return null;
        }
    }
}
