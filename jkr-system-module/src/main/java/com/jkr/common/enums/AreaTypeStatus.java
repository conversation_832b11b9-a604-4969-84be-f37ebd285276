package com.jkr.common.enums;

/**
 * 区域类型
 *
 * <AUTHOR>
 */
public enum AreaTypeStatus {
	PROVINCE("2", "省"),
	CITY("3", "市"),
	COUNTY("4", "县"),
	STREET("5", "乡镇街道"),
	VILLAGE("6", "社区居委会");

	private final String code;
	private final String info;

	AreaTypeStatus(String code, String info) {
		this.code = code;
		this.info = info;
	}

	public String getCode() {
		return code;
	}

	public String getInfo() {
		return info;
	}
}
