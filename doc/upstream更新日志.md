## upstream更新日志

#### 2025-2-21  lty

新代码已经合并到upstream中，有更新脚本，注意执行
更新内容：
1、代码生成：mapper增加集成BaseMapper
2、代码生成：domain类中ids类型改List<long>
3、代码生成：批量删除方法调整
4、代码生成：serviceImpl增加事务注解
5、mysql版本改为8.1.0
6、接口返回值去掉通过msg承接数据的方法，msg只承接消息描述，data承接数据，各项目组根据情况自行修改，前端用msg接受数据的方法都会不好使  **
重点注意 **
7、增加markdown文档管理功能
8、解决部分下载上传路径错误问题
9、解决雪花ID重复问题
10、增加脚本1、参数配置表增加唯一索引 2、增加文档管理相关数据表
11、通过添加@Anonymous实现接口不鉴权
12、接口传输国密加解密暂时关掉

#### 2025-2-24 lty

新代码已经合并到upstream中，无更新脚本
1、解决多角色导致用户列表数据条数错误问题
2、处理后端接口返回值data还有描述文字的问题（包括首次登录和密码修改）

#### 2025-2-26 lty

新代码已经合并到upstream中，无更新脚本
1、解决应用授权弹窗bug
2、解决机构隐藏“停用”属性
3、修复系统管理查询条件水印bug
4、去掉定时任务、职务、代码生成的批量“修改”按钮
5、增加修改密码强制退出功能
6、代码生成：修复导入弹窗检索不清空的问题
7、文件上传、个人信息头像：大小限制10M、文件名称增加校验
8、修复首次登录强制修改密码弹窗缓存输入密码的问题
9、用户导入导出去除“是否默认密码”

#### 2025-2-27 9:28 lty

新代码已经合并到upstream中，无更新脚本
1、增加首页是否显示可配置功能

#### 2025-2-27 16:35 lty

只有脚本更新
1、创建框架数据库版本表（fw_db_version）,便于核对基础框架更新记录

#### 2025-3-3 17:26 lty

1、修复部门mapper缺少@Mapper的注解问题
2、修改代码生成模版缺少@Mapper的问题
3、修复jkr—server下业务代码启动缺少get，set方法的问题，解决方法：pom中引入lombok补充版本
4、左侧菜单和顶部单独设置背景颜色

#### 2025-5-9 17:26 lty

1、增加源文件后缀篡改验证功能
2、调整雪花算法
3、修复jkr-server打包异常（pom文件错误）
4、集成jasypt
5、修复唯一验证可能的造成的漏洞

#### 2025-5-9 zwd

1、调整pom中部分依赖版本
2、修复代码问题
3、增加Redisson,通过分布式锁自动获取/分配yitter的worker id并保存到Redis中

#### 2025-5-12 zwd

1、修复自动分配雪花算法worker ID的代码错误
