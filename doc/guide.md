# 系统使用说明

## 接口文档工具

```text
[Smart-Doc](https://smart-doc-group.github.io/zh/)是一款强大的基于Java的API文档生成工具。
它通过对接口源代码进行分析来生成全面而准确的文档，完全不需要对代码进行任何注入。
这种非侵入式的方法确保了无需添加特殊注解或修改代码即可生成文档，使得集成变得无缝且简单。
```

**为保证文档的完整性，请务必在Class和Method使用JavaDoc注释，且保证注释规范、完整**

## 默认密码

* 系统默认密码在系统管理-参数设置-用户管理-账号初始化密码中设置修改，参数保存在sys_config表中
* 使用configService.selectConfigByKey("sys.user.initPassword")获取设置的默认密码
* 用户使用默认密码首次登录时，系统强制用户修改密码

## 接口请求方法

* 原则上只允许使用POST请求

## 集成测试

* TestBeanUtil,简单的Bean赋值工具类
* BaseControllerTest,简单的测试工具类，通过BeforeAll模拟每次测试前的登录过程；封装了postRequest和getRequest方法，用于post和get请求
* 具体使用可参考SysLoginControllerTest和SysDeptControllerTest

明确代码库的结构: doing
理解你的问题: doing
决定并获取代码库的信息: doing

## 功能模块

1. **系统管理模块**：
    - 用户管理、角色管理、权限管理等基础功能，包含列表展示、增删改查等。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/project/system/domain/SysUser.java
        - jkr-system-module/src/main/java/com/jkr/project/system/domain/SysRole.java
        - jkr-system-module/src/main/java/com/jkr/project/system/domain/SysMenu.java
        - jkr-system-module/src/main/java/com/jkr/project/system/service/impl/SysUserServiceImpl.java
        - jkr-system-module/src/main/java/com/jkr/project/system/service/impl/SysRoleServiceImpl.java

2. **代码生成模块**：
    - 代码自动生成功能，根据数据库表结构生成相应的实体类、Mapper、Service等。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/project/tool/gen/domain/GenTable.java)
        - jkr-system-module/src/main/java/com/jkr/project/tool/gen/controller/GenController.java
        - jkr-system-module/src/main/java/com/jkr/project/tool/gen/service/GenTableServiceImpl.java
        - jkr-system-module/src/main/java/com/jkr/project/tool/gen/service/IGenTableService.java

3. **国际化支持**：
    - 项目支持国际化，有多语言切换功能。
    - 相关配置文件：
        - jkr-server/src/main/resources/i18n/messages.properties
        - jkr-system-module/src/main/java/com/jkr/framework/config/I18nConfig.java

4. **安全认证模块**：
    - 用户登录、退出、注册、密码重置等功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/framework/security/service/SysLoginService.java
        - jkr-system-module/src/main/java/com/jkr/framework/security/service/SysRegisterService.java

5. **日志管理模块**：
    - 系统日志、操作日志、登录日志等管理功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/project/monitor/domain/SysJobLog.java
        - jkr-system-module/src/main/java/com/jkr/project/monitor/domain/SysOperLog.java
        - jkr-system-module/src/main/java/com/jkr/project/monitor/service/impl/SysJobLogServiceImpl.java

6. **任务调度模块**：
    - 定时任务、任务调度管理功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/project/monitor/domain/SysJob.java
        - jkr-system-module/src/main/java/com/jkr/project/monitor/mapper/SysJobMapper.java

7. **缓存管理模块**：
    - Redis缓存管理功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/framework/redis/RedisCache.java
        - jkr-system-module/src/main/java/com/jkr/framework/config/RedisConfig.java

8. **文件上传下载模块**：
    - 文件上传、下载、导入导出功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/common/utils/file/FileUploadUtils.java
        - jkr-system-module/src/main/java/com/jkr/common/utils/file/FileUtils.java

9. **监控管理模块**：
    - 系统监控、服务器监控、在线用户管理等功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/project/monitor/domain/SysUserOnline.java
        - jkr-system-module/src/main/java/com/jkr/project/system/service/ISysUserOnlineService.java

10. **通知公告模块**：
    - 系统通知、公告发布和管理功能。
    - 相关代码文件：
        - jkr-system-module/src/main/java/com/jkr/project/system/domain/SysNotice.java
        - jkr-system-module/src/main/java/com/jkr/project/system/mapper/SysNoticeMapper.java

## 日志

* 在Controller的接口方法上使用@Log注解，系统会根据注解配置记录日志
* LogAspect添加敏感字段到EXCLUDE_PROPERTIES中，避免日志泄露敏感信息

## 脱敏

* @Sensitive注解标记为过时
* 在Java Bean 敏感属性上使用@Desensitize注解，系统会根据注解配置脱敏处理
* 接口结果集脱敏
    * 在Controller需要脱敏的方法上使用@ApiDesensitize注解
    * 在Java Bean需要脱敏的属性上使用@BeanDesensitize注解
    * @BeanDesensitize使用disable配置不需要脱敏的角色

## Server-Sent Event

