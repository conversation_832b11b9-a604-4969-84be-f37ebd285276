# 如何将[工程B](远程基础仓库) 的修改合并到[工程A](本地仓库)中 （以拉取为主，将其他远程仓库代码合并到当前项目）

## 概述

本文档描述了如何将[工程B]中的修改合并[工程A]。通过 Git 操作，我们可以实现代码从[工程B] 到[工程A] 的同步和合并。
在私有化部署的 git 环境下，根据[工程B]复制创建[工程A]，进行开发，[工程A]开发过程中，需要将[工程B] 升级完善的功能合并到[工程A]中。

## 步骤说明

### 1. 连接两个项目的远程仓库

为避免对主分支造成影响，建议分别在[工程A]和[工程B] 中创建一个新分支进行开发。

#### 操作步骤：

### 创建分支 branchA
```bash

cd [工程A]
git checkout -b branchA

cd [工程B]
git checkout -b branchB
```
### 将[工程B] 添加为[工程A] 的 **远程仓库**

```bash
# 打开终端，进入[工程A] 的目录
cd [工程A]

# 将[工程A]添加为远程仓库（命名为upstream）
git remote add upstream [工程B的git地址]

```

### 2. 拉取[工程B] 的最新代码到[工程A]

在提交修改之前，建议先拉取[工程A] 的最新代码，确保本地代码是最新的。

#### 操作步骤：

```bash

cd [工程A]
# 拉取[工程B]的最新代码并切换到主分支（假设主分支为master）
git fetch upstream 

# 切换到分支(其他分支,如果名称重复需要修改名称)
git checkout -b branchB upstream/branchB

# 获取[工程B]最新代码
git pull
```
### 合并代码 (--allow-unrelated-histories 因两个项目没有管理历史，强制合并)
```bash
# 将[工程B] 代码合并到[工程A] （branchB=> branchA）
cd [工程A]
git checkout branchA

git merge branchB --allow-unrelated-histories

```
### 解决冲突

如果两个项目中有相同的文件或目录，可能会产生冲突。Git 会标记出冲突的地方，你需要手动解决这些冲突。
1、打开冲突文件，找到冲突标记（<<<<<<<, =======, >>>>>>>）。
2、根据需要编辑文件，保留你想要的更改。
3、保存文件并标记为已解决。
```bash
# 添加 冲突解决的文件
git add <conflicted-file>
```
### 完成合并

```bash
# 添加 冲突解决的文件
git commit
```

#### 全部操作步骤：

```bash
cd [工程A]
git checkout -b branchA

cd [工程B]
git checkout -b branchB

cd [工程A]
git remote add upstream [工程B的git地址]
git fetch upstream
git checkout -b branchB upstream/branchB

git checkout branchA
git merge branchB --allow-unrelated-histories
# 解决冲突
git add <conflicted-file>
git commit -m "Merge ProjectB into ProjectA"
git push 
git remote remove upstream
```

### 7. 关于处理冲突与合并

如果有代码冲突，团队成员需要通过协作解决冲突。项目管理员或负责人负责审查并最终将[工程B] 的代码合并到[工程A]。

#### 操作步骤：

1. [工程A] 所有者审核 Pull Request。
2. 解决所有冲突后，批准并合并到主干的请求。
3. 确保代码质量和功能稳定性。

---

## 注意事项

- 在编写 commit 提交说明时，请保持清晰和简洁，便于他人理解修改内容。
- 如果团队有其他贡献规范（如分支命名规则等），请参考项目文档或开发指南。

---

希望这份文档能帮助你顺利将[工程B] 的代码合并到[工程A]！如果有任何疑问，请随时联系团队负责人。

