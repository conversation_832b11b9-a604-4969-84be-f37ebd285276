package com.jkr.runner;

import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.idgen.YitIdHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

/**
 * <AUTHOR>
 * @site
 * @company
 * @create 2025年4月15日11:39:33
 */
@Slf4j
//@Component  //被 spring 容器管理
//@Order(0)   //如果多个自定义的 ApplicationRunner  ，用来标明执行的顺序
@Deprecated // 弃用，使用 WorkerIdHandler和YitApplicationRunner 代替，在 WorkerIdHandler 中配置
public class IdGeneratorConfig implements ApplicationRunner {
    /**
     * 机器ID
     */
    @Value("${com.jkr.snowflake.workerId:64}")
    private short workerId;

    @Override
    public void run(ApplicationArguments args) {
        // 创建 IdGeneratorOptions 对象，可在构造函数中输入 WorkerId：
        IdGeneratorOptions options = new IdGeneratorOptions(workerId);
        // options.WorkerIdBitLength = 6; // 默认值6，限定 WorkerId 最大值为2^6-1，即默认最多支持64个节点。
        // options.SeqBitLength = 6; // 默认值6，限制每毫秒生成的ID个数。若生成速度超过5万个/秒，建议加大 SeqBitLength 到 10。
        // options.BaseTime = 1582136402000L; // 如果要兼容老系统的雪花算法，此处应设置为老系统的BaseTime。
        // ...... 其它参数参考 IdGeneratorOptions 定义。
        // 保存参数（务必调用，否则参数设置不生效）：
        YitIdHelper.setIdGenerator(options);
        // 以上过程只需全局一次，且应在生成ID之前完成。`
        log.info("雪花id生成器设置完成");
    }
}
