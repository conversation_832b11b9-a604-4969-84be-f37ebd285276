{
  "serverUrl": "http://localhost:8080",
  // 服务器地址，非必须
  "isStrict": false,
  // 是否开启严格模式
  "allInOne": false,
  // 是否将文档合并到一个文件中，一般推荐为true
  //"outPath": "D:/docs/server-modules", // 指定文档的输出路径
  "outPath": "../smart",
  //如果想要调试则需要把调试文件生成到项目中
  "coverOld": true,
  // 是否覆盖旧的文件，主要用于markdown文件覆盖
  "packageFilters": "",
  // controller包过滤，多个包用英文逗号隔开
  "style": "xt256",
  // 基于highlight.js的代码高亮设置
  "createDebugPage": true,
  // 是否创建一个可调试接口的文档页面
  // 其他配置...
  "projectName": "jkr-framework"
  //  说明
  //  outPath：文档生成的地址
  // 如果需要http://localhost:8080/doc/api.html，直接访问需要指定项目资源目录，保证生成的文件在项目资源内
  //  allInOne：true/false  不同的值生成的调试文件名不一样
  //  操作步骤
  //  1、生成文档：mvn complie
  //  2、重启项目
}