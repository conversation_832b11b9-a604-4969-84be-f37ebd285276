# nacos 配置
#nacos:
#  config:
#    bootstrap:
#      enable: true
#    server-addr: 127.0.0.1:8848
#    namespace: ac696703-4e0e-40cc-852b-2899abfc17c0
#    group: DEFAULT_GROUP
#    data-ids: application,application-local,application-dev
#    remote-first: true
#    type: yaml
#    auto-refresh: true
#    max-retry: 10
#    config-retry-time: 2333
#    config-long-poll-timeout: 46000
#    enable-remote-sync-config: true
#    username: nacos
#    password: nacos

# 项目相关配置
jkr:
  # 名称
  name: jkr
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /certServer
  undertow:
    threads:
      io: 16 # 设置IO线程数: 默认为CPU核心数
      worker: 16 # 设置工作线程数: 默认为2倍CPU核心数
    buffer-size: 1024 # 设置每个请求的最大缓冲区大小，单位为字节,不要设置过大
    direct-buffers: true # 是否分配直接内存 (NIO直接分配)
#  tomcat:
#    # tomcat的URI编码
#    uri-encoding: UTF-8
#    # 连接数满后的排队数，默认为100
#    accept-count: 1000
#    threads:
#      # tomcat最大线程数，默认为200
#      max: 800
#      # Tomcat启动初始化的线程数，默认值10
#      min-spare: 100

# 日志配置
logging:
  level:
    com.jkr: debug
    org.springframework: warn
    org.apache: warn
    com.ulisesbocchio: warn
    org.quartz: warn
    com.alibaba: warn
    io.undertow: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  #  autoconfigure:
  #    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  data:
    redis:
      # 地址
      host: ***************  # 127.0.0.1 #
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 93
      # 密码
      password: JrkxS@rs01#@!.  # sxkj0818web #
      # 连接超时时间
      timeout: 60s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  # RabbitMQ 配置项，对应 RabbitProperties 配置类
  rabbitmq:
    host: 127.0.0.1 # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: guest # RabbitMQ 服务的账号
    password: guest # RabbitMQ 服务的密码
  # flyway 配置
  flyway:
    enabled: false  # 是否打开flyway管理
    clean-disabled: true  # 禁止清理数据表
    baseline-on-migrate: true # 如果数据库不是空表，需要设置成true，否则启动报错
    baseline-version: 1.0.0 # 与baseline-on-migrate搭配使用，数据库版本
    locations:
      - classpath:db/migration  # 数据库的位置
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
  # 移动端令牌有效期（单位分钟）
  mobileExpireTime: 25200

# Mybatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    type-aliases-package: com.jkr.project.**.**.domain
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  mapper-locations: classpath*:mybatis/**/*Mapper.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 代码生成
gen:
  # 作者
  author: jkr
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.jkr.project.argi
  # 自动去除表前缀，默认是true
  autoRemovePre: true
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_,bas_
  # 是否允许生成文件覆盖到本地（自定义路径），默认不允许
  allowOverwrite: false

# jasypt 配置
# 启动方式一：把秘钥当做程序启动时环境变量
# java -Djasypt.encryptor.password=秘钥 -jar xxx.jar
# 启动方式二：把秘钥当做程序启动时的命令行参数
# java -jar xxx.jar --jasypt.encryptor.password=秘钥
# 启动方式三：设置系统环境变量
# 1.设置系统环境变量 ENCRYPT = 密钥
# 2.jasypt.encryptor.password= ${ENCRYPT}
jasypt:
  encryptor:
    # jasypt盐值，不要在生产环境设置明文参数
    password:
    #指定解密算法(默认)
    algorithm: PBEWithMD5AndDES
    # 3.0.0版本及以上版本需要添加如下配置
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

#  雪花算法配置
yitter:
  # worker id 过期时间，单位秒
  ttl: 86400
  # 心跳时间，单位秒
  heartbeat: 86300
