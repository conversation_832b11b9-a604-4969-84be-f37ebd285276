# Getting Started
    数据库版本管理工具
### 依赖
```xml
    <!-- flyway高版本依赖 mysql需要单独引用插件-->
    <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>9.22.2</version>
    </dependency>
    <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-mysql</artifactId>
        <version>9.22.2</version>
    </dependency>
```
### 配置
```yaml
    spring:
        # flyway 配置  
        flyway:
          enabled: true  # 是否打开flyway管理
          clean-disabled: true  # 禁止清理数据表
          baseline-on-migrate: true # 如果数据库不是空表，需要设置成true，否则启动报错
          baseline-version: 1.0.0 # 与baseline-on-migrate搭配使用，数据库版本
          locations:
            - classpath:db/sql  # 数据库的位置
            - classpath:db/migration  # 数据库的位置
```

### 说明
* 按照yml文件中flyway.locations的配置在resources下创建文件夹，用来存放SQL文件
* SQL文件命名格式：V[version]__[name].sql
  * V：大写，固定
  * version：版本号，须与yml中flyway.baseline-version配置格式相同
  * __：两个英文下划线
  * name：简单说明SQL用途，例如：数据库初始化 or 修改xxx表字段。。。。



