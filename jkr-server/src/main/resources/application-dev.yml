# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    dynamic:
      druid:
        # 初始连接数
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置连接超时时间
        connectTimeout: 30000
        # 配置网络超时时间
        socketTimeout: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        webStatFilter:
          enabled: true
        statViewServlet:
          enabled: true
          # 设置白名单，不填则允许所有访问
          allow:
          url-pattern: /druid/*
          # 控制台管理用户名和密码
          login-username: jkr
          login-password: 123456
        filter:
          stat:
            enabled: true
            # 慢SQL记录
            log-slow-sql: true
            slow-sql-millis: 1000
            merge-sql: true
          wall:
            config:
              multi-statement-allow: true
      primary: master
      strict: false
      datasource:
        master:
          url: ****************************************************************************************************************************************************************
          username: rdgroup03
          password: JrkxS@Msql#6awsE
          # jasypt加密示例
          #username: ENC(BBqp7BFFld3GO0ACoavxVCLJaeoPnSE6)
          #password: ENC(A8iqCpQ3uSawMx8W1uSV7RcY0+T16HjF)
        slave:
          url: ****************************************************************************************************************************************************************
          username: rdgroup03
          password: JrkxS@Msql#6awsE
          # jasypt加密示例
          #username: ENC(BBqp7BFFld3GO0ACoavxVCLJaeoPnSE6)
          #password: ENC(A8iqCpQ3uSawMx8W1uSV7RcY0+T16HjF)

# 自定义配置
com:
  jkr:
    minio:
      endPoint: https://rdcenter1.jlsenxiang.com/ # minio服务地址
      bucketName: dev  #文件库名称
      accessKey: admin # 用户名
      secretKey: JrkxS@mn01#@!. # 密码 密码
    encrypt:
      # 是否启用加解密
      enabled: false # value is true or false

#七牛云
qiniu:
  accessKey: 988cBjoifTDqC-TEdNH1CLWhBKUYHs4GMcqjplm-
  secretKey: l1KowQsQiFcbc9-LIrCj9L20riAl4Ox6rgOFp3cq
  bucket: sxkj-dev
  domain: https://devqn.jikeruan.com/

web:
  maxUploadSize: 5242880000
#快检接口开关
inspectionEnabled: false

inspection:
  url: http://192.168.189.146:8088/inspection
  #样品编码查询接口
  queryBySampleNoUrl: /rest/test/findRecordListByNo
  #根据样品编号查询合格最新结果
  queryQualifiedResultBySampleUrl: /rest/test/findRecordListByNoLatest
  #检测样品发送接口
  saveSampleUrl: /rest/task/task/create/batch
  #检测样品获取增量数据接口
  findRecordListByDate: /rest/test/findRecordListByDate
  #快检系统身份标识
  AccessKey: hgz
  #快检秘钥
  SecretKey: 4028e4d455487d86015548ec043d02d5
  #快检获取数据接口定时配置
  job: 0 */30 * * * ?

product:
  reBuyProduct:
    url: http://172.16.183.201:9191/system/goods/getGoodsListByPhone

source:
  checkPhoneNum:
    url: http://172.16.183.201:9191/certificate/getFlagByCertificate

#区块连开关
blockChainEnabled: false
#区块链接口地址
blockChain:
  baseUrl: http://192.168.189.141:20006
  insertUrl: /fabric/api/v1/insert
  queryByIdUrl: /fabric/api/v1/getQueryResultByTxId
  checkByIdUrl: /fabric/api/v1/checkByTxId

# 通用延时发布定时任务配置
delayed:
  publish:
    enabled: true # 是否启用延时发布定时任务

serialNumberLength: 7

dataCentrePushEnabled: false
#appid
dataCentre:
  appId: bt9333503358861312
  #秘钥
  secret: 80bde89d53121b64a47c80487cb14273
  #推送区域编码(市级)
  pushAreaCode: 2206
  #推送服务url
  gateway: http://192.168.183.126:8090/traceAnalysisServer
  #推送追溯接口
  traceApi: /open/gateway/tracechain
  #数据处理错误回调
  notifyUrl: http://192.168.183.126:8080/
