<template>
    <!-- 添加或修改${functionName}对话框 -->
    <Dialog :title="dialogTitle" v-model="dialogVisible">
        <el-form ref="formRef" :model="form" :rules="formRules" label-width="80px">
            #foreach($column in $columns)
                #set($field=$column.javaField)
                #if($column.insert && !$column.pk)
                    #if(($column.usableColumn) || (!$column.superColumn))
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        #set($dictType=$column.dictType)
                        #if($column.htmlType == "input")
                            <el-form-item label="${comment}" prop="${field}">
                                <el-input v-model="form.${field}" placeholder="请输入${comment}" />
                            </el-form-item>
                        #elseif($column.htmlType == "imageUpload")
                            <el-form-item label="${comment}" prop="${field}">
                                <image-upload v-model="form.${field}"/>
                            </el-form-item>
                        #elseif($column.htmlType == "fileUpload")
                            <el-form-item label="${comment}" prop="${field}">
                                <file-upload v-model="form.${field}"/>
                            </el-form-item>
                        #elseif($column.htmlType == "editor")
                            <el-form-item label="${comment}">
                                <editor v-model="form.${field}" :min-height="192"/>
                            </el-form-item>
                        #elseif($column.htmlType == "select" && "" != $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-select v-model="form.${field}" placeholder="请选择${comment}">
                                    <el-option
                                            v-for="dict in ${dictType}"
                                            :key="dict.value"
                                            :label="dict.label"
                                        #if($column.javaType == "Integer" || $column.javaType == "Long")
                                            :value="parseInt(dict.value)"
                                        #else
                                            :value="dict.value"
                                        #end
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        #elseif($column.htmlType == "select" && $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-select v-model="form.${field}" placeholder="请选择${comment}">
                                    <el-option label="请选择字典生成" value="" />
                                </el-select>
                            </el-form-item>
                        #elseif($column.htmlType == "checkbox" && "" != $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-checkbox-group v-model="form.${field}">
                                    <el-checkbox
                                            v-for="dict in ${dictType}"
                                            :key="dict.value"
                                            :label="dict.value">
                                        {{dict.label}}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        #elseif($column.htmlType == "checkbox" && $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-checkbox-group v-model="form.${field}">
                                    <el-checkbox>请选择字典生成</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        #elseif($column.htmlType == "radio" && "" != $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-radio-group v-model="form.${field}">
                                    <el-radio
                                            v-for="dict in ${dictType}"
                                            :key="dict.value"
                                        #if($column.javaType == "Integer" || $column.javaType == "Long")
                                            :label="parseInt(dict.value)"
                                        #else
                                            :label="dict.value"
                                        #end
                                    >{{dict.label}}</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        #elseif($column.htmlType == "radio" && $dictType)
                            <el-form-item label="${comment}" prop="${field}">
                                <el-radio-group v-model="form.${field}">
                                    <el-radio label="1">请选择字典生成</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        #elseif($column.htmlType == "datetime")
                            <el-form-item label="${comment}" prop="${field}">
                                <el-date-picker clearable
                                                v-model="form.${field}"
                                                type="date"
                                                value-format="YYYY-MM-DD"
                                                placeholder="请选择${comment}">
                                </el-date-picker>
                            </el-form-item>
                        #elseif($column.htmlType == "textarea")
                            <el-form-item label="${comment}" prop="${field}">
                                <el-input v-model="form.${field}" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                        #end
                    #end
                #end
            #end
            #if($table.sub)
                <el-divider content-position="center">${subTable.functionName}信息</el-divider>
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" icon="Plus" @click="handleAdd${subClassName}">添加</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button type="danger" icon="Delete" @click="handleDelete${subClassName}">删除</el-button>
                    </el-col>
                </el-row>
                <el-table :data="${subclassName}List" :row-class-name="row${subClassName}Index" @selection-change="handle${subClassName}SelectionChange" ref="${subclassName}">
                    <el-table-column type="selection" width="50" align="center" />
                    <el-table-column label="序号" align="center" prop="index" width="50"/>
                    #foreach($column in $subTable.columns)
                        #set($javaField=$column.javaField)
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        #if($column.pk || $javaField == ${subTableFkclassName})
                        #elseif($column.list && $column.htmlType == "input")
                            <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template #default="scope">
                                    <el-input v-model="scope.row.$javaField" placeholder="请输入$comment" />
                                </template>
                            </el-table-column>
                        #elseif($column.list && $column.htmlType == "datetime")
                            <el-table-column label="$comment" prop="${javaField}" width="240">
                                <template #default="scope">
                                    <el-date-picker clearable
                                                    v-model="scope.row.$javaField"
                                                    type="date"
                                                    value-format="YYYY-MM-DD"
                                                    placeholder="请选择$comment">
                                    </el-date-picker>
                                </template>
                            </el-table-column>
                        #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") && "" != $column.dictType)
                            <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template #default="scope">
                                    <el-select v-model="scope.row.$javaField" placeholder="请选择$comment">
                                        <el-option
                                                v-for="dict in $column.dictType"
                                                :key="dict.value"
                                                :label="dict.label"
                                                :value="dict.value"
                                        ></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                        #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") && "" == $column.dictType)
                            <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template #default="scope">
                                    <el-select v-model="scope.row.$javaField" placeholder="请选择$comment">
                                        <el-option label="请选择字典生成" value="" />
                                    </el-select>
                                </template>
                            </el-table-column>
                        #end
                    #end
                </el-table>
            #end
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" :disabled="formLoading" @click="submitForm">确 定</el-button>
                <el-button @click="dialogVisible=false">取 消</el-button>
            </div>
        </template>
    </Dialog>
</template>

<script setup name="${BusinessName}">
    import Dialog from "@/components/Dialog/index.vue";
    import { get${BusinessName}, add${BusinessName}, update${BusinessName} } from "@/api/${moduleName}/${businessName}";

    const { proxy } = getCurrentInstance();
    #if(${dicts} != '')
        #set($dictsNoSymbol=$dicts.replace("'", ""))
    const { ${dictsNoSymbol} } = proxy.useDict(${dicts});
    #end

    const emit = defineEmits(['success'])
    const form = ref({})
    const dialogTitle = ref('')
    const dialogVisible = ref(false)
    const formLoading = ref(false)
    const formType = ref('')
    const formRef = ref()
    const open = async (type,id)=>{
      resetForm()
      dialogVisible.value = true
      dialogTitle.value = formType
      formType.value = type
      if(id){
        formLoading.value = true
        try {
          const data = await get${BusinessName}(id)
          form.value = data.data
        }finally
        {
          formLoading.value = false
        }
      }
    }
    defineExpose({ open })
    const formRules = reactive({
        #foreach ($column in $columns)
            #if($column.required)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
                    $column.javaField: [
                { required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select" || $column.htmlType == "radio")"change"#else"blur"#end }
              ]#if($foreach.count != $columns.size()),#end
            #end
        #end
    })


    /** 提交表单 */
    const submitForm = async () => {
      // 校验表单
      if (!formRef) return
      const valid = await formRef.value.validate()
      if (!valid) return

      try {
        if (formType.value == '编辑') {
          await update${BusinessName}(form.value)
          proxy.$modal.msgSuccess("修改成功");
        } else {
          await add${BusinessName}(form.value)
          proxy.$modal.msgSuccess("添加成功");
        }
        dialogVisible.value = false
        emit('success')
      } finally {
        formLoading.value = false
      }
    }

    /** 表单重置 */
    function resetForm() {
      form.value = {
          #foreach ($column in $columns)
              #if($column.htmlType == "checkbox")
                      $column.javaField: []#if($foreach.count != $columns.size()),#end
              #else
                      $column.javaField: null#if($foreach.count != $columns.size()),#end
              #end
          #end
      };
        #if($table.sub)
                ${subclassName}List.value = [];
        #end
      proxy.resetForm("formRef");
    }
</script>
<style scoped lang="scss">

</style>