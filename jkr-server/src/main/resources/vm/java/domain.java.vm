package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jkr.framework.aspectj.lang.annotation.Excel;
#if($table.crud || $table.sub)
import com.jkr.framework.web.domain.BaseModel;
#elseif($table.tree)
import com.jkr.framework.web.domain.TreeEntity;
#end
import java.util.List;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
    #if($table.crud || $table.sub)
        #set($Entity="BaseModel")
    #elseif($table.tree)
        #set($Entity="TreeEntity")
    #end
        @Data
        @EqualsAndHashCode(callSuper = true)
        @TableName("${tableName}")
		public class ${ClassName} extends ${Entity}
		{
		private static final long serialVersionUID = 1L;

    #foreach ($column in $columns)
        #if(!$table.isSuperColumn($column.javaField))
				/** $column.columnComment */
            #if($column.list)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
                #if($parentheseIndex != -1)
				@Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
                #elseif($column.javaType == 'Date')
				@JsonFormat(pattern = "yyyy-MM-dd")
				@Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
                #else
				@Excel(name = "${comment}")
                #end
            #end
		private $column.javaType $column.javaField;

        #end
    #end
            /** 主键集合 */
            private List<Long> ids;
    #if($table.sub)
			/** $table.subTable.functionName信息 */
			private List<${subClassName}> ${subclassName}List;

    #end
}
