package ${packageName}.service;

import java.util.List;

import ${packageName}.domain .${ClassName};

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service {
	/**
	 * 查询${functionName}
	 *
	 * @param ${pkColumn.javaField} ${functionName}主键
	 * @return ${functionName}
	 */
	public ${ClassName} select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});

	/**
	 * 查询${functionName}列表
	 *
	 * @param ${className} ${functionName}
	 * @return ${functionName}集合
	 */
	public List<${ClassName}> select${ClassName}List(${ClassName} ${className});

	/**
	 * 新增${functionName}
	 *
	 * @param ${className} ${functionName}
	 * @return 结果
	 */
	public int insert${ClassName}(${ClassName} ${className});

	/**
	 * 修改${functionName}
	 *
	 * @param ${className} ${functionName}
	 * @return 结果
	 */
	public int update${ClassName}(${ClassName} ${className});

	/**
	 * 批量删除${functionName}
	 *
	 * @param ids 需要删除的${functionName}主键集合
	 * @return 结果
	 */
	public int delete${ClassName}ByIds(List<Long> ids);

	/**
	 * 删除${functionName}信息
	 *
	 * @param ${pkColumn.javaField} ${functionName}主键
	 * @return 结果
	 */
	public int delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField});

}
