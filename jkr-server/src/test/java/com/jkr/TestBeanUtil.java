package com.jkr;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * Bean随机赋值工具类 TestBeanUtil
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-19 16:54
 */
@Slf4j
public class TestBeanUtil {
	private static final SecureRandom RANDOM = new SecureRandom();

	public static Object setValues(Class clazz) {
		try {
			return setAllFieldValues(clazz.newInstance());
		} catch (Exception e) {
		}
		return null;
	}

	public static Object setValues(Object object) {
		try {
			return setAllFieldValues(object);
		} catch (Exception e) {
		}
		return null;
	}

	/**
	 * 为Bean的实例赋值
	 *
	 * @param obj 待赋值的对象实例
	 * @return 赋值后的对象实例
	 */
	private static Object setAllFieldValues(Object obj) throws Exception {
		Class<?> clazz = obj.getClass();
		// 遍历类层级结构，包括所有父类
		while (clazz != null) {
			Field[] fields = clazz.getDeclaredFields();

			for (Field field : fields) {
				String fieldName = field.getName();
				// 排除特殊字段，此处待优化
				if ("createTime".equals(fieldName) || "updateTime".equals(fieldName)) {
					continue;
				}
				field.setAccessible(true);
				Class<?> fieldType = field.getType();
				// 根据字段类型赋值
				if (fieldType == int.class || fieldType == Integer.class) {
					// 随机整数，范围 0-99
					field.set(obj, RANDOM.nextInt(100));
				} else if (fieldType == double.class || fieldType == Double.class) {
					// 随机小数，范围 0-99.9999
					field.set(obj, RANDOM.nextDouble() * 100);
				} else if (fieldType == boolean.class || fieldType == Boolean.class) {
					// 随机布尔值
					field.set(obj, RANDOM.nextBoolean());
				} else if (fieldType == String.class) {
					// 随机字符串，长度为10
					field.set(obj, generateRandomString(10));
				} else if (fieldType == Date.class) {
					// 随机日期
					field.set(obj, generateRandomDate());
				} else if (fieldType == BigDecimal.class) {
					field.set(obj, BigDecimal.TEN);
				} else if (fieldType == BigInteger.class) {
					field.set(obj, BigInteger.TEN);
				}
			}
			// 获取父类
			clazz = clazz.getSuperclass();
		}
		return obj;
	}

	/**
	 * 随机生成指定长度的字符串（包含数字和字母）
	 *
	 * @param length 字符串长度
	 */
	private static String generateRandomString(int length) {
		String characters = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
		StringBuilder sb = new StringBuilder(length);
		for (int i = 0; i < length; i++) {
			sb.append(characters.charAt(RANDOM.nextInt(characters.length())));
		}
		return sb.toString();
	}

	/**
	 * 随机生成一个日期
	 */
	private static Date generateRandomDate() {
		LocalDateTime now = LocalDateTime.now().minusDays(RANDOM.nextInt(100));
		return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
	}
}
