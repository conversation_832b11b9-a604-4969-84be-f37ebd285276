package com.jkr.project.sso.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.system.domain.SysUserClient;
import com.jkr.project.system.service.ISysUserClientService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class SysUserClientControllerTest extends BaseControllerTest  {
    private String root = "/system/user/client";
    @Resource
    private ISysUserClientService SysUserClientService;

    @Test
    void list(){
        SysUserClient sysUserClient = (SysUserClient) TestBeanUtil.setValues(SysUserClient.class);
        postRequest(root + "/list", sysUserClient, null);
    }
    @Test
    void add(){
        SysUserClient sysUserClient = (SysUserClient) TestBeanUtil.setValues(SysUserClient.class);
        sysUserClient.setClientId(101L);
        sysUserClient.setUserId(3L);
        postRequest(root + "/add", sysUserClient, null);
    }
    @Test
    void info(){
        postRequest(root + "/info/{id}", null, null,26L);
    }
    @Test
    void edit(){
        SysUserClient sysUserClient = SysUserClientService.selectSysUserClientById(26L);
        sysUserClient.setClientLoginName("测试用户");
        postRequest(root + "/edit", sysUserClient, null);
    }

    @Test
    void delete(){
        SysUserClient sysUserClient = (SysUserClient) TestBeanUtil.setValues(SysUserClient.class);
        postRequest(root + "/remove/{clientId}", sysUserClient, null,26L);
    }

    @Test
    void saveOrUpdate(){
        SysUserClient sysUserClient = (SysUserClient) TestBeanUtil.setValues(SysUserClient.class);
        sysUserClient.setClientId(5L);
        sysUserClient.setUserId(128L);
        sysUserClient.setAuth(false);
        postRequest(root + "/saveOrUpdate", sysUserClient, null);
    }
}
