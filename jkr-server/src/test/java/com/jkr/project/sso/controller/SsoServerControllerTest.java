package com.jkr.project.sso.controller;

import com.jkr.BaseControllerTest;
import com.jkr.project.system.service.ISsoServerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class SsoServerControllerTest extends BaseControllerTest {
    private String root = "/sso";

    public static final String Authorization = "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjBjZjBmYTdiLTVjNzUtNDJkMC1hZjk3LTJkN2FhZDMzNDFkNSJ9.h4Dz0bM37w3fFtx9mv1R5KbnFEFvueHGWK_uNqCfCPAB7E4XkKmYHyaMgG2xi3KZ8FYsFa4IpTXHGwloxmkeTA";

    @Test
    void authAppByAppId(){
        Map<String,String> header = new HashMap<>();
        header.put("appId","ac13dcb4959ed504d7222d0aa12e407c");
        header.put("appSecret","f603e800060f1b98bb62a07b61aa6d4b");
        header.put("Authorization",Authorization);
        postRequestForSso(root + "/authAppByAppId", null, header,null);
    }

    @Test
    void getAppToken(){
        Map<String,String> map = new HashMap<>();
        map.put("loginName","admin");
        map.put("password","admin@135");
        map.put("appId","ac13dcb4959ed504d7222d0aa12e407c");
        map.put("appSecret","f603e800060f1b98bb62a07b61aa6d4b");
        postRequestForSso(root + "/getAppToken", null, null, map);
    }

    @Test
    void validToken(){
        Map<String,String> header = new HashMap<>();
        header.put("Authorization",Authorization);
        postRequestForSso(root + "/validToken", null, header,null);
    }
}
