package com.jkr.project.sso.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.system.domain.SysClient;
import com.jkr.project.system.service.ISysClientService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class SysClientControllerTest extends BaseControllerTest {
    private String root = "/system/client";

    @Resource
    private ISysClientService sysClientService;
    @Test
    void list(){
        SysClient sysClient = (SysClient) TestBeanUtil.setValues(SysClient.class);
        postRequest(root + "/list", sysClient, null);
    }
    @Test
    void add(){
        SysClient sysClient = (SysClient) TestBeanUtil.setValues(SysClient.class);
        sysClient.setCnName("测试类添加");
        sysClient.setEnName("test-add");
        sysClient.setRedirectPath("http://localhost:8080/");
        sysClient.setServerPath("http://localhost:8080/");
        sysClient.setAllowImgUrl("sys_client/微信图片_20241202193418_dev_20250206162753.jpg");
        sysClient.setUnAllowImgUrl("sys_client/微信图片_20241203184503_dev_20250206162756.jpg");
        postRequest(root + "/add", sysClient, null);
    }
    @Test
    void info(){
        postRequest(root + "/info/{id}", null, null,3L);
    }
    @Test
    void edit(){
        SysClient sysClient = sysClientService.selectSysClientById(3L);
        sysClient.setCnName("测试类修改");
        postRequest(root + "/edit", sysClient, null);
    }

    @Test
    void delete(){
        SysClient sysClient = (SysClient) TestBeanUtil.setValues(SysClient.class);
        postRequest(root + "/remove/{clientId}", sysClient, null,3L);
    }

}
