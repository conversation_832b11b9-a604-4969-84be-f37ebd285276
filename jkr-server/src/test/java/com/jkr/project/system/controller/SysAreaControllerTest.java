package com.jkr.project.system.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.system.domain.SysArea;
import com.jkr.project.system.service.ISysAreaService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SysAreaControllerTest extends BaseControllerTest {
	private String root = "/system/area";

	@Resource
	private ISysAreaService areaService;

	@Test
	void list() {
		postRequest(root + "/list", new SysArea(), null);
	}

	@Test
	void getInfo() {
		postRequest(root + "/info/{areaId}", null, null, "110000000000");
	}

	@Test
	void add() throws Exception {
		SysArea area = (SysArea) TestBeanUtil.setValues(SysArea.class);
		area.setAreaId("test1");
		area.setParentId("000000000000");
		area.setParentIds("0,000000000000");
		area.setName("测试省份1");
		area.setCode("500");
		area.setType("1");
		area.setLng("111");
		area.setLat("22");
		area.setDelFlag("0");
		postRequest(root + "/add", area, null);
	}

	@Test
	void edit() {
		SysArea area = areaService.selectSysAreaByAreaId("110000000000");
		area.setName("北京市-改");
		postRequest(root + "/edit", area, null);
	}

	@Test
	void remove() {
		postRequest(root + "/remove/{areaId}", null, null, "110000000000");
	}
	@Test
	void getSysAreaTreeData() {
		postRequest(root + "/getSysAreaTreeData", null, null);
	}
}
