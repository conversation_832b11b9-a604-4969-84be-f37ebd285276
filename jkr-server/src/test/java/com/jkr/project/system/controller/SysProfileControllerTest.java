package com.jkr.project.system.controller;

import com.jkr.BaseControllerTest;
import com.jkr.project.system.domain.vo.UpdatePwdVo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SysProfileControllerTest extends BaseControllerTest {
	private String root = "/system/user/profile";

	@Test
	void profile() {
		getRequest(root + "/info", null, null);
	}

	@Test
	void updateProfile() {
	}

	@Test
	void updatePwd() {
		// 测试时要记住并修改密码
		UpdatePwdVo vo = new UpdatePwdVo();
		vo.setOldPassword(loginPwd);
		vo.setNewPassword("admin#135");
		postRequest(root + "/updatePwd", vo, null);
	}

	@Test
	void avatar() {
	}
}