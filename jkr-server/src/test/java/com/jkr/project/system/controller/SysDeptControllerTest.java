package com.jkr.project.system.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.system.domain.SysDept;
import com.jkr.project.system.service.ISysDeptService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SysDeptControllerTest extends BaseControllerTest {
	private String root = "/system/dept";

	@Resource
	private ISysDeptService deptService;

	@Test
	void list() {
		postRequest(root + "/list", SysDept.class, null);
	}

	@Test
	void excludeChild() {
		postRequest(root + "/list/exclude/{deptId}", null, null, 111L);
	}

	@Test
	void getInfo() {
		postRequest(root + "/info/{deptId}", null, null, 111L);
	}

	@Test
	void add() throws Exception {
		SysDept dept = (SysDept) TestBeanUtil.setValues(SysDept.class);
		dept.setParentId(111L);
		dept.setEmail("<EMAIL>");
		dept.setStatus("0");
		dept.setChildren(null);
//		dept.setOrderNum(null);
//		dept.setPhone("dfjldjfdsjfdsjfldjflkdsjfkl");
//		dept.setDeptName("jklfdjfdjfddfjfdjfjkdlsjfdjfklfjdflksdjflkjdsfkljsdflkjsdfkljasldkfjsdlfjdslkjfslkdfjdlfjaljdfdaskddjflkjfd");
		postRequest(root + "/add", dept, null);
	}

	@Test
	void edit() {
		SysDept dept = deptService.selectDeptById(111L);
		dept.setEmail("<EMAIL>");
		postRequest(root + "/edit", dept, null);
	}

	@Test
	void remove() {
		postRequest(root + "/remove/{deptId}", null, null, 104L);
	}
}