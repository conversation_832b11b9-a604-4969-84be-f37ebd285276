package com.jkr.project.system.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.system.domain.SysFile;
import com.jkr.project.system.service.ISysFileService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class SysFileControllerTest extends BaseControllerTest {
	private String root = "/system/file";

	@Resource
	private ISysFileService sysFileService;

	@Test
	void list() {
		SysFile sysFile = (SysFile) TestBeanUtil.setValues(SysFile.class);
		postRequest(root + "/list", sysFile, null);
	}

	@Test
	void getInfo() {
		postRequest(root + "/info/{fileId}", null, null, "36");
	}

	@Test
	void add() throws Exception {
		SysFile file = (SysFile) TestBeanUtil.setValues(SysFile.class);
		//file.setFileId(37L);
		file.setTableId(37L);
		file.setTableName("sys_file");
		file.setFieldType("fieldType");
		file.setName("微信图片_20250107083046_dev_20250115162432.jpg");
		file.setRealName("微信图片_20250107083046.jpg");
		file.setUrl("sys_file/微信图片_20250107083046_dev_20250115162432.jpg");
		file.setType("image/jpeg");
		file.setSize(731756);
		file.setDelFlag(0);
		postRequest(root + "/add", file, null);
	}

	@Test
	void addList() throws Exception {
		SysFile file = (SysFile) TestBeanUtil.setValues(SysFile.class);
		//file.setFileId(37L);
		file.setTableId(37L);
		file.setTableName("sys_file");
		file.setFieldType("fieldType");
		file.setName("微信图片_20250107083046_dev_20250115162432.jpg");
		file.setRealName("微信图片_20250107083046.jpg");
		file.setUrl("sys_file/微信图片_20250107083046_dev_20250115162432.jpg");
		file.setType("image/jpeg");
		file.setSize(731756);
		file.setDelFlag(0);
		List<SysFile> fileList = new ArrayList<>();
		fileList.add(file);
		fileList.add(file);
		postRequest(root + "/addList", fileList, null);
	}

	@Test
	void remove() {
		postRequest(root + "/remove/{fileId}", null, null, "36");
	}
}

