package com.jkr.project.system.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.common.utils.sms.CaptchaUtils;
import com.jkr.project.system.domain.SysSms;
import com.jkr.project.system.service.ISysSmsService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
class SysSmsControllerTest extends BaseControllerTest {
	private String root = "/system/sms";

	@Resource
	private ISysSmsService sysSmsService;

	@Test
	void sendCode() {
		postRequest(root + "/send/code/{loginName}", null, null, "13844095921");
	}

}

