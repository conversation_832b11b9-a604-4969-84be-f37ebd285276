package com.jkr.project.monitor.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.monitor.domain.SysDocumentDir;
import com.jkr.project.monitor.service.SysDocumentDirService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

public class SysDocumentDirControllerTest extends BaseControllerTest {
    private String root = "/sys/documentdir";
    @Resource
    private SysDocumentDirService sysDocumentDirService;
    @Test
    void list() {
        SysDocumentDir sysDocumentDir = (SysDocumentDir) TestBeanUtil.setValues(SysDocumentDir.class);
        getRequest(root + "/list", sysDocumentDir, null);
    }
    @Test
    void add(){
        SysDocumentDir sysDocumentDir = (SysDocumentDir) TestBeanUtil.setValues(SysDocumentDir.class);
        sysDocumentDir.setDirName("测试类添加");
        postRequest(root + "/add", sysDocumentDir, null);
    }
    @Test
    void info(){
        postRequest(root + "/info/{dirId}", null, null,3L);
    }

    @Test
    void edit(){
        SysDocumentDir sysDocumentDir = sysDocumentDirService.selectSysDocumentDirById(3L);
        sysDocumentDir.setDirName("测试目录修改");
        sysDocumentDir.setParentId(0L);
        postRequest(root + "/edit", sysDocumentDir, null);
    }



    @Test
    void remove() {
        postRequest(root + "/remove/{dirId}", null, null, 123, 1234);
    }
}
