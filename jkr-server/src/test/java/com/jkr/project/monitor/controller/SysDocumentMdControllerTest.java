package com.jkr.project.monitor.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.monitor.domain.SysDocumentMd;
import com.jkr.project.monitor.service.SysDocumentMdService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

public class SysDocumentMdControllerTest extends BaseControllerTest {
    private String root = "/sys/documentmd";
    @Resource
    private SysDocumentMdService sysDocumentMdService;
    @Test
    void list() {
        SysDocumentMd sysDocumentMd = (SysDocumentMd) TestBeanUtil.setValues(SysDocumentMd.class);
        getRequest(root + "/list", sysDocumentMd, null);
    }
    @Test
    void add(){
        SysDocumentMd sysDocumentMd = (SysDocumentMd) TestBeanUtil.setValues(SysDocumentMd.class);
        sysDocumentMd.setTitle("测试类添加");
        postRequest(root + "/add", sysDocumentMd, null);
    }
    @Test
    void info(){
        postRequest(root + "/info/{mdId}", null, null,35L);
    }

    @Test
    void edit(){
        SysDocumentMd sysDocumentMd = sysDocumentMdService.selectSysDocumentMdById(35L);
        sysDocumentMd.setTitle("测试文档修改");
        sysDocumentMd.setDirName("测试文档修改");
        postRequest(root + "/edit", sysDocumentMd, null);
    }



    @Test
    void remove() {
        postRequest(root + "/remove/{mdId}", null, null, 123, 1234);
    }
}
