package com.jkr.project.monitor.controller;

import com.jkr.BaseControllerTest;
import com.jkr.TestBeanUtil;
import com.jkr.project.monitor.domain.SysOperLog;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SysOperlogControllerTest extends BaseControllerTest {
	private String root = "/monitor/operlog";

	@Test
	void list() {
		SysOperLog operLog = (SysOperLog) TestBeanUtil.setValues(SysOperLog.class);

		postRequest(root + "/list", operLog, null);
	}

	@Test
	void export() {
		SysOperLog operLog = (SysOperLog) TestBeanUtil.setValues(SysOperLog.class);
		operLog.setStatus(0);
		postRequest(root + "/export", operLog, null);
	}

	@Test
	void remove() {
		postRequest(root + "/remove/{operIds}", null, null, 123, 1234);
	}

	@Test
	void clean() {
		postRequest(root + "/clean", null, null);
	}
}