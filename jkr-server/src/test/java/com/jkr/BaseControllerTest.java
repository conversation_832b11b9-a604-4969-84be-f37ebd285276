package com.jkr;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.jkr.common.constant.Constants;
import com.jkr.framework.security.LoginBody;
import com.jkr.framework.web.domain.AjaxResult;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.nio.charset.Charset;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 测试基类，主要提供了默认登录流程，以及解析返回结果的工具方法
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-14 10:25
 */
@Rollback
@Transactional
@AutoConfigureMockMvc
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = Application.class)
public class BaseControllerTest {
	protected Logger log = LoggerFactory.getLogger(BaseControllerTest.class);
	@Resource
	protected MockMvc mockMvc;
	@Resource
	protected ObjectMapper objectMapper;

	protected MvcResult mvcResult;
	protected String tokenName = "Authorization";
	protected String loginName = "admin";
	protected String loginPwd = "admin@135";
	protected Long timestamp = System.currentTimeMillis();
	protected String token = "";
	/*
		原始密码admin123的加密结果
	    $2a$10$FHYh56MlhxAISnuFeNoKkOglG.3KzDYnbKVdt.JG69DgBQq9bYFPm
	 */

	/**
	 * 模拟登录流程，获取token
	 */
	@BeforeAll
	public void beforeAll() throws Exception {
		LoginBody loginBody = new LoginBody();
		loginBody.setUsername(loginName);
		loginBody.setPassword(loginPwd);
		loginBody.setUuid("85514eb9e3c14360830b44c440847477");
		MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post("/login")
						.contentType(MediaType.APPLICATION_JSON)
						.content(objectMapper.writeValueAsString(loginBody))
						.accept(MediaType.APPLICATION_JSON))
//				.andExpect(MockMvcResultMatchers.status().isOk())
//				.andDo(MockMvcResultHandlers.print())
				.andReturn();
		String content = result.getResponse().getContentAsString();
		Map json = objectMapper.readValue(content, Map.class);
		int code = (int) json.get("code");
		if (code == 200 || code == 205) {
			token = Constants.TOKEN_PREFIX + json.get("msg");
		}
	}

	/**
	 * post请求
	 *
	 * @param url    请求地址
	 * @param bean   RequestBody请求参数
	 * @param map    RequestParam请求参数
	 * @param params PathVariable请求参数
	 */
	protected void postRequest(String url, Object bean, Map<String, String> map, Object... params) {
		try {
			Assert.isTrue(token != null, "登录出现异常");
			MockHttpServletRequestBuilder builder = post(url, params == null || params.length == 0 ? new Object[]{""} : params)
					.header(tokenName, token)
					.header("timestamp", timestamp + "")
					.header("user-agent", "anything")
					.contentType(MediaType.APPLICATION_JSON)
					.accept(MediaType.APPLICATION_JSON);
			if (bean != null) {
				builder.content(objectMapper.writeValueAsString(bean));
			}
			if (map != null && !map.isEmpty()) {
				for (Map.Entry<String, String> me : map.entrySet()) {
					builder.param(me.getKey(), me.getValue());
				}
			}
			mvcResult = mockMvc.perform(builder)
					.andExpectAll(status().isOk())
					.andDo(print())
					.andReturn();
			assertResult(mvcResult);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * get请求
	 *
	 * @param url    请求地址  Controller RequestMapping + Method RequestMapping
	 * @param bean   对象请求参数类型
	 * @param params 请求参数
	 */
	protected void getRequest(String url, Object bean, Map<String, String> map, Object... params) {
		Assert.isTrue(token != null, "登录出现异常");
		try {
			MockHttpServletRequestBuilder builder = get(url, params == null || params.length == 0 ? new Object[]{""} : params)
					.header(tokenName, token)
					.header("timestamp", timestamp + "")
					.header("user-agent", "anything")
					.contentType(MediaType.APPLICATION_JSON)
					.accept(MediaType.APPLICATION_JSON);
			if (bean != null) {
				String content = objectMapper.writeValueAsString(bean);
				builder.content(content);
			}
			if (map != null && !map.isEmpty()) {
				for (Map.Entry<String, String> me : map.entrySet()) {
					builder.param(me.getKey(), me.getValue());
				}
			}
			mvcResult = mockMvc.perform(builder)
					.andExpectAll(status().isOk())
					.andDo(print())
					.andReturn();
			assertResult(mvcResult);
		} catch (Exception e) {
			log.error("断言测试结果失败");
		}
	}

	/**
	 * post请求
	 *
	 * @param url    请求地址
	 * @param bean   RequestBody请求参数
	 * @param map    RequestParam请求参数
	 * @param params PathVariable请求参数
	 */
	protected void postRequestForSso(String url, Object bean, Map<String, String> header, Map<String, String> map, Object... params) {
		try {
			Assert.isTrue(token != null, "登录出现异常");
			MockHttpServletRequestBuilder builder = post(url, params == null || params.length == 0 ? new Object[]{""} : params)
					.header("timestamp", timestamp + "")
					.header("user-agent", "anything")
					.contentType(MediaType.APPLICATION_JSON)
					.accept(MediaType.APPLICATION_JSON);
			if (bean != null) {
				builder.content(objectMapper.writeValueAsString(bean));
			}
			if (header != null && !header.isEmpty()) {
				for (Map.Entry<String, String> me : header.entrySet()) {
					builder.header(me.getKey(), me.getValue());
				}
			}
			if (map != null && !map.isEmpty()) {
				for (Map.Entry<String, String> me : map.entrySet()) {
					builder.param(me.getKey(), me.getValue());
				}
			}
			mvcResult = mockMvc.perform(builder)
					.andExpectAll(status().isOk())
					.andDo(print())
					.andReturn();
			assertResult(mvcResult);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 断言测试结果
	 *
	 * @param result 测试返回结果
	 */
	private void assertResult(MvcResult result) throws Exception {
		AjaxResult responseResult = parseResponse(result);
		assert "200".equals(responseResult.get("code").toString());
	}

	/**
	 * 解析响应结果
	 *
	 * @param result 请求响应结果
	 * @return CommonResult
	 */
	private AjaxResult parseResponse(MvcResult result) throws Exception {
		try {
			String content = result.getResponse().getContentAsString(Charset.defaultCharset());
			if (content.isEmpty()) {
				return AjaxResult.error(1, "响应内容为空");
			}
			return objectMapper.readValue(content, AjaxResult.class);
		} catch (Exception e) {
			return AjaxResult.error(1, e.getMessage());
		}
	}

}
